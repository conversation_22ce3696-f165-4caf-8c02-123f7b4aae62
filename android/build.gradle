buildscript {
    repositories {
        google()
        mavenCentral()
        // Add other repositories if needed
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.11.1'
        // Add other classpath dependencies here
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}


tasks.register("clean", Delete) {
    delete rootProject.buildDir
}


