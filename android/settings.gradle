pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }
    settings.ext.flutterSdkPath = flutterSdkPath()

    includeBuild("${settings.ext.flutterSdkPath}/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version '8.11.1' apply false // 7.3.0
    // START: FlutterFire Configuration
    id "com.google.gms.google-services" version "4.3.15" apply false // 4.3.15
    // END: FlutterFire Configuration
    id "org.jetbrains.kotlin.android" version "2.1.0" apply false  // 1.7.10 => 1.9.0 => 2.0.0
}

include ":app"
