enum PortalType {
  admin('ADMIN'),
  app('APP');

  const PortalType(this.value);
  final String value;
}

enum ClubType {
  standing('STANDING'),
  impromptu('IMPROMPTU');

  const ClubType(this.value);
  final String value;
}

enum ClubMemberType {
  member('MEMBER'),
  leader('LEADER');

  const ClubMemberType(this.value);
  final String value;
}

enum BookCaseSortingOption {
  title('Title A-Z'),
  completionDate('Date Completed'),
  author('Author'),
  ratings('Rating');

  const BookCaseSortingOption(this.value);
  final String value;
}

enum ClubMembershipStatus {
  pending('PENDING'),
  active('ACTIVE'),
  rejected('REJECTED'),
  revoked('REVOKED'),
  removed('REMOVED'),
  left('LEFT'),
  reOpened(',REOPENED'),
  reScind('RESCIND'),
  inActive(',INACTIVE'),
  isInActive('INACTIVE');

  const ClubMembershipStatus(this.value);
  final String value;
}

enum ClubRequestType {
  incomingClubRequestByUserId('incomingClubRequestByUserId'),
  outgoingClubRequestByUserId('outgoingClubRequestByUserId'),
  incomingRequestByClubId('incomingRequestByClubId'),
  outGoingRequestByClubId('outGoingRequestByClubId');

  const ClubRequestType(this.value);
  final String value;
}

enum SearchOption {
  whoReadingThis("Who’s Reading This?"),
  whoReadThis("Who’s Read This?"),
  profile("Profile"),
  whatClubRead("What Clubs Are Reading This?"),
  clubs("Clubs"),
  whoIntoThisBook("Who’s Into This Book?"),
  whoIntoThisAuthor("Who’s Into This Author?");

  const SearchOption(this.value);
  final String value;
}

enum FilterOption {
  interested("Interested"),
  whoReadingThis("whoReadingThis"),
  whoReadThis("whoReadThis"),
  whatClubIntoThis("whatClubIntoThis");

  const FilterOption(this.value);
  final String value;
}

enum MatchesFilter {
  currentlyReadMatch("currentlyReaders"),
  toBeReadMatch("interestedUsers"),
  starMatch("ratingMatchedUsers");

  const MatchesFilter(this.value);
  final String value;
}

enum ErrorSource {
  agora("AgoraMeeting"),
  logOut("AutoLogout");

  const ErrorSource(this.value);
  final String value;
}

enum VersionResult {
  forceUpdate("FORCE_UPDATE"),
  optionalUpdate("OPTIONAL_UPDATE"),
  upToDate("UP_TO_DATE");

  const VersionResult(this.value);
  final String value;
}

enum InvalidDeepLink {
  message('There was an issue with the link please try again.');

  const InvalidDeepLink(this.value);
  final String value;
}

enum PlatformPurchase {
  google("GOOGLE"),
  apple("APPLE");

  const PlatformPurchase(this.value);
  final String value;
}

enum PlatformSubscriptionLink {
  googlePlayLink("https://play.google.com/store/account/subscriptions"),
  appStoreLink("https://apps.apple.com/account/subscriptions");

  const PlatformSubscriptionLink(this.value);
  final String value;
}

enum SystemNavigationMode {
  gesture("GESTURE"),
  threeButton("THREE_BUTTON");

  const SystemNavigationMode(this.value);
  final String value;
}
