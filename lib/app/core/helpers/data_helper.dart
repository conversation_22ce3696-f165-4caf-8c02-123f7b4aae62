import 'package:eljunto/app/core/enums/enums.dart';

import '../../../models/book_case_model.dart';
import '../../../models/book_club_model.dart';
import '../../../models/profile_model/profile_home_model/current_reading_model.dart';

class DataHelper {
  static List<List<BookCaseModel>> getCurrentlyReadingAndTopShelfBooks(
      List<BookCaseModel> books) {
    List<BookCaseModel> currentlyReadingBooks = [];
    List<BookCaseModel> topShelfBooks = [];
    List<BookCaseModel> completedBooks = [];

    for (var book in books) {
      if (book.is_currently_reading!) {
        // Add the book to currentlyReadingBooks if it is currently being read
        currentlyReadingBooks.add(book);

        // If the book is being reread (reRead >= 1), also add it to completedBooks
        if (book.reRead != null && book.reRead! >= 1) {
          completedBooks.add(book);
          if (book.topShelf!) {
            topShelfBooks.add(book);
          }
        }
      } else {
        // If the book is not currently being read, add it to completedBooks
        completedBooks.add(book);
        if (book.topShelf!) {
          topShelfBooks.add(book);
        }
      }
    }
    print("IN COMPLETED BOOKS : ${completedBooks.length}");
    return [currentlyReadingBooks, topShelfBooks, completedBooks];
  }

  static List<List<BookClubModel>> getStandingAndImprmptuClubs(
      List<BookClubModel> bookClubs) {
    List<BookClubModel> standingBookClubs = [];
    List<BookClubModel> impromptuBookClubs = [];

    for (var bookClub in bookClubs) {
      if (bookClub.bookClubType?.toLowerCase() ==
          ClubType.standing.value.toLowerCase()) {
        standingBookClubs.add(bookClub);
      } else if (bookClub.bookClubType?.toLowerCase() ==
          ClubType.impromptu.value.toLowerCase()) {
        impromptuBookClubs.add(bookClub);
      }
    }

    return [standingBookClubs, impromptuBookClubs];
  }

  static List<BookClubModel> getClubsLedByUser(
      List<BookClubModel> bookClubs, int userId) {
    List<BookClubModel> leaderBookClubs = [];

    for (var bookClub in bookClubs) {
      if (bookClub.userId == userId) {
        leaderBookClubs.add(bookClub);
      }
    }

    return leaderBookClubs;
  }

  static List<BookCaseModel> sortBookCaseList(
      List<BookCaseModel> bookcaseList, BookCaseSortingOption sortOption) {
    switch (sortOption) {
      case BookCaseSortingOption.title:
        bookcaseList
            .sort((a, b) => (a.bookName ?? '').compareTo(b.bookName ?? ''));
        break;
      case BookCaseSortingOption.completionDate:
        bookcaseList.sort((a, b) =>
            (b.readingCompleteDate ?? 0).compareTo(a.readingCompleteDate ?? 0));
        break;
      case BookCaseSortingOption.author:
        bookcaseList
            .sort((a, b) => (a.bookAuthor ?? '').compareTo(b.bookAuthor ?? ''));
        break;
      case BookCaseSortingOption.ratings:
        bookcaseList
            .sort((a, b) => (b.ratings ?? 0.0).compareTo(a.ratings ?? 0.0));
        break;
    }
    return bookcaseList;
  }

  static List<ProfileBookCase> sortProfileBookCaseList(
      List<ProfileBookCase> bookcaseList, BookCaseSortingOption sortOption) {
    switch (sortOption) {
      case BookCaseSortingOption.title:
        bookcaseList
            .sort((a, b) => (a.bookName ?? '').compareTo(b.bookName ?? ''));
        break;
      case BookCaseSortingOption.completionDate:
        bookcaseList.sort((a, b) =>
            (a.readingCompleteDate ?? 0).compareTo(b.readingCompleteDate ?? 0));
        break;
      case BookCaseSortingOption.author:
        bookcaseList
            .sort((a, b) => (a.bookAuthor ?? '').compareTo(b.bookAuthor ?? ''));

        break;
      case BookCaseSortingOption.ratings:
        bookcaseList
            .sort((a, b) => (b.ratings ?? 0.0).compareTo(a.ratings ?? 0.0));
        break;
    }
    return bookcaseList;
  }
}
