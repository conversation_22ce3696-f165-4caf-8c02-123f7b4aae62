import 'dart:async';
import 'dart:developer';

import 'package:eljunto/app/core/utils/app_config.dart';
import 'package:eljunto/models/messages/custom_chat_message.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sembast/sembast_io.dart';

class DatabaseHelper {
  // --- Sembast Singleton Setup ---
  DatabaseHelper._privateConstructor();
  static final DatabaseHelper instance = DatabaseHelper._privateConstructor();

  static Database? _database;
  static const String _dbNameDev = "prysom_sembast_dev.db";
  static const String _dbNameProd = "prysom_sembast.db";

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final dbDirectory = await getApplicationDocumentsDirectory();
    final dbName =
        AppConfig.shared.flavor == Flavor.dev ? _dbNameDev : _dbNameProd;
    final dbPath = join(dbDirectory.path, dbName);
    return await databaseFactoryIo.openDatabase(dbPath);
  }

  // --- Store References (like tables in SQL) ---
  final _messagesStore = stringMapStoreFactory.store('messages');
  final _clubMetadataStore = intMapStoreFactory.store('club_metadata');
  final _seenStatusStore = stringMapStoreFactory.store('seen_status');
  final _clubMembersStore = intMapStoreFactory.store('club_members');

  // --- Message Methods ---

  Future<void> insertMessage(CustomChatMessage message, int clubId) async {
    final db = await database;
    final messageId = message.customMessageProperties?['message_id'] as String?;
    if (messageId == null) return; // Cannot store a message without a unique ID

    // Add clubId to the message map for easy querying
    final messageJson = message.toJson()..['clubId'] = clubId;

    // .put() is an "upsert" - it inserts or updates automatically.
    await _messagesStore.record(messageId).put(db, messageJson);
  }

  Future<void> batchInsertMessages(
      List<CustomChatMessage> messages, int clubId) async {
    if (messages.isEmpty) return;
    final db = await database;
    await db.transaction((txn) async {
      for (final message in messages) {
        final messageId =
            message.customMessageProperties?['message_id'] as String?;
        if (messageId != null) {
          final messageJson = message.toJson()..['clubId'] = clubId;
          await _messagesStore.record(messageId).put(txn, messageJson);
        }
      }
    });
  }

  Future<List<CustomChatMessage>> getMessagesByBookClub(
    String clubId, {
    String orderBy = 'DESC',
    int limit = 20,
    int offset = 0,
  }) async {
    final db = await database;
    final finder = Finder(
      filter: Filter.equals('clubId', int.parse(clubId)),
      sortOrders: [SortOrder('createdAt', orderBy == 'DESC')],
      limit: limit,
      offset: offset,
    );
    final snapshots = await _messagesStore.find(db, finder: finder);
    return snapshots
        .map((snap) => CustomChatMessage.fromJson(snap.value))
        .toList();
  }

  // --- Seen Status Methods ---

  Future<void> insertOrUpdateSeenStatus(
      int userId, String clubId, bool isSeen) async {
    final db = await database;
    // Use a composite key for uniqueness
    final recordKey = '${userId}_$clubId';
    await _seenStatusStore.record(recordKey).put(db, {'isSeen': isSeen});
  }

  Future<bool> getSeenStatus(int userId, String clubId) async {
    final db = await database;
    final recordKey = '${userId}_$clubId';
    final record = await _seenStatusStore.record(recordKey).get(db);
    return record?['isSeen'] as bool? ?? false;
  }

  // --- Club & Member Methods ---

  Future<void> insertOrUpdateClubMetadata(
      int clubId, DateTime createdAt, String bookClubName) async {
    final db = await database;
    await _clubMetadataStore.record(clubId).put(db, {
      'bookClubName': bookClubName,
      'createdAt': createdAt.toIso8601String(),
    });
  }

  Future<Map<String, dynamic>?> getClubMetadata(int clubId) async {
    final db = await database;
    return await _clubMetadataStore.record(clubId).get(db);
  }

  Future<void> insertOrUpdateClubMembers(
      int bookClubId, List<Map<String, dynamic>> members) async {
    final db = await database;
    // Store the entire list of members under a single key (the bookClubId).
    // This is a common and efficient NoSQL pattern.
    await _clubMembersStore.record(bookClubId).put(db, {'members': members});
  }

  Future<List<Map<String, dynamic>>> getClubMembers(int bookClubId) async {
    final db = await database;
    final record = await _clubMembersStore.record(bookClubId).get(db);

    if (record != null && record['members'] is List) {
      // 1. First, safely cast the 'members' value to a List.
      final membersList = record['members'] as List;

      // 2. Then, use .map() to ensure each item in the list is a Map<String, dynamic>.
      //    The .toList() at the end creates the final list.
      return membersList
          .map((member) => Map<String, dynamic>.from(member))
          .toList();
    }

    return []; // Return an empty list if no valid data is found.
  }

  // --- Database Management ---

  Future<void> clearEntireDatabase() async {
    final db = await database;
    await _messagesStore.delete(db);
    await _clubMetadataStore.delete(db);
    await _seenStatusStore.delete(db);
    await _clubMembersStore.delete(db);
    log("Cleared Sembast local db");
  }

  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }
}
