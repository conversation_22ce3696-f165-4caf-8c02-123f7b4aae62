import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:month_picker_dialog/month_picker_dialog.dart';

import '../constants.dart';
import '../utils/text_style.dart';

class DateTimeHelper {
  static String getMonthYearDateFormat(int? intDatetoFormat) {
    DateTime? datetoFormat = intDatetoFormat != null
        ? DateTime.fromMillisecondsSinceEpoch(intDatetoFormat)
        : null;

    if (datetoFormat == null) {
      return 'Unknown';
    }
    return DateFormat('MMM yyyy').format(datetoFormat);
  }

  static String getDayMonthYearDateFormat(int? intDatetoFormat) {
    DateTime? datetoFormat = intDatetoFormat != null
        ? DateTime.fromMillisecondsSinceEpoch(intDatetoFormat)
        : null;

    if (datetoFormat == null) {
      return 'TBD';
    }
    return DateFormat('EEE MMM dd, yyyy').format(datetoFormat.toLocal());
  }

  static String getMeetingScheduleTime(
      int? meetingstartTime, int? meetingEndTime) {
    if (meetingstartTime == 0 || meetingEndTime == 0) {
      return 'TBD';
    }
    final yy =
        DateTime.fromMillisecondsSinceEpoch(meetingstartTime ?? 0).toLocal();
    final yyy =
        DateTime.fromMillisecondsSinceEpoch(meetingEndTime ?? 0).toLocal();
    // DateTime startTime =yy.toLocal();
    // DateTime endTime =
    //     DateFormat('HH:mm').parse(meetingEndndTime ?? '').toLocal();
    String formattedTime = DateFormat('h:mm a').format(yy);
    String formatedEndTime = DateFormat('h:mm a').format(yyy);
    final meetingTime = '$formattedTime-$formatedEndTime';
    return meetingTime;
  }

  static List<String> getMonths() {
    List<String> monthNames = List.generate(12, (index) {
      return DateFormat.MMM().format(DateTime(0, index + 1));
    });

    return monthNames;
  }

  static List<String> getYears() {
    List<String> years = List.generate(2025 - 2000, (index) {
      return (2000 + index).toString();
    });

    return years;
  }

  static Future<DateTime?> getMonthYear(BuildContext context) async {
    DateTime? selectedDate;
    await showMonthPicker(
      monthPickerDialogSettings: MonthPickerDialogSettings(
        headerSettings: PickerHeaderSettings(
          headerCurrentPageTextStyle: lbRegular.copyWith(
            fontSize: 14,
            color: AppConstants.primaryColor,
          ),
          headerSelectedIntervalTextStyle: lbRegular.copyWith(
            fontSize: 14,
            color: AppConstants.primaryColor,
          ),
          headerBackgroundColor: AppConstants.textGreenColor,
        ),
        dialogSettings: PickerDialogSettings(
          insetPadding: EdgeInsets.symmetric(
              horizontal: MediaQuery.of(context).size.width * 0.05),
          dialogBackgroundColor: AppConstants.backgroundColor,
        ),
        actionBarSettings: PickerActionBarSettings(
          cancelWidget: Text(
            "Cancel",
            style: lbRegular.copyWith(
              fontSize: 12,
              color: AppConstants.primaryColor,
            ),
          ),
          confirmWidget: Text(
            "Ok",
            style: lbBold.copyWith(
              fontSize: 12,
              color: AppConstants.primaryColor,
            ),
          ),
        ),
        dateButtonsSettings: PickerDateButtonsSettings(
          selectedDateRadius: 7,
          selectedMonthBackgroundColor: AppConstants.textGreenColor,
          monthTextStyle: lbRegular.copyWith(
            fontSize: 14,
            color: AppConstants.primaryColor,
          ),
          yearTextStyle: lbRegular.copyWith(
            fontSize: 12,
            color: AppConstants.primaryColor,
          ),
          unselectedYearsTextColor: AppConstants.primaryColor,
          unselectedMonthsTextColor: AppConstants.primaryColor,
          currentMonthTextColor: AppConstants.primaryColor,
          selectedMonthTextColor: Colors.white,
        ),
      ),
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime(
        DateTime.now().year,
        DateTime.now().month,
      ),
    ).then((value) {
      selectedDate = value;
    });
    return selectedDate;
  }

  static TimeOfDay parseTimeOfDay(String timeString) {
    final format = DateFormat('h:mm a'); // For 12-hour format with AM/PM
    final DateTime parsedTime = format.parse(timeString);
    // print("End Time : $parsedTime");
    return TimeOfDay.fromDateTime(parsedTime);
  }

  static TimeOfDay parseTimeOfDay_1(String time) {
    try {
      final format = DateFormat.jm(); // 12-hour format with AM/PM
      final dateTime = format.parseLoose(time);
      return TimeOfDay.fromDateTime(dateTime);
    } catch (e) {
      print("Error parsing time: $e");
      return const TimeOfDay(hour: 0, minute: 0); // or handle it as needed
    }
  }

// Increment TimeOfDay by a given number of minutes
  static TimeOfDay incrementTimeByMinutes(TimeOfDay time, int minutes) {
    final totalMinutes = time.hour * 60 + time.minute + minutes;
    return TimeOfDay(hour: totalMinutes ~/ 60, minute: totalMinutes % 60);
  }

// Check if the selected date is today
  static bool isToday(DateTime? pickDate) {
    final today = DateTime.now();
    return pickDate?.year == today.year &&
        pickDate?.month == today.month &&
        pickDate?.day == today.day;
  }

// Check if the loop time is a future time compared to the current time
  static bool isFutureTime(TimeOfDay loopTime, TimeOfDay currentTime) {
    return (loopTime.hour > currentTime.hour) ||
        (loopTime.hour == currentTime.hour &&
            loopTime.minute > currentTime.minute);
  }

// Format TimeOfDay to a readable string
  static String formatTimeOfDay(TimeOfDay time) {
    final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }

  static List<Map<String, String>> generateTimeIntervalsMap() {
    List<Map<String, String>> timeIntervals = [
      {'TBD': 'TBD'}
    ]; // Start with "TBD" as the first item
    const int totalMinutes = 4 * 60; // 4 hours in minutes
    const int interval = 30; // 15 minutes interval

    for (int minutes = interval; minutes <= totalMinutes; minutes += interval) {
      int hours = minutes ~/ 60;
      int remainingMinutes = minutes % 60;

      // Format the key as "HH:mm"
      String key =
          '${hours.toString().padLeft(2, '0')}:${remainingMinutes.toString().padLeft(2, '0')}';

      // Format the display value as a human-readable time interval
      String value = '';
      if (hours > 0) {
        value += '$hours hour';
        if (hours > 1) value += 's';
      }
      if (remainingMinutes > 0) {
        if (hours > 0) value += ' ';
        value += '$remainingMinutes min';
      }

      timeIntervals.add({key: value});
    }

    return timeIntervals;
  }

  static String formatedDateFunction(DateTime date, DateTime? pickDate) {
    String daySuffix(int day) {
      if (day >= 11 && day <= 13) {
        return 'th';
      }
      switch (day % 10) {
        case 1:
          return 'st';
        case 2:
          return 'nd';
        case 3:
          return 'rd';
        default:
          return 'th';
      }
    }

    String formattedDate = DateFormat('EEE MMM d').format(date);
    String dayWithSuffix = daySuffix(pickDate?.day ?? 0);
    String yearAndTime = DateFormat('yyyy').format(
      pickDate ?? DateTime.now(),
    );
    return '$formattedDate$dayWithSuffix, $yearAndTime';
  }

// Generate time options in 15-minute intervals based on the selected date
  static List<String> generateTimeOptions(
      DateTime? pickDate, TimeOfDay? pickedStartTime) {
    List<String> timeOptions = ['TBD'];
    const int interval = 15;
    const startTime = TimeOfDay(hour: 0, minute: 0);
    const endTime = TimeOfDay(hour: 23, minute: 45);
    final currentTime = pickedStartTime ?? TimeOfDay.now();

    var loopTime = startTime;
    while (loopTime.hour < endTime.hour ||
        (loopTime.hour == endTime.hour && loopTime.minute <= endTime.minute)) {
      if (!isToday(pickDate) || isFutureTime(loopTime, currentTime)) {
        timeOptions.add(formatTimeOfDay(loopTime));
      }
      loopTime = incrementTimeByMinutes(loopTime, interval);
    }
    return timeOptions;
  }

  static List<String> getMeetingAlertOptions(DateTime selectedMeetingDate) {
    DateTime now = DateTime.now();

    // Calculate the difference between current date and selected meeting date
    Duration difference = selectedMeetingDate.difference(now);
    List<String> alertOptions = [];

    // If the meeting is more than 1 day away, show all options
    if (difference.inDays >= 7) {
      alertOptions = [
        '15 Min',
        '1 Day',
        '1 Week',
      ];
      //filters = alertOptions;
    }
    // If the meeting is set for tomorrow, show 1 Day and 15 Min options
    else if (difference.inHours >= 24 && difference.inDays < 7) {
      alertOptions = [
        '15 Min',
        '1 Day',
      ];
      //filters = alertOptions;
    }
    // If the meeting is today, show only the 15 Min option
    else if (difference.inHours <= 24) {
      alertOptions = ['15 Min'];
      //filters = alertOptions;
    }

    return alertOptions;
  }
}
