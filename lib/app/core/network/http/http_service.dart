import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/app/core/enums/enums.dart';
import 'package:http_parser/http_parser.dart';

class HttpApiService {
  final dio.Dio _dio; // Make dio instance private

  // Constructor accepting a Dio instance
  HttpApiService({required dio.Dio dioInstance}) : _dio = dioInstance;

  Future<dio.Response> post(String url, Map<String, dynamic> body) async {
    return await _dio.post(
      url,
      options: dio.Options(
        headers: {
          'Content-Type': 'application/json',
          'Portal-Type': PortalType.app.value,
        },
        validateStatus: (status) =>
            status != null &&
            (status >= 200 && status < 300 || status >= 400 && status < 500),
      ),
      data: jsonEncode(body),
    );
  }

  Future<dio.Response> get(String url) async {
    return await _dio.get(
      url,
      options: dio.Options(
        headers: {
          'Content-Type': 'application/json',
          'Portal-Type': PortalType.app.value,
        },
        validateStatus: (status) =>
            status != null &&
            (status >= 200 && status < 300 || status >= 400 && status < 500),
      ),
    );
  }

  Future<dio.Response> put(String url, Map<String, dynamic> body) async {
    return await _dio.put(
      url,
      options: dio.Options(
        headers: {
          'Content-Type': 'application/json',
          'Portal-Type': PortalType.app.value,
        },
        validateStatus: (status) =>
            status != null &&
            (status >= 200 && status < 300 || status >= 400 && status < 500),
      ),
      data: jsonEncode(body),
    );
  }

  Future<dio.Response> delete(String url) async {
    return await _dio.delete(
      url,
      options: dio.Options(
        headers: {
          'Content-Type': 'application/json',
          'Portal-Type': PortalType.app.value,
        },
        validateStatus: (status) =>
            status != null &&
            (status >= 200 && status < 300 || status >= 400 && status < 500),
      ),
    );
  }

  Future<dio.Response> uploadFile(String url, File file, int userId) async {
    final data = await file.length();
    log("FileName 1: $data");

    String fileName = file.path.split('/').last;
    log("FileName : $fileName");
    dio.FormData formData = dio.FormData.fromMap({
      'file': await dio.MultipartFile.fromFile(
        file.path,
        filename: fileName,
        contentType:
            MediaType.parse('image/${file.path.split('.').last.toLowerCase()}'),
      ),
      'userId': userId.toString(),
    });
    log("FoemData : $formData");

    return await _dio.post(
      url,
      data: formData,
      options: dio.Options(
        headers: {
          'Portal-Type': PortalType.app.value,
        },
        validateStatus: (status) =>
            status != null &&
            (status >= 200 && status < 300 || status >= 400 && status < 500),
      ),
    );
  }
}
