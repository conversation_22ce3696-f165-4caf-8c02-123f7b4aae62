import 'package:flutter/material.dart';

/// A class to hold all the global navigator keys for the application.
///
/// Using dedicated keys for each navigator (root and shell branches) allows for
/// precise navigation control within different parts of the app.
class AppNavigationKeys {
  AppNavigationKeys._();

  /// The key for the root navigator. This is the top-level navigator of the app.
  static final rootNavigatorKey = GlobalKey<NavigatorState>();

  /// Navigator key for the 'Home' branch of the [StatefulShellRoute].
  static final homeNavigatorKey = GlobalKey<NavigatorState>(debugLabel: 'Home');

  /// Navigator key for the 'Profile' branch of the [StatefulShellRoute].
  static final profileNavigatorKey =
      GlobalKey<NavigatorState>(debugLabel: 'Profile');

  /// Navigator key for the 'Message' branch of the [StatefulShellRoute].
  static final messageNavigatorKey =
      GlobalKey<NavigatorState>(debugLabel: 'Message');

  /// Navigator key for the 'Clubs' branch of the [StatefulShellRoute].
  static final clubsNavigatorKey =
      GlobalKey<NavigatorState>(debugLabel: 'Clubs');

  /// Navigator key for the 'Search' branch of the [StatefulShellRoute].
  static final searchNavigatorKey =
      GlobalKey<NavigatorState>(debugLabel: 'Search');

  /// Navigator key for the 'Settings' branch of the [StatefulShellRoute].
  static final settingsNavigatorKey =
      GlobalKey<NavigatorState>(debugLabel: 'Settings');

  /// Navigator key for the 'No Network' screen.
  static final noNetworkNavigatorKey =
      GlobalKey<NavigatorState>(debugLabel: 'NoNetwork');
}
