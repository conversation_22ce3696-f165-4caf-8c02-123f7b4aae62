import 'dart:developer';

import 'package:eljunto/reusableWidgets/transition_widget.dart';
import 'package:eljunto/views/clubs/club_invitations.dart';
import 'package:eljunto/views/clubs/clubs_home/clubs_home_screen.dart';
import 'package:eljunto/views/clubs/discussion_question_screen.dart';
import 'package:eljunto/views/clubs/fellow_reader_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option/add_edit_meeting_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option/charter_member_request_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option/manage_incoming_request_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option/manage_meetings_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option/manage_memeber_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option/manage_outgoing_request_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option/new_book_club_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option_screen.dart';
import 'package:eljunto/views/clubs/new_club_opening_screen.dart';
import 'package:eljunto/views/clubs/outgoing_club_request_screen.dart';
import 'package:eljunto/views/clubs/user_club_details/user_club_details_screen.dart';
import 'package:go_router/go_router.dart';

import '../app_navigation_keys.dart';

/// Defines the routes for the 'Clubs' section of the application.
class ClubsRoutes {
  ClubsRoutes._();

  static final StatefulShellBranch branch = StatefulShellBranch(
    navigatorKey: AppNavigationKeys.clubsNavigatorKey,
    routes: [
      GoRoute(
        path: '/Clubs',
        name: 'Clubs',
        builder: (context, state) => ClubsHomeScreen(key: state.pageKey),
        routes: [
          GoRoute(
            path: "club-invitations",
            name: "club-invitations",
            pageBuilder: (context, state) {
              final query = state.uri.queryParameters;
              final userId = query['userId'];
              return TransitionPageWidget.navigateTransitionPage(
                child: ClubInvitations(
                  key: state.pageKey,
                  userId: int.parse(userId ?? '0'),
                ),
              );
            },
          ),
          GoRoute(
            path: "club-request",
            name: "club-request",
            pageBuilder: (context, state) {
              return TransitionPageWidget.navigateTransitionPage(
                child: OutGoingClubRequest(
                  key: state.pageKey,
                ),
              );
            },
          ),
          GoRoute(
            path: "new-club-opening",
            name: "new-club-opening",
            pageBuilder: (context, state) {
              return TransitionPageWidget.navigateTransitionPage(
                child: NewClubOpeningScreen(
                  key: state.pageKey,
                ),
              );
            },
          ),
          GoRoute(
            path: "NewClubScreen",
            name: "NewClubScreen",
            pageBuilder: (context, state) {
              final invitation = state.extra.toString();
              return TransitionPageWidget.navigateTransitionPage(
                child: NewBookClubScreen(
                  key: state.pageKey,
                  clubType: invitation,
                ),
              );
            },
          ),
          GoRoute(
            path: "user-club-details",
            name: "user-club-details",
            pageBuilder: (context, state) {
              final queryParameters = state.uri.queryParameters;
              final bookClubId = queryParameters['bookClubId'];
              final userId = queryParameters['userId'];
              log("In Route BookClubId : $bookClubId");
              return TransitionPageWidget.navigateTransitionPage(
                child: UserClubDetailsScreen(
                  key: state.pageKey,
                  bookClubId: int.parse(bookClubId ?? '0'),
                  userId: userId ?? '',
                ),
              );
            },
            routes: [
              GoRoute(
                path: "clubsScreen4",
                name: "clubsScreen4",
                pageBuilder: (context, state) {
                  final queryParameters = state.uri.queryParameters;
                  final bookClubId = queryParameters['bookClubId'];

                  return TransitionPageWidget.navigateTransitionPage(
                    child: LeaderAdminScreen(
                      key: state.pageKey,
                      bookClubId: int.parse(bookClubId ?? '0'),
                    ),
                  );
                },
                routes: [
                  GoRoute(
                    path: "ManageIncomeRequest",
                    name: "ManageIncomeRequest",
                    pageBuilder: (context, state) {
                      final queryParameters = state.uri.queryParameters;
                      final bookClubId = queryParameters['bookClubId'];
                      final userId = queryParameters['userId'];
                      return TransitionPageWidget.navigateTransitionPage(
                        child: ManageIncomeRequestScreen(
                          key: state.pageKey,
                          bookClubId: int.parse(bookClubId ?? '0'),
                          userId: userId ?? '',
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
          GoRoute(
            path: "discussionScreen",
            name: "discussionScreen",
            pageBuilder: (context, state) {
              final extras = state.extra as Map;

              return TransitionPageWidget.navigateTransitionPage(
                child: DiscussionQueScreen(
                  key: state.pageKey,
                  bookClubName: extras['clubName'],
                  discussionQuestions: extras['discussionQue'],
                ),
              );
            },
          ),
          GoRoute(
            path: "ManageOutGInvitations",
            name: "ManageOutGInvitations",
            pageBuilder: (context, state) {
              final queryParameters = state.uri.queryParameters;
              final bookClubId = queryParameters['bookClubId'];
              final buttonName = state.extra.toString();
              return TransitionPageWidget.navigateTransitionPage(
                child: ManageOutGoningScreen(
                  key: state.pageKey,
                  buttonName: buttonName,
                  bookClubId: int.parse(bookClubId ?? '0'),
                ),
              );
            },
          ),
          GoRoute(
            path: "fellow-reader",
            name: "fellow-reader",
            pageBuilder: (context, state) {
              return TransitionPageWidget.navigateTransitionPage(
                child: FellowReaderScreen(
                  key: state.pageKey,
                ),
              );
            },
          ),
          GoRoute(
            path: "ManageMember",
            name: "ManageMember",
            pageBuilder: (context, state) {
              final buttonName = state.extra.toString();
              return TransitionPageWidget.navigateTransitionPage(
                child: ManageMemberScreen(
                  key: state.pageKey,
                  buttonName: buttonName,
                ),
              );
            },
          ),
          GoRoute(
            path: "ManageMeeting",
            name: "ManageMeeting",
            pageBuilder: (context, state) {
              final buttonName = state.extra.toString();
              return TransitionPageWidget.navigateTransitionPage(
                child: ManageMeetingScreen(
                  key: state.pageKey,
                  buttonName: buttonName,
                ),
              );
            },
          ),
          GoRoute(
            path: "AddMeeting",
            name: "AddMeeting",
            pageBuilder: (context, state) {
              final extras = state.extra as Map;
              return TransitionPageWidget.navigateTransitionPage(
                child: AddEditMeetingScreen(
                  key: state.pageKey,
                  addEditName: extras['addMeeting'],
                  upcomingMeetings: extras['editData'],
                  editMeetingFlag: extras['boolean'],
                  editMeetngId: extras['editMeetingId'],
                ),
              );
            },
          ),
          GoRoute(
            path: "CharterRequest",
            name: "CharterRequest",
            pageBuilder: (context, state) {
              final buttonName = state.extra.toString();
              return TransitionPageWidget.navigateTransitionPage(
                child: CharterMemberScreen(
                  key: state.pageKey,
                  buttonName: buttonName,
                ),
              );
            },
          ),
        ],
      ),
    ],
  );
}
