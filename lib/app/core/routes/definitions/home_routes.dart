import 'package:eljunto/reusableWidgets/transition_widget.dart';
import 'package:eljunto/views/home_screen/book_review_screen.dart';
import 'package:eljunto/views/home_screen/club_charter_screen.dart';
import 'package:eljunto/views/home_screen/club_details/club_details_screen.dart';
import 'package:eljunto/views/home_screen/club_member_bookcase_screen.dart';
import 'package:eljunto/views/home_screen/club_member_profile_screen.dart';
import 'package:eljunto/views/home_screen/home_screen.dart';
import 'package:eljunto/views/home_screen/home_screen_6.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../app_navigation_keys.dart';

/// Defines the routes for the 'Home' section of the application.
class HomeRoutes {
  HomeRoutes._();

  static final StatefulShellBranch branch = StatefulShellBranch(
    navigatorKey: AppNavigationKeys.homeNavigatorKey,
    routes: [
      GoRoute(
        path: '/Home',
        name: 'Home',
        pageBuilder: (context, state) {
          return CustomTransitionPage(
            child: HomeScreen(
              key: state.pageKey,
            ),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              return SlideTransition(
                position: animation.drive(
                  Tween(begin: const Offset(0, 1), end: Offset.zero).chain(
                    CurveTween(
                      curve: Curves.easeIn,
                    ),
                  ),
                ),
                child: child,
              );
            },
          );
        },
        routes: [
          GoRoute(
            path: 'club-details',
            name: 'club-details',
            pageBuilder: (context, state) {
              final extra = state.extra as Map;
              final bookClubId = extra['bookClubId'];
              final bookClubName = extra['bookClubName'];
              final impromptuCount = extra['impromptuCount'];
              return TransitionPageWidget.navigateTransitionPage(
                child: ClubDetails(
                  key: state.pageKey,
                  bookClubId: bookClubId,
                  bookClubName: bookClubName,
                  impromptuCount: impromptuCount,
                ),
              );
            },
          ),
          GoRoute(
            path: 'club-charter',
            name: 'club-charter',
            pageBuilder: (context, state) {
              final extra = state.extra as Map;
              return TransitionPageWidget.navigateTransitionPage(
                child: ClubCharter(
                  key: state.pageKey,
                  bookClubName: extra['bookClubName'],
                  clubCharter: extra['clubCharter'],
                ),
              );
            },
          ),
          GoRoute(
            path: 'club-member-profile',
            name: 'club-member-profile',
            pageBuilder: (context, state) {
              final extra = state.extra as Map;
              return TransitionPageWidget.navigateTransitionPage(
                child: ClubMemberProfile(
                  key: state.pageKey,
                  userId: extra['userId'],
                  userName: extra['userName'],
                ),
              );
            },
          ),
          GoRoute(
            path: 'book-review',
            name: 'book-review',
            pageBuilder: (context, state) {
              final extras = state.extra as Map;
              return TransitionPageWidget.navigateTransitionPage(
                child: BookReview(
                  key: state.pageKey,
                  userName: extras['userName'],
                  userHandle: extras['userHandle'],
                  userClubInvitation: extras['userClubInvitation'],
                  bookName: extras['bookName'],
                  bookAuthor: extras['bookAuthor'],
                  ratings: extras['ratings'],
                  review: extras['review'],
                  userProfilePicture: extras['userProfile'],
                  userOwnProfile: extras['userOwnProfile'],
                ),
              );
            },
          ),
          GoRoute(
            path: 'home6',
            name: 'HomeScreen6',
            pageBuilder: (context, state) {
              final memberName = state.extra.toString();
              return TransitionPageWidget.navigateTransitionPage(
                child: HomeScreen6(
                  key: state.pageKey,
                  clubName: memberName,
                ),
              );
            },
          ),
          GoRoute(
            path: 'club-member-bookcase',
            name: 'club-member-bookcase',
            pageBuilder: (context, state) {
              final Map<String, dynamic>? extra =
                  state.extra as Map<String, dynamic>?;
              return TransitionPageWidget.navigateTransitionPage(
                child: ClubMemberBookCase(
                  key: state.pageKey,
                  userName: extra?['userName'] as String?,
                  userHandle: extra?['userHandle'] as String?,
                  userClubInvitation: extra?['userClubInvitation'] as bool?,
                  userId: extra?['userId'],
                  userProfilePicture: extra?['userProfilePicture'],
                ),
              );
            },
          ),
        ],
      ),
    ],
  );
}
