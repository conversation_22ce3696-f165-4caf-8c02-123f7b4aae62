import 'package:eljunto/reusableWidgets/transition_widget.dart';
import 'package:eljunto/views/settings/block_list.dart';
import 'package:eljunto/views/settings/change_password.dart';
import 'package:eljunto/views/settings/delete_account.dart';
import 'package:eljunto/views/settings/manage_email.dart';
import 'package:eljunto/views/settings/manage_subscription.dart';
import 'package:eljunto/views/settings/notification_settings.dart';
import 'package:eljunto/views/settings/settings_screen.dart';
import 'package:go_router/go_router.dart';

import '../app_navigation_keys.dart';

/// Defines the routes for the 'Settings' section of the application.
class SettingsRoutes {
  SettingsRoutes._();

  static final StatefulShellBranch branch = StatefulShellBranch(
    navigatorKey: AppNavigationKeys.settingsNavigatorKey,
    routes: [
      GoRoute(
        path: '/Settings',
        name: 'Settings',
        builder: (context, state) => SettingScreen(key: state.pageKey),
        routes: [
          GoRoute(
            path: 'change-password',
            name: 'change-password',
            pageBuilder: (context, state) {
              return TransitionPageWidget.navigateTransitionPage(
                child: ChangePasswordPage(
                  key: state.pageKey,
                ),
              );
            },
          ),
          GoRoute(
            path: 'manage-email',
            name: 'manage-email',
            pageBuilder: (context, state) {
              return TransitionPageWidget.navigateTransitionPage(
                child: ManageEmailPage(
                  key: state.pageKey,
                ),
              );
            },
          ),
          GoRoute(
            path: 'notification-settings',
            name: 'notification-settings',
            pageBuilder: (context, state) {
              return TransitionPageWidget.navigateTransitionPage(
                child: NotificationSettingsPage(
                  key: state.pageKey,
                ),
              );
            },
          ),
          GoRoute(
            path: 'block-list',
            name: 'block-list',
            pageBuilder: (context, state) {
              return TransitionPageWidget.navigateTransitionPage(
                child: BlockListPage(
                  key: state.pageKey,
                ),
              );
            },
          ),
          GoRoute(
            path: 'manage-subscription',
            name: 'manage-subscription',
            pageBuilder: (context, state) {
              return TransitionPageWidget.navigateTransitionPage(
                child: ManageSubscriptionPage(
                  key: state.pageKey,
                ),
              );
            },
          ),
          GoRoute(
            path: 'delete-account',
            name: 'delete-account',
            pageBuilder: (context, state) {
              return TransitionPageWidget.navigateTransitionPage(
                child: DeleteAccountPage(
                  key: state.pageKey,
                ),
              );
            },
          ),
        ],
      ),
    ],
  );
}
