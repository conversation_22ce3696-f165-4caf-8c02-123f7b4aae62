import 'dart:developer';

import 'package:shared_preferences/shared_preferences.dart';

/// A helper class to manage the persistence of deep link routes.
///
/// This class encapsulates the SharedPreferences logic for storing and
/// retrieving a pending deep link that needs to be navigated to after
/// the app has fully initialized.
class RoutePersistence {
  RoutePersistence._();

  static const _pendingRouteKey = 'pendingDeepLink';
  static String? lastClickedDeepLink;

  /// Stores the given [route] as a pending deep link.
  static Future<void> storePendingRoute(String route) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_pendingRouteKey, route);
    log("Stored pending route: $route");
  }

  /// Retrieves the pending deep link route without clearing it.
  ///
  /// Returns the route, or `null` if none is stored.
  static Future<String?> getPendingRouteWithoutClearing() async {
    final prefs = await SharedPreferences.getInstance();
    final pendingRoute = prefs.getString(_pendingRouteKey);
    log("Retrieved pending route (without clearing): $pendingRoute");
    return pendingRoute;
  }

  /// Retrieves the pending deep link route and then clears it from storage.
  ///
  /// This ensures the deep link is only used once.
  /// Returns the route, or `null` if none was stored.
  static Future<String?> getPendingRouteAndClear() async {
    final prefs = await SharedPreferences.getInstance();
    final pendingRoute = prefs.getString(_pendingRouteKey);
    if (pendingRoute != null) {
      await prefs.remove(_pendingRouteKey);
      log("Cleared pending deep link after retrieving: $pendingRoute");
    }
    return pendingRoute;
  }

  /// Explicitly clears any stored pending deep link route.
  static Future<void> clearPendingRoute() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_pendingRouteKey);
    log("Explicitly cleared pending route");
  }
}
