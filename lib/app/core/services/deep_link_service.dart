import 'dart:async';
import 'dart:developer';

import 'package:app_links/app_links.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DeepLinkService {
  static final DeepLinkService _instance = DeepLinkService._internal();
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();

  final AppLinks _appLinks = AppLinks();
  StreamSubscription<Uri>? _linkSubscription;
  Uri? _pendingDeepLink;

  /// Callback to execute when a deep link is received
  Function(Uri uri)? onDeepLinkReceived;

  Future<void> initDeepLinks() async {
    try {
      // Check for any stored deep link first (from previous sessions)
      await _loadStoredDeepLink();

      // Handle initial link (cold start)
      final appLink = await _appLinks.getInitialLink();
      if (appLink != null) {
        _pendingDeepLink = appLink;
        // Also persist it
        await _persistDeepLink(appLink);
        log('Initial deep link: $_pendingDeepLink');
      }
    } catch (e) {
      log('Error getting initial link: $e');
    }

    // Handle link updates when the app is running
    try {
      _linkSubscription = _appLinks.uriLinkStream.listen(
        (Uri? uri) {
          if (uri != null) {
            log('Updated deep link: $uri');
            _pendingDeepLink = uri;
            _persistDeepLink(uri); // Persist in case app is terminated
            onDeepLinkReceived?.call(uri); // Trigger callback if set
          }
        },
        onError: (err) {
          log('Error in uriLinkStream: $err');
        },
      );
    } catch (e) {
      log('Error listening to uriLinkStream: $e');
    }
  }

  Future<void> _persistDeepLink(Uri deepLink) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString('pendingDeepLink', deepLink.toString());
      log('Deep link persisted: ${deepLink.toString()}');
    } catch (e) {
      log('Error persisting deep link: $e');
    }
  }

  Future<void> _loadStoredDeepLink() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? storedLink = prefs.getString('pendingDeepLink');
      if (storedLink != null && storedLink.isNotEmpty) {
        _pendingDeepLink = Uri.parse(storedLink);
        log('Loaded stored deep link: $_pendingDeepLink');
      }
    } catch (e) {
      log('Error loading stored deep link: $e');
    }
  }

  Future<void> storePendingDeepLink(Uri deepLink) async {
    _pendingDeepLink = deepLink;
    await _persistDeepLink(deepLink);
  }

  /// Retrieves the pending deep link and clears it after use.
  Future<Uri?> consumePendingDeepLink() async {
    final link = _pendingDeepLink;
    _pendingDeepLink = null; // Clear in-memory link

    // Also clear from storage
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.remove('pendingDeepLink');
    } catch (e) {
      log('Error clearing stored deep link: $e');
    }

    return link;
  }

  Future<bool> hasPendingDeepLink() async {
    // If there's no in-memory link, check storage
    if (_pendingDeepLink == null) {
      await _loadStoredDeepLink();
    }
    return _pendingDeepLink != null;
  }

  /// Dispose stream when not needed
  void dispose() {
    _linkSubscription?.cancel();
  }
}
