import 'dart:async';
import 'dart:developer';

import 'package:app_badge_plus/app_badge_plus.dart';
import 'package:app_settings/app_settings.dart';
import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/routes/app_navigation_keys.dart';
import 'package:eljunto/app/core/routes/redirect_logic.dart';
import 'package:eljunto/app/core/routes/route_persistence.dart';
import 'package:eljunto/app/core/services/deep_link_service.dart';
import 'package:eljunto/controller/login_controller.dart';
import 'package:eljunto/services/login_service.dart';
import 'package:eljunto/views/meeting/services/overlay_service.dart';
import 'package:eljunto/views/meeting/services/video_call_manager.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../controller/user_credential_controller.dart';

String? refreshToken;

class NotificationServices {
  LoginService loginService = LoginService();
  bool isUserLoggedIn = false;
  FirebaseMessaging messaging = FirebaseMessaging.instance;
  int notificationCount = 0;

  // Add stream subscription management
  StreamSubscription<RemoteMessage>? _onMessageSubscription;
  StreamSubscription<RemoteMessage>? _onMessageOpenedAppSubscription;
  final _routerKey = AppNavigationKeys.rootNavigatorKey;

  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // function to initialize flutter local notification plugins to show notifications for android when app is active
  void initLocalNotifications(BuildContext context) {
    // for Android
    var androidInitializationSettings =
        const AndroidInitializationSettings('notification_icon');
//@mipmap/ic_launcher
    // for iOS
    var iosInitializationSettings = const DarwinInitializationSettings();

    var initializationSettings = InitializationSettings(
      android: androidInitializationSettings,
      iOS: iosInitializationSettings,
    );

    flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (message) {
        log('notification clicked');
        if (message.payload != null) {
          handleDeepLink(message.payload ?? '');
        }
      },
    );
  }

  void initializeBadgeCount() {
    AppBadgePlus.isSupported().then((isSupported) {
      if (isSupported) {
        log('badging supported');
        AppBadgePlus.updateBadge(notificationCount);
      }
    });
  }

  void updateBadgeCount() {
    AppBadgePlus.isSupported().then((isSupported) {
      if (isSupported) {
        AppBadgePlus.updateBadge(notificationCount).then((_) {
          log('Badge count updated to $notificationCount');
        }).catchError((error) {
          log('Failed to update badge count: $error');
        });
      }
    });
  }

  Future<void> firebaseInit(BuildContext context) async {
    // Initialize for terminated state

    final initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null && initialMessage.data['deepLink'] != null) {
      log("App opened from terminated state with notification deep link");
      // For terminated state, set the direct deep link flag
      String deepLink = initialMessage.data['deepLink'];

      // Store it directly in our tracking variable
      RoutePersistence.lastClickedDeepLink = _parseRouteFromDeepLink(deepLink);

      // If app is already initialized, handle immediately
      if (AppRedirect.appInitialized) {
        handleDeepLink(deepLink);
      }
      // Otherwise, the splash screen will pick it up from lastClickedDeepLink
    }

    // Handle background state
    _onMessageOpenedAppSubscription =
        FirebaseMessaging.onMessageOpenedApp.listen((message) {
      if (message.data['deepLink'] != null) {
        log("App opened from background with notification deep link");
        String deepLink = message.data['deepLink'];

        // For background state, set flag that we just clicked a notification
        RoutePersistence.lastClickedDeepLink =
            _parseRouteFromDeepLink(deepLink);

        // Direct navigation if app is already initialized
        handleDeepLink(deepLink);
      }
    });

    // Handle foreground notifications (same as before)
    _onMessageSubscription =
        FirebaseMessaging.onMessage.listen((message) async {
      if (context.mounted) {
        final isLoggedIn =
            await Provider.of<UserCredentialController>(context, listen: false)
                .isUserLoggedIn();

        if (!isLoggedIn) {
          if (message.data.containsKey('deepLink')) {
            try {
              final deepLinkUri = Uri.parse(message.data['deepLink']);
              log('Deep link received while not logged in: ${deepLinkUri.toString()}');
              DeepLinkService().storePendingDeepLink(deepLinkUri);
            } catch (e) {
              log("Error parsing deep link: $e");
            }
          }
          return;
        }

        notificationCount++;
        updateBadgeCount();

        // Show notification for both platforms
        showNotification(message);
      }
    });
  }

  void dispose() {
    _onMessageSubscription?.cancel();
    _onMessageOpenedAppSubscription?.cancel();
  }

  void requestNotificationPermission() async {
    NotificationSettings notificationSettings =
        await messaging.requestPermission(
      alert: true,
      announcement: true,
      badge: true,
      carPlay: true,
      criticalAlert: true,
      // provisional: true,
      sound: true,
    );

    if (notificationSettings.authorizationStatus ==
        AuthorizationStatus.authorized) {
      if (kDebugMode) {
        print("user permission granted");
      }
    } else if (notificationSettings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      if (kDebugMode) {
        print("user permission provisional granted");
      }
    } else {
      if (kDebugMode) {
        AppSettings.openAppSettings(type: AppSettingsType.notification);
        print("user denied permission");
      }
    }
  }

  Future<void> showNotification(RemoteMessage message) async {
    AndroidNotificationChannel notificationChannel = AndroidNotificationChannel(
      'high_importance_channel',
      message.notification?.android?.channelId.toString() ?? '',
      importance: Importance.high,
      playSound: true,
      showBadge: true,
      sound: const UriAndroidNotificationSound(''),
    );

    AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      notificationChannel.id.toString(),
      notificationChannel.name.toString(),
      importance: Importance.max,
      playSound: true,
      priority: Priority.max,
      ticker: 'ticker',
      sound: notificationChannel.sound,
      setAsGroupSummary: true,
      groupKey: message.messageType,
      groupAlertBehavior: GroupAlertBehavior.summary,
      visibility: NotificationVisibility.public,
      icon: 'notification_icon',
      // largeIcon: const DrawableResourceAndroidBitmap('notification_icon'),
      channelShowBadge: true,
      enableVibration: true,
      colorized: true,
      color: AppConstants.primaryColor,
    );

    DarwinNotificationDetails darwinNotificationDetails =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      interruptionLevel: InterruptionLevel.timeSensitive,
      badgeNumber: notificationCount,
    );

    NotificationDetails notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: darwinNotificationDetails,
    );

    Future.delayed(Duration.zero, () {
      flutterLocalNotificationsPlugin.show(
        0,
        message.notification!.title.toString(),
        message.notification!.body.toString(),
        notificationDetails,
        payload: message.data['deepLink'],
      );
    });
  }

  String? token;
  Future<String> getDeviceToken() async {
    try {
      // await messaging.deleteToken();
      // await messaging.getAPNSToken();
      token = await messaging.getToken();
      // SharedPreferences pref = await SharedPreferences.getInstance();
      // final String firebaseTokenKey = 'fcmToken';
      // final String currentToken = pref.getString(firebaseTokenKey) ?? '';
      // if (currentToken != token) {
      //   log('Get FCM Token  $token');
      //   pref.setString(firebaseTokenKey, token!);
      // }
      // final deviceToken = pref.getString(firebaseTokenKey);
      // log('Device Token1111: $deviceToken');
      log("Get Device FCM TOKEN : $token");
    } catch (e) {
      log("Failed to create FCM Token : ${e.toString()}");
    }

    return token!;
  }

  void isTokenRefresh(BuildContext context) {
    log('FCM Token Refreshed Function');
    messaging.onTokenRefresh.listen((token) async {
      log('FCM Token Refreshed In Listen: $token');
      SharedPreferences pref = await SharedPreferences.getInstance();
      final String firebaseTokenKey = 'fcmToken';
      final String currentToken = pref.getString(firebaseTokenKey) ?? '';
      if (currentToken != token) {
        log('FCM Token Refreshed: $token');
        await pref.setString(firebaseTokenKey, token);
        if (context.mounted) {
          await Provider.of<LoginController>(context, listen: false)
              .updateFcmToken();
        }
      }
    });
  }

  // Consolidated message handling
  Future<void> setupInteractMessage(BuildContext context) async {
    if (!isUserLoggedIn) {
      return;
    }
    // Handle initial message (terminated state)
    final initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null && context.mounted) {
      handleDeepLink(initialMessage.data['deepLink']);
    }

    // Handle background/terminated messages
    _onMessageOpenedAppSubscription =
        FirebaseMessaging.onMessageOpenedApp.listen((message) {
      log('Message opened: ${message.messageId}');

      if (context.mounted) {
        handleDeepLink(message.data['deepLink']);
      }
    });
  }

  String? pendingDeepLink = '';

  /// ROUTING TO SPECIFIC PAGE WHEN OPENED VIA DEEPLINKS
  void handleDeepLink(String? deepLink) async {
    if (deepLink == null) return;

    log('Handling deep link: $deepLink');

    // Check if there's an active call and put it in PiP mode
    final videoCallManager = VideoCallManager();
    final bool isCallActive = videoCallManager.isCallActive;

    if (isCallActive) {
      OverlayService().showVideoCallOverlay();
    }

    if (_routerKey.currentState == null) {
      log('Router not ready, storing deep link');
      await RoutePersistence.storePendingRoute(deepLink);
      return;
    }

    // After navigation, clear the pending route to ensure one-time use
    await RoutePersistence.clearPendingRoute();

    // Process the deep link
    log('Navigating to: $deepLink');
    final parsedRoute = _parseRouteFromDeepLink(deepLink);
    // ignore: use_build_context_synchronously
    _routerKey.currentState!.context.go(parsedRoute);
  }

  Future forgroundMessage() async {
    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
  }

  void cancelAllOperations() {
    log("Cancelling all notification services...");

    // Cancel Firebase listeners
    _onMessageSubscription?.cancel();
    _onMessageOpenedAppSubscription?.cancel();

    // Reset badge count
    AppBadgePlus.updateBadge(0);

    // Cancel all scheduled notifications
    flutterLocalNotificationsPlugin.cancelAll();

    // Clear stored FCM token
    token = null;
    // refreshToken = null;
    isUserLoggedIn = false;
  }

  Future<void> checkLoginStatusAndInit(BuildContext context) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? savedToken = prefs.getString('jwttoken');
    log('Saved Token : $savedToken');

    if (savedToken != null && savedToken.isNotEmpty) {
      isUserLoggedIn = true;
      if (context.mounted) {
        firebaseInit(context);
        setupInteractMessage(context);
      }
      requestNotificationPermission();
    } else {
      cancelAllOperations();
    }
  }

  String _parseRouteFromDeepLink(String deeplink) {
    log("Deep Link 111: $deeplink");
    Uri uri = Uri.parse(deeplink);
    log("URIIIII : $uri");

    String? clubId = uri.queryParameters['bookClubId'];
    String? userId = uri.queryParameters['userId'];

    // More robust route parsing
    if (uri.path.contains('ManageIncomeRequest')) {
      return '/Clubs/user-club-details/clubsScreen4/ManageIncomeRequest?bookClubId=${clubId ?? ''}&userId=${userId ?? ''}';
    } else if (uri.path.contains('user-club-details')) {
      return '/Clubs/user-club-details?bookClubId=${clubId ?? ''}&userId=${userId ?? ''}';
    } else if (uri.path.contains('club-invitations')) {
      return '/Clubs/club-invitations?userId=${userId ?? ''}';
    } else if (uri.path.contains('chat-screen')) {
      return '/chat-screen?userId=$userId&bookClubId=$clubId';
    }

    return deeplink;
  }
}
