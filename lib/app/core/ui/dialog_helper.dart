import 'dart:developer';

import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../models/club_charter_model.dart';
import '../../../reusableWidgets/customDialouge_with_message.dart';
import '../constants.dart';
import '../utils/text_style.dart';

class DialogHelper {
  static Future<void> topShelfandAlreadyBookAddFunction(
      BuildContext context, String title, String description) async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          description,
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () async {
                    // final bookId = currentbookCaseList?[index].bookId;
                    // await deleteBook(bookId).then(
                    //   (value) {},
                    // );
                    // setState(() {});
                    if (context.mounted) {
                      context.pop();
                    }
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  static void showDurationValidationDialog(BuildContext context) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return CustomDialog(
          title: "Add meeting:",
          message:
              "Date, time and duration should either be marked as 'TBD' or have specific values assigned",
          showDoneImage: false,
        );
      },
    );
  }

  static Future<void> showInfoDialog({
    required BuildContext context,
    required String title,
    required String subTitle,
    required List<ClubCharterModel> infoList,
    required bool isHide,
    String canEditText = "(You can edit this later)",
  }) async {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(20),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30.0),
                  child: Center(
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      // overflow: TextOverflow.ellipsis,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 3),
                isHide
                    ? Center(
                        child: Text(
                          canEditText, //"(You can edit this later)",
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                          ),
                        ),
                      )
                    : const SizedBox.shrink(),
                const SizedBox(height: 23),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Text(
                    subTitle,
                    textAlign: TextAlign.start,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Column(
                  children: infoList.map((e) {
                    return Padding(
                      padding: const EdgeInsets.only(left: 35, right: 20),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 3.0),
                            child: Text(
                              "•",
                              textAlign: TextAlign.start,
                              style: lbRegular.copyWith(
                                fontSize: 15,
                                height: 0.8,
                              ),
                            ),
                          ),
                          const SizedBox(width: 5),
                          Expanded(
                            child: Text(
                              e.rules ?? '',
                              textAlign: TextAlign.start,
                              style: lbRegular.copyWith(
                                fontSize: 12,
                                height: 1.5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 25),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        context.pop();
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 3.5,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(49),
                          color: AppConstants.textGreenColor,
                        ),
                        child: Center(
                          child: Text(
                            "Ok",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 30),
              ],
            ),
          ],
        );
      },
    );
  }

  static Future<void> showClubCharterInfo({
    required BuildContext context,
    required String? clubType,
  }) async {
    List<ClubCharterModel> clubCharterInfo;
    String subTitle;

    if (clubType == ClubType.standing.value) {
      clubCharterInfo = [
        ClubCharterModel(
          rules:
              'Genre/Themes/Authors of books (For eg: 70s Sci-fi books turned into movies, Reese Witherspoon book club books, Stephen King books, Civil War Historical Fiction)',
        ),
        ClubCharterModel(
          rules: 'Typical meeting frequency, day of the week, and time.',
        ),
        ClubCharterModel(rules: 'Membership cap: Eg 7 members only.'),
        ClubCharterModel(rules: 'If the discussion leader rotates.'),
        ClubCharterModel(
          rules: 'Who is responsible for book selection (Eg: it rotates).',
        ),
      ];
      subTitle = "Standing club charter includes:";
    } else {
      clubCharterInfo = [
        ClubCharterModel(
          rules: 'How many sessions the book will be discussed over?',
        ),
        ClubCharterModel(rules: 'Membership cap: Eg 7 members only.'),
      ];
      subTitle = "Impromptu club charter includes:";
    }

    await showInfoDialog(
      context: context,
      title: "Club Charter",
      subTitle: subTitle,
      infoList: clubCharterInfo,
      isHide: true,
    );
  }

  static Future<void> showMemberRequestPromptInfo({
    required BuildContext context,
    required String? clubType,
  }) async {
    List<ClubCharterModel> memberRequestPromptInfo =
        clubType == ClubType.standing.value
            ? [
                ClubCharterModel(rules: 'Reading speed.'),
                ClubCharterModel(rules: 'Willing to lead discussion?'),
              ]
            : [
                ClubCharterModel(rules: 'Have they read the book?'),
                ClubCharterModel(
                    rules: 'When do they anticipate finishing the book?'),
              ];

    await showInfoDialog(
      context: context,
      title: "Member Request Prompt",
      subTitle:
          "What do you want to know about people asking to join your club?",
      infoList: memberRequestPromptInfo,
      isHide: true,
    );
  }

  static Future<void> appUpdateDialog(
    BuildContext context, {
    required bool isMandatory,
    required String storeUrl,
  }) async {
    log('app store url : $storeUrl');
    await showDialog(
      barrierColor: Colors.white54,
      barrierDismissible: isMandatory ? false : true,
      context: context,
      builder: (context) => AlertDialog(
        insetPadding: EdgeInsets.symmetric(horizontal: 20),
        backgroundColor: AppConstants.backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: AppConstants.primaryColor),
        ),
        titlePadding: EdgeInsets.only(top: 10, right: 10),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            GestureDetector(
              onTap: () => isMandatory ? SystemNavigator.pop() : context.pop(),
              child: Image.asset(
                AppConstants.closePopupImagePath,
                alignment: Alignment.centerRight,
                height: 30,
                width: 30,
              ),
            ),
            Text(
              'New update available',
              textAlign: TextAlign.center,
              style: lbRegular.copyWith(fontSize: 18),
            ),
          ],
        ),
        content: Text(
          isMandatory
              ? 'We’ve made some important improvements. Please update the app to continue using it.'
              : 'A new version of the app is available. Update now to enjoy the latest features and improvements.',
          textAlign: TextAlign.center,
          style: lbRegular.copyWith(fontSize: 18),
        ),
        actionsAlignment: MainAxisAlignment.center,
        actions: [
          TextButton(
            onPressed: () async {
              try {
                final launched = await launchUrl(
                  Uri.parse(storeUrl),
                  mode: LaunchMode.externalApplication,
                );
                if (!launched) {
                  log('Failed to launch store URL');
                }
              } catch (e) {
                log('error : $e');
              }
            },
            style: TextButton.styleFrom(
              backgroundColor: AppConstants.textGreenColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50),
              ),
              padding: EdgeInsets.symmetric(vertical: 12, horizontal: 25),
            ),
            child: Text(
              'Update',
              style: lbBold.copyWith(fontSize: 18),
            ),
          ),
          const SizedBox(width: 10),
          TextButton(
            onPressed: () =>
                isMandatory ? SystemNavigator.pop() : context.pop(),
            style: TextButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50),
                side: BorderSide(color: AppConstants.primaryColor),
              ),
              padding: EdgeInsets.symmetric(vertical: 12, horizontal: 30),
            ),
            child: Text(
              isMandatory ? 'Exit' : 'Cancel',
              style: lbBold.copyWith(fontSize: 18),
            ),
          ),
        ],
      ),
    );
  }

  static void inValidDeepLink(BuildContext context, String message,
      {Function? onDialogClosed}) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(20),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                    if (onDialogClosed != null) {
                      onDialogClosed();
                    }
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30.0),
                  child: Center(
                    child: Text(
                      message,
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        context.pop();
                        if (onDialogClosed != null) {
                          onDialogClosed();
                        }
                      },
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(vertical: 12, horizontal: 45),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(49),
                          color: AppConstants.textGreenColor,
                        ),
                        child: Center(
                          child: Text(
                            "Ok",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 30),
              ],
            ),
          ],
        );
      },
    );
  }
}
