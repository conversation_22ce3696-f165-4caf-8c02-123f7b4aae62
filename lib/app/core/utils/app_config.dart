import 'package:flutter/material.dart';

enum Flavor { prod, dev }

class AppConfig {
  final Flavor flavor;
  final String appName;
  final String baseUrl;
  final MaterialColor primaryColor;

  static late AppConfig shared;

  factory AppConfig({
    required Flavor flavor,
    required String appName,
    required String baseUrl,
    MaterialColor primaryColor = Colors.blue,
  }) {
    shared = AppConfig._internal(
      flavor,
      appName,
      baseUrl,
      primaryColor,
    );
    return shared;
  }

  AppConfig._internal(
    this.flavor,
    this.appName,
    this.baseUrl,
    this.primaryColor,
  );
}
