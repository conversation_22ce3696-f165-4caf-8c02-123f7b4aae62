import 'dart:convert';

import 'package:flutter/services.dart';

class CurrencyHelper {
  static List<dynamic> _currencyData = [];
  static bool _isDataLoaded = false;

  static Future<void> _ensureDataLoaded() async {
    if (!_isDataLoaded) {
      String jsonString =
          await rootBundle.loadString('assets/config/currency_code.json');
      _currencyData = json.decode(jsonString);
      _isDataLoaded = true;
    }
  }

  /// Returns a list of symbols for a given currency code (ISO 4217).
  /// Returns null if the code is not found.
  static Future<List<String>?> getSymbolsFromCode(String currencyCode) async {
    await _ensureDataLoaded();
    for (var currency in _currencyData) {
      if (currency['isoCode'] != null &&
          currency['isoCode'].toString().toUpperCase() ==
              currencyCode.toUpperCase()) {
        return List<String>.from(
            currency['symbol']); // Ensure it's a List<String>
      }
    }
    return null; // Code not found
  }

  /// Returns the ISO 4217 currency code for a given symbol.
  /// Returns null if the symbol is not found.
  /// If multiple currencies use the same symbol, it will return the first one found in the list.
  static Future<String?> getCodeFromSymbol(String currencySymbol) async {
    await _ensureDataLoaded();
    for (var currency in _currencyData) {
      if (currency['symbol'] != null &&
          (currency['symbol'] as List).contains(currencySymbol)) {
        return currency['isoCode'];
      }
    }
    return null; // Symbol not found
  }
}
