import 'dart:io';

class FileUtils {
  static Future<File> renameFile(File originalFile, File croppedFile) async {
    final newPath = originalFile.path.replaceFirst(
      RegExp(r'[^/]+$'), // This will match the file name part
      originalFile.uri.pathSegments.last
          .replaceAll('.jpg', '.png'), // Replacing original name with new one
    );
    return croppedFile.rename(newPath);
  }

  static String? getMimeType(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      default:
        return null;
    }
  }
}
