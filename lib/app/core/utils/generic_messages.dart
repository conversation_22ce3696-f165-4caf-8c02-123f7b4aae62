class GenericMessages {
  static const String recordAdded = "Record added successfully";
  static const String recordUpdated = "Record updated successfully";
  static const String recordDeleted = "Record deleted successfully";
  static const String somethingWrong = "Something went wrong";
  static const String requiredFields = "Please fill all required fields";
  static const String recordNotFound = "No record found";

  static const String inviteRequestSuccess = "Your invitation has been sent!";

  static const String inviteAlreadySent =
      "An invite to join your club has already been sent to this user";

  static const String memberAlreadyExists =
      "Member already exists in book club";
  static const String userRequestAlreadySend =
      "This user has already sent a request to join your club, check clubs incoming requests";
  // static const String requestInactive =
  //     "An outgoing request from this user to join your club is in an inactive state";
  static const String inviteInactive =
      "An outgoing invitation to this user to join your club is in an inactive state, check clubs outgoing invitations";

  static const String requestFirstInviteSecond =
      "This user has already sent a request to join your club, check clubs incoming requests";

  // User Messages
  static const String joinRequestSuccess =
      "Your request to join has been submitted to the club leader";
  static const String requestAlreadySent =
      "A request to join this club has already been sent";
  static const String leaderInviteAlreadySent =
      "You have an invitation from this club, check your incoming club invitations";
  // static const String leaderInviteInactive =
  //     "An outgoing invitation from this club to invite you is currently in an inactive state";
  static const String userRequestInactive =
      "An outgoing request to join this club is currently in an inactive state, check your outgoing club requests";

  static const String inviteFirstRequestSecond =
      'An invitation from this club has been sent to you, check your incoming invitations';
}
