import 'package:url_launcher/url_launcher.dart';

import '../constants.dart';

class LaunchUrl {
  Future privacyPolicy() async {
    if (!await launchUrl(AppConstants.privacyPolicyUrl)) {
      throw Exception('Could not launch : ${AppConstants.privacyPolicyUrl}');
    }
  }

  Future termsAndCondition() async {
    if (!await launchUrl(AppConstants.termsAndConditionUrl)) {
      throw Exception(
          'Could not launch : ${AppConstants.termsAndConditionUrl}');
    }
  }
}
