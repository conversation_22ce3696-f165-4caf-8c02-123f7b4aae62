import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:eljunto/app/core/enums/enums.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SystemUtils {
  static Future<String> getDeviceName() async {
    final prefs = await SharedPreferences.getInstance();
    final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      log('getDeviceName: ${androidInfo.model}-${androidInfo.id} & ${androidInfo.model}');

      await prefs.setString(
          "deviceId", '${androidInfo.model}-${androidInfo.id}');
      await prefs.setString("deviceName", androidInfo.model);
      // deviceId = '${androidInfo.model}-${androidInfo.id}';
      log("DeviceId : ${androidInfo.model}-${androidInfo.id} ");

      return '${androidInfo.model}-${androidInfo.id}';
    } else {
      final iosInfo = await deviceInfo.iosInfo;
      log('getDeviceName: ${iosInfo.identifierForVendor}');
      await prefs.setString("deviceId", iosInfo.identifierForVendor!);
      await prefs.setString("deviceName", iosInfo.model);
      // deviceId = iosInfo.identifierForVendor;
      return iosInfo.identifierForVendor ?? 'iPhone';
    }
  }

  /// Get the system navigation mode (gesture or 3-button)
  static String getSystemNavigationMode(BuildContext context) {
    // On iOS, we always use gesture navigation.
    if (Platform.isIOS) {
      return SystemNavigationMode.gesture.value;
    }

    // On Android, we check the bottom system UI padding.
    // A bottom padding greater than 0 likely indicates the presence of the 3-button navigation bar.
    final bottomPadding = MediaQuery.of(context).viewPadding.bottom;

    // A small threshold can be used for safety, but 0 is usually sufficient.
    if (bottomPadding > 0) {
      return SystemNavigationMode.threeButton.value;
    } else {
      return SystemNavigationMode.gesture.value;
    }
  }
}
