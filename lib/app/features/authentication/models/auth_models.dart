// Authentication Models
// Consolidated authentication data models following established patterns

class LoginRequest {
  final String email;
  final String password;
  final String fcmToken;
  final String deviceId;

  LoginRequest({
    required this.email,
    required this.password,
    required this.fcmToken,
    required this.deviceId,
  });

  Map<String, dynamic> toJson() {
    return {
      'userEmailId': email,
      'userCred': password,
      'fcmToken': fcmToken,
      'deviceId': deviceId,
    };
  }
}

class LoginResponse {
  final bool success;
  final int statusCode;
  final String? token;
  final Map<String, dynamic>? userData;
  final String? message;

  LoginResponse({
    required this.success,
    required this.statusCode,
    this.token,
    this.userData,
    this.message,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      success: json['statusCode'] == 200,
      statusCode: json['statusCode'] ?? 0,
      token: json['data']?['token'],
      userData: json['data'],
      message: json['message'],
    );
  }
}

class SignupRequest {
  final String email;

  SignupRequest({required this.email});

  Map<String, dynamic> toJson() {
    return {'userEmailId': email};
  }
}

class SignupResponse {
  final bool success;
  final int statusCode;
  final String? token;
  final String? message;

  SignupResponse({
    required this.success,
    required this.statusCode,
    this.token,
    this.message,
  });

  factory SignupResponse.fromJson(Map<String, dynamic> json) {
    return SignupResponse(
      success: json['statusCode'] == 200,
      statusCode: json['statusCode'] ?? 0,
      token: json['data']?['token'],
      message: json['message'],
    );
  }
}

class OtpRequest {
  final String email;
  final String token;
  final String otp;
  final bool isForgotPassword;

  OtpRequest({
    required this.email,
    required this.token,
    required this.otp,
    required this.isForgotPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      'userEmailId': email,
      'token': token,
      'passcode': otp,
      'flag': isForgotPassword,
    };
  }
}

class OtpResponse {
  final bool success;
  final int statusCode;
  final String? message;
  final Map<String, dynamic>? data;

  OtpResponse({
    required this.success,
    required this.statusCode,
    this.message,
    this.data,
  });

  factory OtpResponse.fromJson(Map<String, dynamic> json) {
    return OtpResponse(
      success: json['statusCode'] == 200,
      statusCode: json['statusCode'] ?? 0,
      message: json['message'],
      data: json['data'],
    );
  }
}

class PasswordSetupRequest {
  final String email;
  final String token;
  final String otp;
  final String password;
  final String fcmToken;
  final String deviceId;

  PasswordSetupRequest({
    required this.email,
    required this.token,
    required this.otp,
    required this.password,
    required this.fcmToken,
    required this.deviceId,
  });

  Map<String, dynamic> toJson() {
    return {
      'userEmailId': email,
      'token': token,
      'passcode': otp,
      'userCred': password,
      'fcmToken': fcmToken,
      'deviceId': deviceId,
    };
  }
}

class PasswordResetRequest {
  final String email;
  final String token;
  final String otp;
  final String password;

  PasswordResetRequest({
    required this.email,
    required this.token,
    required this.otp,
    required this.password,
  });

  Map<String, dynamic> toJson() {
    return {
      'userEmailId': email,
      'tokenCred': otp,
      'token': token,
      'userCred': password,
      'confirmUserCred': password,
    };
  }
}

class ProfileSetupRequest {
  final String email;
  final String token;
  final String otp;
  final String userName;
  final String userHandle;
  final String userLocation;
  final String userBio;

  ProfileSetupRequest({
    required this.email,
    required this.token,
    required this.otp,
    required this.userName,
    required this.userHandle,
    required this.userLocation,
    required this.userBio,
  });

  Map<String, dynamic> toJson() {
    return {
      'userEmailId': email,
      'token': token,
      'passcode': otp,
      'userName': userName,
      'userHandle': userHandle,
      'userLocation': userLocation,
      'userBio': userBio,
    };
  }
}

class HandleAvailabilityRequest {
  final String email;
  final String userHandle;

  HandleAvailabilityRequest({
    required this.email,
    required this.userHandle,
  });

  Map<String, dynamic> toJson() {
    return {
      'userEmailId': email,
      'userHandle': userHandle,
    };
  }
}

class AuthResponse {
  final bool success;
  final int statusCode;
  final String? message;
  final Map<String, dynamic>? data;

  AuthResponse({
    required this.success,
    required this.statusCode,
    this.message,
    this.data,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      success: json['statusCode'] == 200,
      statusCode: json['statusCode'] ?? 0,
      message: json['message'],
      data: json['data'],
    );
  }
}

// Authentication state models
enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

enum AuthStep {
  login,
  signup,
  otpVerification,
  passwordSetup,
  profileSetup,
  completed,
}

class AuthData {
  final String? token;
  final String? email;
  final String? fcmToken;
  final String? deviceId;
  final Map<String, dynamic>? userData;

  AuthData({
    this.token,
    this.email,
    this.fcmToken,
    this.deviceId,
    this.userData,
  });

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'email': email,
      'fcmToken': fcmToken,
      'deviceId': deviceId,
      'userData': userData,
    };
  }

  factory AuthData.fromJson(Map<String, dynamic> json) {
    return AuthData(
      token: json['token'],
      email: json['email'],
      fcmToken: json['fcmToken'],
      deviceId: json['deviceId'],
      userData: json['userData'],
    );
  }
}
