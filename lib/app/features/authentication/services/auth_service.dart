import 'dart:developer';
import 'dart:io';
import 'dart:async';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/network/http/http_service.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/features/authentication/models/auth_models.dart';
import 'package:eljunto/app/features/authentication/utils/auth_utils.dart';

/// Consolidated Authentication Service
/// Handles all authentication-related API calls, validation, and storage operations
class AuthService {
  final String _baseURL = ApiConstants.flavorBaseUrl;
  final HttpApiService _apiService = locator<HttpApiService>();

  // API Methods

  /// Login user with email and password
  Future<LoginResponse> login(LoginRequest request) async {
    try {
      log("Login Payload: ${request.toJson()}");
      
      // Encrypt password before sending
      final encryptedRequest = LoginRequest(
        email: request.email,
        password: AuthHelpers.encryptPassword(request.password),
        fcmToken: request.fcmToken,
        deviceId: request.deviceId,
      );

      dio.Response response = await _apiService.post(
        '$_baseURL/auth/signin',
        encryptedRequest.toJson(),
      );

      final loginResponse = LoginResponse.fromJson(response.data);
      
      if (loginResponse.success && loginResponse.userData != null) {
        // Save authentication data locally
        await AuthHelpers.saveAuthData(loginResponse.userData!);
      }

      return loginResponse;
    } on SocketException {
      log("SocketException: Unable to connect to server");
      return LoginResponse(
        success: false,
        statusCode: 0,
        message: 'No internet connection',
      );
    } on TimeoutException {
      log("TimeoutException: Request timeout");
      return LoginResponse(
        success: false,
        statusCode: 0,
        message: 'Request timeout',
      );
    } catch (e) {
      log("Login error: $e");
      return LoginResponse(
        success: false,
        statusCode: 0,
        message: 'Something went wrong',
      );
    }
  }

  /// Verify email for signup
  Future<SignupResponse> verifyEmail(SignupRequest request) async {
    try {
      log("Email verification payload: ${request.toJson()}");

      dio.Response response = await _apiService.post(
        '$_baseURL/auth/email-verification',
        request.toJson(),
      );

      return SignupResponse.fromJson(response.data);
    } on SocketException {
      log("SocketException: Unable to connect to server");
      return SignupResponse(
        success: false,
        statusCode: 0,
        message: 'No internet connection',
      );
    } on TimeoutException {
      log("TimeoutException: Request timeout");
      return SignupResponse(
        success: false,
        statusCode: 0,
        message: 'Request timeout',
      );
    } catch (e) {
      log("Email verification error: $e");
      return SignupResponse(
        success: false,
        statusCode: 0,
        message: 'Something went wrong',
      );
    }
  }

  /// Verify OTP
  Future<OtpResponse> verifyOtp(OtpRequest request) async {
    try {
      log("OTP verification payload: ${request.toJson()}");

      dio.Response response = await _apiService.post(
        '$_baseURL/auth/pass-code-verify',
        request.toJson(),
      );

      return OtpResponse.fromJson(response.data);
    } on SocketException {
      log("SocketException: Unable to connect to server");
      return OtpResponse(
        success: false,
        statusCode: 0,
        message: 'No internet connection',
      );
    } on TimeoutException {
      log("TimeoutException: Request timeout");
      return OtpResponse(
        success: false,
        statusCode: 0,
        message: 'Request timeout',
      );
    } catch (e) {
      log("OTP verification error: $e");
      return OtpResponse(
        success: false,
        statusCode: 0,
        message: 'Something went wrong',
      );
    }
  }

  /// Set password for new user
  Future<AuthResponse> setPassword(PasswordSetupRequest request) async {
    try {
      // Encrypt password before sending
      final encryptedRequest = PasswordSetupRequest(
        email: request.email,
        token: request.token,
        otp: request.otp,
        password: AuthHelpers.encryptPassword(request.password),
        fcmToken: request.fcmToken,
        deviceId: request.deviceId,
      );

      log("Set password payload: ${encryptedRequest.toJson()}");

      dio.Response response = await _apiService.post(
        '$_baseURL/auth/sign-up-set-password',
        encryptedRequest.toJson(),
      );

      final authResponse = AuthResponse.fromJson(response.data);
      
      if (authResponse.success && authResponse.data != null) {
        // Save authentication data locally
        await AuthHelpers.saveAuthData(authResponse.data!);
      }

      return authResponse;
    } on SocketException {
      log("SocketException: Unable to connect to server");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'No internet connection',
      );
    } on TimeoutException {
      log("TimeoutException: Request timeout");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'Request timeout',
      );
    } catch (e) {
      log("Set password error: $e");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'Something went wrong',
      );
    }
  }

  /// Reset password
  Future<AuthResponse> resetPassword(PasswordResetRequest request) async {
    try {
      // Encrypt password before sending
      final encryptedRequest = PasswordResetRequest(
        email: request.email,
        token: request.token,
        otp: request.otp,
        password: AuthHelpers.encryptPassword(request.password),
      );

      log("Reset password payload: ${encryptedRequest.toJson()}");

      dio.Response response = await _apiService.post(
        '$_baseURL/auth/reset-password',
        encryptedRequest.toJson(),
      );

      return AuthResponse.fromJson(response.data);
    } on SocketException {
      log("SocketException: Unable to connect to server");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'No internet connection',
      );
    } on TimeoutException {
      log("TimeoutException: Request timeout");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'Request timeout',
      );
    } catch (e) {
      log("Reset password error: $e");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'Something went wrong',
      );
    }
  }

  /// Set user name and handle
  Future<AuthResponse> setNameAndHandle(ProfileSetupRequest request) async {
    try {
      log("Profile setup payload: ${request.toJson()}");

      dio.Response response = await _apiService.post(
        '$_baseURL/auth/signup-name-handle',
        request.toJson(),
      );

      return AuthResponse.fromJson(response.data);
    } on SocketException {
      log("SocketException: Unable to connect to server");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'No internet connection',
      );
    } on TimeoutException {
      log("TimeoutException: Request timeout");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'Request timeout',
      );
    } catch (e) {
      log("Profile setup error: $e");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'Something went wrong',
      );
    }
  }

  /// Check handle availability
  Future<AuthResponse> checkHandleAvailability(HandleAvailabilityRequest request) async {
    try {
      log("Handle availability payload: ${request.toJson()}");

      dio.Response response = await _apiService.post(
        '$_baseURL/auth/check-available-handle',
        request.toJson(),
      );

      return AuthResponse.fromJson(response.data);
    } on SocketException {
      log("SocketException: Unable to connect to server");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'No internet connection',
      );
    } on TimeoutException {
      log("TimeoutException: Request timeout");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'Request timeout',
      );
    } catch (e) {
      log("Handle availability error: $e");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'Something went wrong',
      );
    }
  }
}
