import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/core/widgets/custom_scaffold.dart';
import 'package:eljunto/app/features/authentication/providers/auth_provider.dart';
import 'package:eljunto/app/features/authentication/utils/auth_utils.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_button.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_footer.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_form_field.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_header.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

/// Enhanced Login Screen following new architecture patterns
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Initialize the auth provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthProvider>().initialize();
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  void _clearValidationMessage() {
    context.read<AuthProvider>().clearError();
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    _clearValidationMessage();

    final success = await context.read<AuthProvider>().login(
          _emailController.text,
          _passwordController.text,
          context,
        );

    if (success && mounted) {
      // Navigation is handled by the redirect logic in the router
      context.go('/Home');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Stack(
          children: [
            CustomScaffold(
              backgroundColor: Colors.transparent,
              appBar: const AuthHeader(),
              body: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            const SizedBox(height: 24),

                            // Title
                            const AuthTitle(title: 'Welcome Back'),

                            const SizedBox(height: 36),

                            // Email Field
                            AuthFormField(
                              controller: _emailController,
                              focusNode: _emailFocusNode,
                              hintText: 'Enter your email',
                              labelText: 'Email',
                              keyboardType: TextInputType.emailAddress,
                              isEnabled: authProvider.isFieldEnabled,
                              textCapitalization: TextCapitalization.none,
                              autoValidateMode:
                                  AutovalidateMode.onUserInteraction,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your email';
                                }
                                if (!AuthValidators.validateEmail(value)) {
                                  return 'Please enter a valid email';
                                }
                                return null;
                              },
                              onChanged: (_) => _clearValidationMessage(),
                            ),

                            const SizedBox(height: 20),

                            // Password Field
                            AuthFormField(
                              controller: _passwordController,
                              focusNode: _passwordFocusNode,
                              hintText: 'Enter your password',
                              labelText: 'Password',
                              isPassword: true,
                              isEnabled: authProvider.isFieldEnabled,
                              autoValidateMode:
                                  AutovalidateMode.onUserInteraction,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your password';
                                } else if (authProvider
                                    .errorMessage.isNotEmpty) {
                                  return "* ${authProvider.errorMessage}";
                                }
                                return null;
                              },
                              onChanged: (_) => _clearValidationMessage(),
                            ),

                            const SizedBox(height: 20),

                            // Login Button
                            AuthButton(
                              text: 'Login',
                              height: 40,
                              borderRadius: 36,
                              onPressed: authProvider.isFieldEnabled
                                  ? _handleLogin
                                  : null,
                              isLoading: authProvider.loginLoading,
                              isEnabled: authProvider.isFieldEnabled,
                            ),

                            const SizedBox(height: 20),

                            // Forgot Password Link
                            Align(
                              alignment: Alignment.center,
                              child: AuthTextButton(
                                text: 'Forgot Password?',
                                onPressed: authProvider.isFieldEnabled
                                    ? () => context.pushNamed(
                                          'set-password',
                                          extra: {
                                            "isForgotPassword": true,
                                            "email":
                                                _emailController.text.trim(),
                                          },
                                        )
                                    : null,
                                textColor: AppConstants.textGreenColor,
                                textStyle: lbBold.copyWith(
                                  fontSize: 14,
                                  color: AppConstants.textGreenColor,
                                ),
                              ),
                            ),

                            const SizedBox(height: 30),

                            // Sign Up Link
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  "Don't have an account? ",
                                  style: lbRegular.copyWith(
                                    fontSize: 16,
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                                AuthTextButton(
                                  text: 'Sign Up',
                                  onPressed: authProvider.isFieldEnabled
                                      ? () => context.pushNamed('sign-up')
                                      : null,
                                  textColor: AppConstants.primaryColor,
                                  textStyle: lbBold.copyWith(
                                    fontSize: 14,
                                    color: AppConstants.textGreenColor,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Contact Us
                  const AuthFooter(),
                ],
              ),
            ),

            // No Connection Tag
            const NoConnectionTag(bottomPosition: 70),
          ],
        );
      },
    );
  }
}
