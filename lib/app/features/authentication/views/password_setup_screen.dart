import 'package:eljunto/app/core/widgets/custom_scaffold.dart';
import 'package:eljunto/app/features/authentication/providers/auth_provider.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_button.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_footer.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_form_field.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_header.dart';
import 'package:eljunto/app/features/authentication/views/widgets/validation_display.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

/// Enhanced Password Setup Screen following new architecture patterns
class PasswordSetupScreen extends StatefulWidget {
  final String email;
  final String token;
  final String otp;
  final bool isForgotPassword;

  const PasswordSetupScreen({
    super.key,
    required this.email,
    required this.token,
    required this.otp,
    this.isForgotPassword = false,
  });

  @override
  State<PasswordSetupScreen> createState() => _PasswordSetupScreenState();
}

class _PasswordSetupScreenState extends State<PasswordSetupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _passwordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    super.dispose();
  }

  void _clearValidationMessage() {
    context.read<AuthProvider>().clearError();
  }

  Future<void> _handleSetPassword() async {
    if (!_formKey.currentState!.validate()) return;

    await context.read<AuthProvider>().setPassword(
          widget.email,
          widget.token,
          widget.otp,
          _passwordController.text,
          _confirmPasswordController.text,
          widget.isForgotPassword,
          context,
        );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Stack(
          children: [
            CustomScaffold(
              backgroundColor: Colors.transparent,
              appBar: AuthHeader(
                onBackPressed: () => context.pop(),
              ),
              body: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            const SizedBox(height: 24),

                            // Title
                            AuthTitle(
                              title: widget.isForgotPassword
                                  ? 'Reset your password'
                                  : 'Set your password',
                            ),

                            const SizedBox(height: 36),

                            // Password Field
                            AuthFormField(
                              controller: _passwordController,
                              focusNode: _passwordFocusNode,
                              hintText: 'Enter your password',
                              labelText: 'Password',
                              isPassword: true,
                              isEnabled: authProvider.isFieldEnabled,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter a password';
                                }
                                return null;
                              },
                              autoValidateMode:
                                  AutovalidateMode.onUserInteraction,
                              onChanged: (value) {
                                _clearValidationMessage();
                                authProvider.validatePassword(value);
                                if (_confirmPasswordController
                                    .text.isNotEmpty) {
                                  authProvider.validateConfirmPassword(
                                    _confirmPasswordController.text,
                                    value,
                                  );
                                }
                              },
                            ),

                            // Password Strength Indicator
                            PasswordStrengthIndicator(
                              isPasswordValid: authProvider.isPasswordValid,
                              isPasswordComplex: authProvider.isPasswordComplex,
                              showValidation: true,
                            ),

                            const SizedBox(height: 20),

                            // Confirm Password Field
                            AuthFormField(
                              controller: _confirmPasswordController,
                              focusNode: _confirmPasswordFocusNode,
                              hintText: 'Confirm your password',
                              labelText: 'Confirm Password',
                              isPassword: true,
                              isEnabled: authProvider.isFieldEnabled,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please confirm your password';
                                }
                                if (value != _passwordController.text) {
                                  return 'Passwords do not match';
                                }
                                if (authProvider.errorMessage.isNotEmpty) {
                                  return authProvider.errorMessage;
                                }
                                return null;
                              },
                              onChanged: (value) {
                                _clearValidationMessage();
                                authProvider.validateConfirmPassword(
                                  value,
                                  _passwordController.text,
                                );
                              },
                            ),

                            const SizedBox(height: 25),

                            // Set Password Button
                            AuthButton(
                              text: widget.isForgotPassword
                                  ? 'Reset Password'
                                  : 'Set Password',
                              onPressed: authProvider.isFieldEnabled
                                  ? _handleSetPassword
                                  : null,
                              isLoading: authProvider.passwordLoading,
                              isEnabled: authProvider.isFieldEnabled,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Contact Us
                  const AuthFooter(),
                ],
              ),
            ),

            // No Connection Tag
            const NoConnectionTag(bottomPosition: 70),
          ],
        );
      },
    );
  }
}
