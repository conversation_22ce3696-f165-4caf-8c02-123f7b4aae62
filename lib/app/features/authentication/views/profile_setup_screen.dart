import 'dart:developer';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/widgets/custom_scaffold.dart';
import 'package:eljunto/app/features/authentication/providers/auth_provider.dart';
import 'package:eljunto/app/features/authentication/utils/auth_utils.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_button.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_footer.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_form_field.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_header.dart';
import 'package:eljunto/app/features/authentication/views/widgets/validation_display.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/utils/text_style.dart';

/// Enhanced Profile Setup Screen following new architecture patterns
class ProfileSetupScreen extends StatefulWidget {
  final String email;
  final String token;
  final String otp;

  const ProfileSetupScreen({
    super.key,
    required this.email,
    required this.token,
    required this.otp,
  });

  @override
  State<ProfileSetupScreen> createState() => _ProfileSetupScreenState();
}

class _ProfileSetupScreenState extends State<ProfileSetupScreen> {
  final _nameFormKey = GlobalKey<FormState>();
  final _handleFormKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _locationController = TextEditingController();
  final _bioController = TextEditingController();
  final _handleController = TextEditingController();

  final _nameFocusNode = FocusNode();
  final _locationFocusNode = FocusNode();
  final _bioFocusNode = FocusNode();
  final _handleFocusNode = FocusNode();

  int _currentPage = 0;

  @override
  void initState() {
    log('user email: ${widget.email}');
    log('user otp: ${widget.otp}');
    log('user token: ${widget.token}');
    super.initState();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _locationController.dispose();
    _bioController.dispose();
    _handleController.dispose();
    _nameFocusNode.dispose();
    _locationFocusNode.dispose();
    _bioFocusNode.dispose();
    _handleFocusNode.dispose();
    super.dispose();
  }

  void _clearValidationMessage() {
    context.read<AuthProvider>().clearError();
  }

  void _nextPage() {
    if (_currentPage == 0) {
      // Force validation of all fields
      bool isNameValid = _nameFormKey.currentState?.validate() ?? false;
      bool isLocationValid = _locationController.text.trim().isNotEmpty;
      bool isBioValid = _bioController.text.trim().isNotEmpty;

      context.read<AuthProvider>().submitName(
            _nameController.text,
            _locationController.text,
            _bioController.text,
            _nameFormKey,
          );

      // Only proceed if all validations pass
      if (isNameValid && isLocationValid && isBioValid) {
        setState(() {
          _currentPage = 1;
        });
      }
    }
  }

  void _previousPage() {
    setState(() {
      _currentPage = 0;
    });
  }

  Future<void> _handleSubmit() async {
    await context.read<AuthProvider>().submitHandle(
          _nameController.text,
          _locationController.text,
          _bioController.text,
          _handleController.text,
          widget.email,
          widget.token,
          widget.otp,
          _handleFormKey,
          context,
        );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Stack(
          children: [
            CustomScaffold(
              backgroundColor: Colors.transparent,
              appBar: AuthHeader(),
              body: Column(
                children: [
                  Expanded(
                    child: _currentPage == 0
                        ? _buildNamePage(authProvider)
                        : _buildHandlePage(authProvider),
                  ),

                  // Contact Us
                  const AuthFooter(),
                ],
              ),
            ),

            // No Connection Tag
            const NoConnectionTag(bottomPosition: 70),
          ],
        );
      },
    );
  }

  Widget _buildNamePage(AuthProvider authProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Form(
        key: _nameFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: 24),

            // Title
            const AuthTitle(title: 'Tell us about yourself'),

            const SizedBox(height: 36),

            // Name Field
            AuthFormField(
              controller: _nameController,
              focusNode: _nameFocusNode,
              hintText: 'Enter your name',
              labelText: 'Name',
              textCapitalization: TextCapitalization.words,
              isEnabled: authProvider.isFieldEnabled,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter your name';
                }
                if (!AuthValidators.validateUserName(value)) {
                  return 'Name must be at least 2 characters';
                }
                return null;
              },
              onChanged: (_) => _clearValidationMessage(),
            ),

            ValidationDisplay(
              message: '* Enter name',
              isVisible: authProvider.nameFlag,
            ),

            const SizedBox(height: 20),

            // Location Field
            AuthFormField(
              controller: _locationController,
              focusNode: _locationFocusNode,
              hintText: 'Enter your location',
              labelText: 'Location',
              textCapitalization: TextCapitalization.words,
              isEnabled: authProvider.isFieldEnabled,
              onChanged: (_) => _clearValidationMessage(),
            ),

            ValidationDisplay(
              message: '* Enter location',
              isVisible: authProvider.locationFlag,
            ),

            const SizedBox(height: 20),

            // Bio Field
            AuthFormField(
              controller: _bioController,
              focusNode: _bioFocusNode,
              hintText: 'Tell us about yourself',
              labelText: 'Bio',
              maxLines: 4,
              maxLength: 150,
              textCapitalization: TextCapitalization.sentences,
              isEnabled: authProvider.isFieldEnabled,
              onChanged: (_) => _clearValidationMessage(),
            ),

            ValidationDisplay(
              message: '* Enter bio',
              isVisible: authProvider.bioFlag,
            ),

            const SizedBox(height: 40),

            // Next Button
            AuthButton(
              text: 'Next',
              onPressed: (!authProvider.nameFlag &&
                      !authProvider.locationFlag &&
                      !authProvider.bioFlag &&
                      _nameController.text.isNotEmpty &&
                      _locationController.text.isNotEmpty &&
                      _bioController.text.isNotEmpty)
                  ? _nextPage
                  : null,
              isLoading: authProvider.nameLoading,
              isEnabled: authProvider.isFieldEnabled,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHandlePage(AuthProvider authProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Form(
        key: _handleFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: 24),

            // Title
            const AuthTitle(title: 'Choose your handle'),

            const SizedBox(height: 36),

            // Handle Field
            AuthFormField(
              controller: _handleController,
              focusNode: _handleFocusNode,
              hintText: '',
              labelText: 'Handle',
              prefixIcon: Text(
                '@',
                style: lbBold.copyWith(
                  fontSize: 16,
                  color: AppConstants.primaryColor,
                ),
              ),
              maxLength: 15,
              isEnabled: authProvider.isFieldEnabled,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a handle';
                }
                if (!AuthValidators.validateUserHandle(value)) {
                  return 'Handle must be at least 3 characters and contain only letters, numbers, and underscores';
                }
                if (!authProvider.isHandlerAvailable) {
                  return authProvider.errorMessage;
                }
                return null;
              },
              onChanged: (value) async {
                _clearValidationMessage();
                if (value.length >= 3) {
                  log('user email: ${widget.email}');
                  await authProvider.checkHandleAvailability(
                      widget.email, value);
                }
              },
            ),

            // Handle Validation
            if (authProvider.handlerFlag)
              ValidationDisplay(
                message: authProvider.isHandlerAvailable
                    ? '*Enter handle'
                    : '*Handle not available',
              )
            else if (_handleController.text.length >= 3 &&
                authProvider.isHandlerAvailable)
              ValidationDisplay(
                message: 'Handle is available!',
                textColor: Colors.green,
                icon: Icons.check_circle,
              ),

            const SizedBox(height: 25),

            // Error Message
            ValidationDisplay(
              message: authProvider.errorMessage,
              isVisible: authProvider.errorMessage.isNotEmpty,
            ),

            const SizedBox(height: 25),

            // Complete Button
            AuthButton(
              text: 'Complete Setup',
              onPressed: authProvider.isFieldEnabled ? _handleSubmit : null,
              isLoading: authProvider.handleLoading,
              isEnabled: authProvider.isFieldEnabled,
            ),
          ],
        ),
      ),
    );
  }
}
