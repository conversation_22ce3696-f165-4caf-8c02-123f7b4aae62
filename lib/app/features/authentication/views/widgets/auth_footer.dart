import 'package:flutter/material.dart';

import '../../../../../reusableWidgets/contact_us.dart';
import '../../../../core/constants.dart';

class AuthFooter extends StatelessWidget {
  const AuthFooter({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.transparent,
      ),
      padding: EdgeInsets.only(bottom: kToolbarHeight, top: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        spacing: 20,
        children: [
          Divider(
            thickness: 2.0,
            color: AppConstants.primaryColor,
          ),
          const ContactUs(),
        ],
      ),
    );
  }
}
