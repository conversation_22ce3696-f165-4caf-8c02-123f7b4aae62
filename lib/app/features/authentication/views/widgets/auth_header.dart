import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/reusableWidgets/version_display.dart';
import 'package:flutter/material.dart';

/// Common Authentication Header Widget
class AuthHeader extends StatelessWidget implements PreferredSizeWidget {
  final double toolbarHeight;
  final VoidCallback? onBackPressed;

  const AuthHeader({
    super.key,
    this.toolbarHeight = 140,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      toolbarHeight: toolbarHeight,
      shape: const Border(
        bottom: BorderSide(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      centerTitle: true,
      title: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            AppConstants.elJuntoLogo,
            height: 100,
            width: 80,
            filterQuality: FilterQuality.high,
            fit: BoxFit.contain,
          ),
          const VersionDisplay(),
          const SizedBox(height: 25),
        ],
      ),
      automaticallyImplyLeading: false,
      leading: null,
      backgroundColor: AppConstants.textGreenColor,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(toolbarHeight);
}

/// Authentication Screen Title Widget
class AuthTitle extends StatelessWidget {
  final String title;
  final String? subtitle;
  final TextAlign textAlign;
  final EdgeInsetsGeometry? padding;

  const AuthTitle({
    super.key,
    required this.title,
    this.subtitle,
    this.textAlign = TextAlign.center,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Text(
            title,
            style: lbRegular.copyWith(
              fontSize: 20,
              color: AppConstants.primaryColor,
            ),
            textAlign: textAlign,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 8),
            Text(
              subtitle!,
              style: lbRegular.copyWith(
                fontSize: 16,
                color: AppConstants.primaryColor,
              ),
              textAlign: textAlign,
            ),
          ],
        ],
      ),
    );
  }
}

/// Authentication Progress Indicator
class AuthProgressIndicator extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final Color? activeColor;
  final Color? inactiveColor;

  const AuthProgressIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    this.activeColor,
    this.inactiveColor,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: List.generate(totalSteps, (index) {
          final isActive = index < currentStep;
          return Expanded(
            child: Container(
              margin: EdgeInsets.only(
                right: index < totalSteps - 1 ? 8 : 0,
              ),
              height: 4,
              decoration: BoxDecoration(
                color: isActive
                    ? (activeColor ?? AppConstants.primaryColor)
                    : (inactiveColor ??
                        AppConstants.primaryColor.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        }),
      ),
    );
  }
}
