import 'package:eljunto/app/core/constants.dart';
import 'package:flutter/material.dart';

BottomNavigationBarItem buildBottomNavBarItem({
  required String label,
  required String iconPath,
  required String activeIconPath,
  required bool isSelected,
  bool hasNotification = false,
  double iconHeight = 28,
  double iconWidth = 38,
}) {
  return BottomNavigationBarItem(
    label: label,
    icon: Stack(
      clipBehavior: Clip.none,
      children: [
        Image.asset(
          isSelected ? activeIconPath : iconPath,
          height: iconHeight,
          width: iconWidth,
          filterQuality: FilterQuality.high,
        ),
        if (hasNotification)
          Positioned(
            top: -6,
            right: 0,
            child: Image.asset(
              AppConstants.notificationImagePath,
              height: 15,
              width: 15,
              filterQuality: FilterQuality.high,
              fit: BoxFit.cover,
            ),
          ),
      ],
    ),
  );
}
