import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/navigation/navigation_provider.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/controller/session_manager.dart';
import 'package:eljunto/reusable_api_function/club/club_function.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import 'nav_bar_item.dart';

class NavigationShell extends StatelessWidget {
  final StatefulNavigationShell navigationShell;

  const NavigationShell({super.key, required this.navigationShell});

  void _onTap(int index) {
    navigationShell.goBranch(
      index,
      initialLocation: index == navigationShell.currentIndex,
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => NavigationProvider(
        context.read<MessageController>(),
        context.read<BookClubController>(),
        context.read<ClubController>(),
        locator<SessionManager>(),
      ),
      child: Consumer<NavigationProvider>(
        builder: (context, notifier, child) {
          return Scaffold(
            body: navigationShell,
            bottomNavigationBar: Container(
              height: 100,
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(
                    width: 1.5,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
              child: Theme(
                data: ThemeData(
                  splashFactory: NoSplash.splashFactory,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                ),
                child: BottomNavigationBar(
                  currentIndex: navigationShell.currentIndex,
                  onTap: _onTap,
                  type: BottomNavigationBarType.fixed,
                  backgroundColor: AppConstants.textGreenColor,
                  selectedItemColor: AppConstants.backgroundColor,
                  unselectedItemColor: AppConstants.primaryColor,
                  selectedLabelStyle: lbRegular.copyWith(fontSize: 12),
                  unselectedLabelStyle: lbRegular.copyWith(
                      fontSize: 12, color: AppConstants.primaryColor),
                  showUnselectedLabels: true,
                  items: [
                    buildBottomNavBarItem(
                      label: "Home",
                      iconPath: "assets/images/bottom_navigation_icon/Home.png",
                      activeIconPath:
                          "assets/images/bottom_navigation_icon/Home Tan.png",
                      isSelected: navigationShell.currentIndex == 0,
                    ),
                    buildBottomNavBarItem(
                      label: "Profile",
                      iconPath:
                          "assets/images/bottom_navigation_icon/Profile.png",
                      activeIconPath:
                          "assets/images/bottom_navigation_icon/Profile Tan.png",
                      isSelected: navigationShell.currentIndex == 1,
                      iconWidth: 31,
                    ),
                    buildBottomNavBarItem(
                      label: "Messages",
                      iconPath:
                          "assets/images/bottom_navigation_icon/Messages.png",
                      activeIconPath:
                          "assets/images/bottom_navigation_icon/Messages Tan.png",
                      isSelected: navigationShell.currentIndex == 2,
                      hasNotification: notifier.hasMessageNotification,
                    ),
                    buildBottomNavBarItem(
                      label: "Clubs",
                      iconPath:
                          "assets/images/bottom_navigation_icon/Clubs.png",
                      activeIconPath:
                          "assets/images/bottom_navigation_icon/Clubs Tan.png",
                      isSelected: navigationShell.currentIndex == 3,
                      hasNotification: notifier.hasClubNotification,
                      iconHeight: 31,
                    ),
                    buildBottomNavBarItem(
                      label: "Search",
                      iconPath:
                          "assets/images/bottom_navigation_icon/Search.png",
                      activeIconPath:
                          "assets/images/bottom_navigation_icon/Search Tan.png",
                      isSelected: navigationShell.currentIndex == 4,
                      iconWidth: 28,
                    ),
                    buildBottomNavBarItem(
                      label: "Settings",
                      iconPath:
                          "assets/images/bottom_navigation_icon/Settings.png",
                      activeIconPath:
                          "assets/images/bottom_navigation_icon/Settings Tan.png",
                      isSelected: navigationShell.currentIndex == 5,
                      iconWidth: 31,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
