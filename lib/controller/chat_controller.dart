import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:eljunto/app/core/helpers/database_helper.dart';
import 'package:eljunto/app/core/routes/app_navigation_keys.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/controller/session_manager.dart';
import 'package:eljunto/models/club_membership_model.dart';
import 'package:eljunto/models/messages/custom_chat_message.dart';
import 'package:eljunto/views/messages/services/chat_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../app/core/constants.dart';
import '../controller/book_club_controller.dart'; // Adjust import as needed

class ChatController with ChangeNotifier {
  final int bookClubId;
  final BookClubController _bookClubController;
  final _chatService = ChatService();
  final MessageController _messageController;
  final String? initialBookClubName;

  ChatController({
    required this.bookClubId,
    required BookClubController bookClubController,
    required MessageController messageController,
    this.initialBookClubName,
  })  : _bookClubController = bookClubController,
        _messageController = messageController;

  // --- State Variables ---
  bool isLoading = true;
  bool isLoadingMore = false;
  List<CustomChatMessage> messages = [];
  List<ClubMembershipModel> members = [];
  CustomChatUser? currentUser;
  int? loggedInUserId;

  // --- Internal Helpers ---
  StreamSubscription? _messageSubscription;
  DocumentSnapshot? _lastDocument;
  static const int _messagesPerPage = 20;
  final _sessionManager = locator<SessionManager>();

  Future<void> initialize() async {
    isLoading = true;

    loggedInUserId = _sessionManager.userId;
    if (loggedInUserId == null) {
      isLoading = false;
      notifyListeners();
      return;
    }
    currentUser = CustomChatUser(id: loggedInUserId.toString());
    final context = AppNavigationKeys.rootNavigatorKey.currentContext!;

    _messageController.markMessageAsSeen(
      bookClubId.toString(),
      bookClubId,
      loggedInUserId!,
    );

    final cachedMemberMaps =
        await DatabaseHelper.instance.getClubMembers(bookClubId);

    if (cachedMemberMaps.isNotEmpty) {
      log('cachedMemberMaps: $cachedMemberMaps');
      members =
          cachedMemberMaps.map((m) => ClubMembershipModel.fromJson(m)).toList();

      await _loadMessagesFromLocalDB();

      isLoading = false;
      notifyListeners();

      _refreshMembersFromNetwork(context);
      _listenForLiveMessages();
    } else {
      await _refreshMembersFromNetwork(context);

      await _loadMessagesFromLocalDB();
      _listenForLiveMessages();

      isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _loadMessagesFromLocalDB() async {
    final localMessagesDynamic = await DatabaseHelper.instance
        .getMessagesByBookClub(
          bookClubId.toString(),
          orderBy: 'DESC',
          limit: _messagesPerPage,
        )
        .timeout(const Duration(seconds: 5));

    if (localMessagesDynamic.isNotEmpty) {
      messages = List<CustomChatMessage>.from(localMessagesDynamic)
          // Reprocess with member data to ensure names/avatars are correct
          .map((msg) => _reprocessMessageWithMemberData(msg))
          .toList();
    }
  }

  Future<void> _refreshMembersFromNetwork(BuildContext context) async {
    final response = await _bookClubController.getBookClubMembers(
      bookClubId,
      context,
    );
    if (response["statusCode"] == 200) {
      final newMembers = (response["data"] as List)
          .map((item) => ClubMembershipModel.fromJson(item))
          .toList();
      // Check if the member list has actually changed before notifying listeners
      if (listEquals(newMembers, members) == false) {
        members = newMembers;
        if (messages.isNotEmpty) {
          messages = messages
              .map((msg) => _reprocessMessageWithMemberData(msg))
              .toList();
        }
        notifyListeners();
      }
    }
  }

  Future<void> _listenForLiveMessages() async {
    _messageSubscription?.cancel();
    _messageSubscription = _chatService
        .getMessagesStream(bookClubId, _messagesPerPage)
        .listen((snapshot) {
      if (snapshot.docs.isNotEmpty) {
        _lastDocument = snapshot.docs.last;
        final newMessages = snapshot.docs.map(_processMessage).toList();

        if (!listEquals(
            newMessages
                .map((e) => e.customMessageProperties?['message_id'])
                .toList(),
            messages
                .map((e) => e.customMessageProperties?['message_id'])
                .toList())) {
          messages = newMessages;
          DatabaseHelper.instance.batchInsertMessages(messages, bookClubId);
          notifyListeners();
        }
      }
    });
  }

  CustomChatMessage _processMessage(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final userId = data['user_id'];

    final member = members.firstWhere(
      (m) => m.userId == userId,
      orElse: () => ClubMembershipModel(userHandle: '...'),
    );
    final Map<String, dynamic> reactions =
        data['reactions'] != null && data['reactions'] is Map
            ? Map<String, dynamic>.from(data['reactions'])
            : {};

    final messageType = data['message_type'] ?? 'text';

    return CustomChatMessage(
      text: data['text'] as String? ?? "",
      user: userId == loggedInUserId
          ? currentUser!
          : CustomChatUser(
              id: userId.toString(),
              firstName: member.userHandle,
              profileImage: member.userProfilePicture != null
                  ? ApiConstants.imageBaseUrl + member.userProfilePicture!
                  : null,
              customUserProperties: {
                "seenBy": data['seenBy'] ?? [],
                "message_type": messageType,
              },
            ),
      createdAt: (data['createdAt'] as Timestamp? ?? Timestamp.now()).toDate(),
      customMessageProperties: {
        "message_id": doc.id,
        "seenBy": data['seenBy'] ?? [],
        "message_type": messageType,
        "reactions": reactions,
      },
    );
  }

  // **FIX FOR THE UNDEFINED METHOD ERROR**
  // This helper method is now defined in the class.
  CustomChatMessage _reprocessMessageWithMemberData(CustomChatMessage msg) {
    // Find the member corresponding to the message's user ID.
    final member = members.firstWhere(
      (m) => m.userId.toString() == msg.user.id,
      orElse: () =>
          ClubMembershipModel(), // Return a default model if not found
    );

    // Update the user's details on the existing CustomChatMessage object.
    if (msg.user.id != loggedInUserId.toString()) {
      msg.user.firstName = member.userHandle;
      msg.user.profileImage = member.userProfilePicture != null
          ? ApiConstants.imageBaseUrl + member.userProfilePicture!
          : null;
    }

    return msg;
  }

  void sendMessage(CustomChatMessage message) {
    // ... (This method remains unchanged)
    final sender = members.firstWhere((m) => m.userId == loggedInUserId);
    final messageData = {
      'text': message.text,
      'user_id': loggedInUserId,
      'user_name': sender.userHandle,
      'profile_image':
          ApiConstants.imageBaseUrl + (sender.userProfilePicture ?? ''),
      'createdAt': FieldValue.serverTimestamp(),
      'seenBy': members
          .map(
            (m) => {'user_id': m.userId, 'isSeen': m.userId == loggedInUserId},
          )
          .toList(),
    };

    // Send the message
    _chatService.sendMessage(bookClubId, messageData);

    // Update message metadata with the current book club name
    String currentBookClubName = "";
    if ((initialBookClubName?.isEmpty ?? false) ||
        initialBookClubName == null) {
      currentBookClubName = members.first.bookClubName!;
    } else {
      currentBookClubName = initialBookClubName!;
    }
    _chatService.updateClubMetadata(
      bookClubId,
      currentBookClubName,
    );

    for (final member in members) {
      if (member.userId != loggedInUserId && member.isMsgNotifyEnabled) {
        _chatService.sendNotification(
          fcmTokens: member.fcmTokens,
          title: "New Message from ${members.first.bookClubName}",
          body: message.text,
          bookClubId: bookClubId.toString(),
          receiverId: member.userId.toString(),
        );
      }
    }
  }

  // **** MISSING METHOD 1: loadMoreMessages ****
  Future<void> loadMoreMessages() async {
    if (isLoadingMore || _lastDocument == null) return;

    isLoadingMore = true;
    notifyListeners();

    try {
      final snapshot = await _chatService.getMoreMessages(
        bookClubId,
        _lastDocument!,
        _messagesPerPage,
      );

      if (snapshot.docs.isNotEmpty) {
        _lastDocument = snapshot.docs.last;
        final newMessages = snapshot.docs.map(_processMessage).toList();
        messages.addAll(newMessages);
      }
    } catch (e) {
      log('Error loading more messages: $e');
    } finally {
      isLoadingMore = false;
      notifyListeners();
    }
  }

  // **** MISSING METHOD 2: addEmojiReaction ****
  Future<void> addEmojiReaction(String messageId, String emoji) async {
    if (loggedInUserId == null) return;

    // Optimistic UI Update
    final messageIndex = messages.indexWhere(
        (m) => m.customMessageProperties?['message_id'] == messageId);
    if (messageIndex != -1) {
      final reactions = messages[messageIndex]
              .customMessageProperties?['reactions'] as Map<String, dynamic>? ??
          {};
      reactions[loggedInUserId.toString()] = emoji;
      messages[messageIndex].customMessageProperties?['reactions'] = reactions;
      notifyListeners();
    }

    // Call the service
    await _chatService.addEmojiReaction(
      bookClubId,
      messageId,
      loggedInUserId.toString(),
      emoji,
    );

    // Send notifications
    final currentUser = members.firstWhere((m) => m.userId == loggedInUserId);
    for (final member in members) {
      if (member.userId != loggedInUserId && member.isMsgNotifyEnabled) {
        _chatService.sendNotification(
          fcmTokens: member.fcmTokens,
          title: "New Reaction in ${members.first.bookClubName}",
          body: "${currentUser.userHandle} reacted with $emoji",
          bookClubId: bookClubId.toString(),
          receiverId: member.userId.toString(),
        );
      }
    }
  }

  // **** MISSING METHOD 3: removeEmojiReaction ****
  Future<void> removeEmojiReaction(String messageId) async {
    if (loggedInUserId == null) return;

    // Optimistic UI Update
    final messageIndex = messages.indexWhere(
        (m) => m.customMessageProperties?['message_id'] == messageId);
    if (messageIndex != -1) {
      final reactions = messages[messageIndex]
              .customMessageProperties?['reactions'] as Map<String, dynamic>? ??
          {};
      reactions.remove(loggedInUserId.toString());
      messages[messageIndex].customMessageProperties?['reactions'] = reactions;
      notifyListeners();
    }

    // Call the service
    await _chatService.removeEmojiReaction(
      bookClubId,
      messageId,
      loggedInUserId.toString(),
    );
  }

  @override
  void dispose() {
    _messageSubscription?.cancel();
    super.dispose();
  }
}
