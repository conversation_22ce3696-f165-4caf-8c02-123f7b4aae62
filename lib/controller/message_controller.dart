import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:eljunto/reusable_api_function/club/club_function.dart';
import 'package:eljunto/services/message_service.dart';
import 'package:eljunto/views/messages/services/firebase_user_club_service.dart';
import 'package:flutter/material.dart';

import '../app/core/helpers/database_helper.dart';

// Best Practice: Use constants for collection names to avoid typos.
class FirestoreCollections {
  static const String clubs = 'Club_Collection';
  static const String users = 'users';
  static const String messages = 'messages';
}

class MessageController with ChangeNotifier {
  String errorMessage = '';
  final MessageService messageService = MessageService();
  StreamSubscription? _groupsStreamSubscription;
  StreamSubscription? _userClubsSubscription;

  Map<int, bool> notificationStatus = {};
  bool hasNewNotification = false;

  List<int> _userBookClubIds = [];
  List<int> get userBookClubIds => _userBookClubIds;

  bool isFirstLoading = true;
  List<DocumentSnapshot> _documentList = [];
  List<DocumentSnapshot> get documentList => _documentList;

  List<Map<String, dynamic>> _userClubsList = [];
  List<Map<String, dynamic>> get userClubsList => _userClubsList;

  final _firebaseUserClubService = FirebaseUserClubService();
  final Set<int> _optimisticallySeenClubIds = {};

  // --- Logic for initializing and chaining streams ---

  Future<void> initializeMessagesData(int loggedInUserId) async {
    isFirstLoading = true;
    notifyListeners();

    await _userClubsSubscription?.cancel();

    _userClubsSubscription =
        _firebaseUserClubService.getUserClubsStream(loggedInUserId).listen(
      (clubsList) {
        _userClubsList = clubsList;
        _userBookClubIds = clubsList
            .map((club) => club['bookClubId'] as int?)
            .where((id) => id != null)
            .cast<int>()
            .toList();

        log("User clubs updated. Found ${_userBookClubIds.length} clubs.");
        _initializeGroupsStream(loggedInUserId);
      },
      onError: (error) {
        log("Error in user clubs stream: $error");
        isFirstLoading = false;
        errorMessage = "Could not load your clubs.";
        notifyListeners();
      },
    );
  }

  void _initializeGroupsStream(int loggedinUserId) {
    _groupsStreamSubscription?.cancel();

    if (_userBookClubIds.isEmpty) {
      isFirstLoading = false;
      _documentList = [];
      notificationStatus.clear();
      hasNewNotification = false;
      unSeenClubMessages(false);
      notifyListeners();
      return;
    }

    final clubIds = _userBookClubIds.length > 30
        ? _userBookClubIds.sublist(0, 30)
        : _userBookClubIds;

    final firestoreStream = FirebaseFirestore.instance
        .collection(FirestoreCollections.clubs)
        .where('bookClubId', whereIn: clubIds)
        .orderBy('createdAt', descending: true)
        .snapshots();

    _groupsStreamSubscription = firestoreStream.listen(
      (snapshot) async {
        _documentList = snapshot.docs;
        log("Club details stream updated with ${_documentList.length} documents.");

        await _updateAllNotificationStatuses(loggedinUserId);

        if (isFirstLoading) {
          isFirstLoading = false;
        }
        notifyListeners();
      },
      onError: (error) {
        log("Error in groups stream: $error");
        isFirstLoading = false;
        errorMessage = "Could not load club messages.";
        notifyListeners();
      },
    );
  }

  /// Fetches seen status for all clubs in parallel and syncs with local DB.
  Future<void> _updateAllNotificationStatuses(int loggedinUserId) async {
    if (_documentList.isEmpty) {
      notificationStatus.clear();
      hasNewNotification = false;
      unSeenClubMessages(false);
      return;
    }

    final tempStatus = <int, bool>{};

    final futures = _documentList.map((doc) async {
      final data = doc.data() as Map<String, dynamic>;
      final clubIdInt = data['bookClubId'] as int;
      final clubIdString = clubIdInt.toString();

      if (_optimisticallySeenClubIds.contains(clubIdInt)) {
        tempStatus[clubIdInt] = true;
        // Remove it from the set so that the *next* stream update will get the real data from Firestore.
        _optimisticallySeenClubIds.remove(clubIdInt);
        return;
      }

      try {
        // **LOGIC RESTORED**: Read from local DB first.
        bool localSeenStatus = await DatabaseHelper.instance
            .getSeenStatus(loggedinUserId, clubIdString);
        tempStatus[clubIdInt] = localSeenStatus; // Optimistic initial state

        final messages = await doc.reference
            .collection(FirestoreCollections.messages)
            .orderBy('createdAt', descending: true)
            .limit(1)
            .get();

        if (messages.docs.isNotEmpty) {
          final latestMessage = messages.docs.first.data();
          final seenByList = latestMessage['seenBy'] as List<dynamic>? ?? [];
          final userSeenData = seenByList.firstWhere(
            (entry) => entry['user_id'] == loggedinUserId,
            orElse: () => null,
          );
          bool firestoreSeenStatus = userSeenData?['isSeen'] ?? false;

          // **LOGIC RESTORED**: Sync local DB if it's out of date with Firestore.
          if (localSeenStatus != firestoreSeenStatus) {
            await DatabaseHelper.instance.insertOrUpdateSeenStatus(
              loggedinUserId,
              clubIdString,
              firestoreSeenStatus,
            );
          }
          tempStatus[clubIdInt] =
              firestoreSeenStatus; // Use definitive Firestore state
        } else {
          tempStatus[clubIdInt] = true;
        }
      } catch (e) {
        debugPrint(
          "Error processing club $clubIdInt for notification status: $e",
        );
        tempStatus[clubIdInt] = true; // Default to seen on error
      }
    }).toList();

    await Future.wait(futures);

    notificationStatus = tempStatus;
    hasNewNotification = notificationStatus.values.any((isSeen) => !isSeen);
    unSeenClubMessages(hasNewNotification);
  }

  Future<void> markMessageAsSeen(
    String clubId,
    int userClubId,
    int loggedinUserId,
  ) async {
    if (notificationStatus[userClubId] == true) return;
    notificationStatus[userClubId] = true;
    _optimisticallySeenClubIds.add(userClubId);

    hasNewNotification = notificationStatus.values.any((isSeen) => !isSeen);
    unSeenClubMessages(hasNewNotification);
    notifyListeners();

    try {
      await DatabaseHelper.instance
          .insertOrUpdateSeenStatus(loggedinUserId, clubId, true);

      final querySnapshot = await FirebaseFirestore.instance
          .collection(FirestoreCollections.clubs)
          .doc(clubId)
          .collection(FirestoreCollections.messages)
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        var latestMessage = querySnapshot.docs.first;
        List<dynamic> seenByList = (latestMessage.data())['seenBy'] ?? [];
        int existingIndex = seenByList
            .indexWhere((entry) => entry['user_id'] == loggedinUserId);

        if (existingIndex != -1) {
          if (seenByList[existingIndex]['isSeen'] == true) return;
          seenByList[existingIndex]['isSeen'] = true;
          seenByList[existingIndex]['timestamp'] =
              DateTime.now().millisecondsSinceEpoch;
        } else {
          seenByList.add({
            'user_id': loggedinUserId,
            'isSeen': true,
            'timestamp': DateTime.now().millisecondsSinceEpoch,
          });
        }
        await latestMessage.reference.update({'seenBy': seenByList});
      }
    } catch (e) {
      debugPrint("Error updating seen status: $e");
      notificationStatus[userClubId] = false;
      _optimisticallySeenClubIds.remove(userClubId);
      notifyListeners();
    }
  }

  void disposeStreams() {
    _groupsStreamSubscription?.cancel();
    _userClubsSubscription?.cancel();
    _groupsStreamSubscription = null;
    _userClubsSubscription = null;
  }

  void logoutUser() {
    disposeStreams();
    _userBookClubIds.clear();
    _userClubsList.clear();
    _documentList.clear();
    hasNewNotification = false;
    isFirstLoading = true;
    notifyListeners();
  }

  // --- Other notification flags (unchanged) ---
  bool _incomingClubInviteFlag = false;
  bool _hasNewOutgoingRequests = false;
  bool _hasNewStandingClubRequests = false;
  bool _hasNewImpromptuClubRequests = false;
  bool _hasUnSeenClubMessages = false;
  bool _manageIncomingRequestFlag = false;

  bool get hasNewClubActivityNotifications => (_incomingClubInviteFlag ||
      _hasNewOutgoingRequests ||
      _hasNewStandingClubRequests ||
      _hasNewImpromptuClubRequests ||
      _manageIncomingRequestFlag);

  bool get isPendingInvitation => _incomingClubInviteFlag;
  bool get isOutgoingRequest => _hasNewOutgoingRequests;
  bool get isUnSeenClubMessages => _hasUnSeenClubMessages;
  bool get manageIncomingRequest => _manageIncomingRequestFlag;
  bool get hasNewStandingClubRequest => _hasNewStandingClubRequests;

  Future<void> incomingClubInvitationStatus(bool status) async {
    _incomingClubInviteFlag = status;
    notifyListeners();
  }

  Future<void> manageIncomingRequestStatus(
    bool status,
    BuildContext context,
  ) async {
    _manageIncomingRequestFlag = status;
    notifyListeners();
  }

  Future<void> updateOutgoingRequests(bool status) async {
    _hasNewOutgoingRequests = status;
    notifyListeners();
  }

  Future<void> updateStandingClubRequests(bool status) async {
    _hasNewStandingClubRequests = status;
    notifyListeners();
  }

  Future<void> updateImpromptuClubRequests(bool status) async {
    _hasNewImpromptuClubRequests = status;
    notifyListeners();
  }

  Future<void> unSeenClubMessages(bool status) async {
    if (_hasUnSeenClubMessages != status) {
      _hasUnSeenClubMessages = status;
      notifyListeners();
    }
  }

  bool isInvalidLink = false;
  Future<void> isInvalidDeeplink(bool value) async {
    isInvalidLink = value;
    notifyListeners();
  }

  void updateClubNotifications(ClubController clubController) {
    // Example logic, adjust as needed:
    bool hasNewStandingRequests = clubController.standingBookClubList
            ?.any((element) => element.incomingRequest == true) ??
        false;
    bool hasNewImpromptuRequests = clubController.impromptuBookClubList
            ?.any((element) => element.incomingRequest == true) ??
        false;

    // Update flags and notify listeners if changed
    if (_hasNewStandingClubRequests != hasNewStandingRequests ||
        _hasNewImpromptuClubRequests != hasNewImpromptuRequests) {
      _hasNewStandingClubRequests = hasNewStandingRequests;
      _hasNewImpromptuClubRequests = hasNewImpromptuRequests;
      notifyListeners();
    }
  }

  bool _isFromChat = false;
  bool get isFromChat => _isFromChat;
  void setIsFromChat(bool value) {
    _isFromChat = value;
    notifyListeners();
  }
}
