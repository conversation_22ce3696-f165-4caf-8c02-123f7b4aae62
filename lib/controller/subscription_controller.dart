import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/controller/session_manager.dart';
import 'package:eljunto/services/subscription_services.dart';
import 'package:flutter/material.dart';

import '../models/subscription_model/apple_purchase_response_model.dart';
import '../models/subscription_model/subscription_model.dart';
import '../models/subscription_model/subscription_purchase_response_model.dart';
import '../models/subscription_model/verify_subscription_model.dart';

class SubscriptionController extends ChangeNotifier {
  final SubscriptionServices subscriptionServices = SubscriptionServices();
  final _sessionManager = locator<SessionManager>();

  SubscriptionModel? subscriptionDetails;
  bool isFreeTraial = false;
  Future<void> getSubscriptionDetails(BuildContext context) async {
    String osType = '';
    if (Platform.isAndroid) {
      osType = 'google';
    } else {
      osType = 'apple';
    }
    try {
      final response =
          await subscriptionServices.getSubscriptionDetails(osType);
      log("Subscription Controller Details : ${response.data}");
      if (response.statusCode == 200) {
        // Fix: Check if response.data is already a Map or needs decoding
        final Map<String, dynamic> jsonData =
            response.data is String ? jsonDecode(response.data) : response.data;

        subscriptionDetails = SubscriptionModel.fromJson(jsonData);
        isFreeTraial = subscriptionDetails?.data?.isFreeTrial ?? false;
        getSubscriptionDetailList();
        log("Subscription Ids Details : ${subscriptionDetails?.data}");
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        subscriptionDetails = null;
        log("Subscription else Details : ${response.data}");
      }
    } catch (e) {
      log("Subscription : ${e.toString()}");
    }
  }

  VerifySubscriptionModel? verifySubscriptionModel;
  Future<bool> isActiveSubscription() async {
    try {
      final response = await subscriptionServices.isActiveSubscription();
      log("Is Active Subscription Details : ${response.data}");
      if (response.statusCode == 200) {
        // Fix: Check if response.data is already a Map or needs decoding
        final Map<String, dynamic> jsonData =
            response.data is String ? jsonDecode(response.data) : response.data;
        log('decode data: $jsonData');
        verifySubscriptionModel = VerifySubscriptionModel.fromJson(jsonData);
        log("Is Active Subscription Details : ${verifySubscriptionModel?.toJson()}");
        final subStatus = verifySubscriptionModel?.data?.usubStatus;
        notifyListeners();
        if (subStatus == 'ACTIVE') {
          return true;
        } else {
          return false;
        }
      } else {
        verifySubscriptionModel = null;
        return false;
      }
    } catch (e) {
      verifySubscriptionModel = null;
      log("Verify Subscription : ${e.toString()}");
      return false;
    }
  }

  bool isPurchaseApp = false;
  Future<bool> purchaseSubscription(SubscriptionResponseModel payload) async {
    try {
      final response =
          await subscriptionServices.googlePlaypurchaseResponse(payload);
      log("Purchase Subscription : ${response.data}");
      if (response.statusCode == 200) {
        isPurchaseApp = true;
        log("Subscription Success : ${response.data}");
      } else {
        isPurchaseApp = false;
        log("Subscription Details : ${response.data}");
      }
    } catch (e) {
      isPurchaseApp = false;
      log("Purchase Subscription : ${e.toString()}");
    }
    return isPurchaseApp;
  }

  bool isApplePurchaseApp = false;
  Future<bool> applePurchaseSubscription(
      ApplePurchaseResponseModel payload) async {
    try {
      final response =
          await subscriptionServices.appStorePurchaseResponse(payload);
      log("Purchase Subscription : ${response.data}");
      if (response.statusCode == 200) {
        isApplePurchaseApp = true;
        log("Subscription Success : ${response.data}");
      } else {
        isApplePurchaseApp = false;
        log("Subscription Details : ${response.data}");
      }
    } catch (e) {
      isApplePurchaseApp = false;
      log("Purchase Subscription : ${e.toString()}");
    }
    return isApplePurchaseApp;
  }

  List<String> _subscriptionDetailList = [];
  List<String> get subscriptionDetailList => _subscriptionDetailList;
  Future<void> getSubscriptionDetailList() async {
    _subscriptionDetailList = subscriptionDetails?.data?.subscriptionDetails
            ?.map((e) => e.productId ?? '')
            .toList() ??
        [];

    notifyListeners();
  }

  bool _verifySubscription = true;
  bool get verifySubscription => _verifySubscription;

  Future<void> updateApiTriggerValue(bool value) async {
    _verifySubscription = value;
    notifyListeners();
  }
}
