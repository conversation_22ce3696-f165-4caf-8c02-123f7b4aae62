import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/controller/session_manager.dart';
import 'package:eljunto/services/user_service.dart';
import 'package:flutter/material.dart';

import '../models/home_model/home_screen1_model/fellow_reader_model.dart';
import '../models/user_model.dart';

class UserController with ChangeNotifier {
  final UserService userService = UserService();
  String errorMessage = '';
  UserModel userModel = UserModel();
  bool isUserLoading = false;
  final _sessionManager = locator<SessionManager>();

  Future<void> getUserDetailsByUserId(int userId, BuildContext context) async {
    // Map<String, dynamic> responseMap = {};
    log("User ID1 : $userId");
    try {
      isUserLoading = true;
      notifyListeners();
      dio.Response response = await userService.getUserDetailsByUserId(userId);

      if (response.statusCode == 200) {
        final responseMap = response.data;
        userModel = UserModel.fromJson(responseMap);
        notifyListeners();
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        userModel = UserModel();
        final jsonResponse = response.data;
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';
        // errorMessage = 'Something went wrong';
        // responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      userModel = UserModel();
      errorMessage = 'Something went wrong';
      log(errorMessage);
      // responseMap = {"error": errorMessage};
    }
    isUserLoading = false;
    notifyListeners();
    // return userModel;
  }

  bool isFellowReaderLoading = false;
  List<FellowReader> fellowReadList = [];
  int fellowReadersCount = 0;

  Future<void> getFellowReadersByUserId(
      Map<String, dynamic> payload, BuildContext context) async {
    // Map<String, dynamic> responseMap = {};
    isFellowReaderLoading = true;
    notifyListeners();
    try {
      dio.Response response = await userService.getFellowReaders(payload);

      if (response.statusCode == 200) {
        final responseMap = response.data;
        // log("Response FellowReaders : $responseMap");
        if (responseMap.containsKey('count') && responseMap['count'] != null) {
          fellowReadersCount = responseMap['count'];
        }
        if (responseMap.containsKey('data') && responseMap['data'] != null) {
          fellowReadList = List<FellowReader>.from(
            responseMap['data'].map((item) => FellowReader.fromJson(item)),
          );
        }
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        final jsonResponse = response.data;
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';
        // errorMessage = 'Something went wrong';
        // responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      log(errorMessage);
      // responseMap = {"error": errorMessage};
    }
    isFellowReaderLoading = false;
    notifyListeners();
    // return responseMap;
  }

  Future<Map<String, dynamic>> uploadFile(
      File imageFile, int userId, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      final response = await userService.uploadFile(
        imageFile,
        userId,
      );
      log("Upload image Response : $response");
      if (response.statusCode == 200) {
        responseMap = response.data;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        responseMap = response.data;
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      log(errorMessage);
      responseMap = {"error": errorMessage};
    } finally {
      notifyListeners();
    }
    return responseMap;
  }

  String apiResponseMessage = '';
  Future<bool> deleteAccount(int? userId, BuildContext context) async {
    bool isAccountDeleted = false;
    try {
      dio.Response response = await userService.deleteAccount(userId);
      if (response.statusCode == 200) {
        log(response.data.toString());
        isAccountDeleted = true;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        apiResponseMessage = response.data['message'];
        log("Can't delete Account : ${response.data}");
        isAccountDeleted = false;
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log(e.toString());
      isAccountDeleted = true;
    }
    return isAccountDeleted;
  }

  bool isNotificationValueUpdate = false;
  String apiResMessage = '';
  Future<bool> controlNotification(Map<String, dynamic> payload) async {
    try {
      dio.Response response = await userService.controlNotification(payload);

      if (response.statusCode == 200) {
        isNotificationValueUpdate = true;
      } else {
        apiResMessage = response.data['message'];
        isNotificationValueUpdate = false;
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log(e.toString());
      isNotificationValueUpdate = false;
    }
    return isNotificationValueUpdate;
  }

  Future<Map<String, dynamic>> getNotificationSettings(
      int userId, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      dio.Response response = await userService.getNotificationSettings(userId);

      if (response.statusCode == 200) {
        responseMap = response.data;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        final jsonResponse = response.data;
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';
        // errorMessage = 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      log(errorMessage);
      responseMap = {"error": errorMessage};
    }
    return responseMap;
  }

  String apiMessage = '';
  Future<bool> postQuestionFeedback(Map<String, dynamic> payload) async {
    bool isQuestionPosted = false;
    try {
      dio.Response response = await userService.postQuestionFeedback(payload);

      if (response.statusCode == 200 || response.statusCode == 201) {
        // currently API giving 201, need to update it to 200
        isQuestionPosted = true;
      } else {
        apiMessage = response.data['message'];
        isQuestionPosted = false;
        notifyListeners();
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log(e.toString());
      isQuestionPosted = false;
    }
    return isQuestionPosted;
  }

  FellowReaderModel? currentlyReadMatchModel;
  Future<void> getCurrentlyReadMatch(
    int userId,
    int limit,
    int offSet,
    String filterName,
    List<int> matchIds,
    BuildContext context,
  ) async {
    try {
      dio.Response response = await userService.getMatchData(
          userId, limit, offSet, filterName, matchIds);

      if (response.statusCode == 200) {
        final jsonResponse = response.data;
        currentlyReadMatchModel = FellowReaderModel.fromJson(jsonResponse);
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        currentlyReadMatchModel = null;
        log("Response Message : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      currentlyReadMatchModel = null;
      log(e.toString());
    }
  }

  FellowReaderModel? toBeReadMatchModel;
  Future<void> getToBeReadMatch(
    int userId,
    int limit,
    int offSet,
    String filterName,
    List<int> matchIds,
    BuildContext context,
  ) async {
    try {
      dio.Response response = await userService.getMatchData(
          userId, limit, offSet, filterName, matchIds);

      if (response.statusCode == 200) {
        final jsonResponse = response.data;
        toBeReadMatchModel = FellowReaderModel.fromJson(jsonResponse);
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        toBeReadMatchModel = null;
        log("Response Message : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      toBeReadMatchModel = null;
      log(e.toString());
    }
  }

  FellowReaderModel? starMatchModel;
  Future<void> getFiveStarMatch(
    int userId,
    int limit,
    int offSet,
    String filterName,
    List<int> matchIds,
    BuildContext context,
  ) async {
    try {
      dio.Response response = await userService.getMatchData(
          userId, limit, offSet, filterName, matchIds);

      if (response.statusCode == 200) {
        final jsonResponse = response.data;
        starMatchModel = FellowReaderModel.fromJson(jsonResponse);
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        starMatchModel = null;
        log("Response Message : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      starMatchModel = null;
      log(e.toString());
    }
  }
}
