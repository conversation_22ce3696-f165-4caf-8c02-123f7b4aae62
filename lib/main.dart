import 'package:eljunto/app/app.dart';
import 'package:eljunto/app/app_initialize.dart';
import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/firebase/firebase_options.dart';
import 'package:eljunto/app/core/utils/app_config.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

void main() async {
  await AppInit.initializeApp(
    appName: "El Junto",
    baseUrl: ApiConstants.baseUrlProd,
    flavor: Flavor.prod,
    firebaseName: "eljunto",
    firebaseOptions: DefaultFirebaseOptions.currentPlatform,
  );

  runApp(
    MultiProvider(
      providers: AppInit.providers,
      child: const MyApp(),
    ),
  );
}
