import 'package:eljunto/app/app.dart';
import 'package:eljunto/app/app_initialize.dart';
import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/firebase/firebase_options_dev.dart';
import 'package:eljunto/app/core/utils/app_config.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

void main() async {
  await AppInit.initializeApp(
    appName: "El Junto Dev",
    baseUrl: ApiConstants.baseUrlDev,
    flavor: Flavor.dev,
    firebaseName: "eljunto-dev",
    firebaseOptions: DevFirebaseOptions.currentPlatform,
  );

  runApp(
    MultiProvider(
      providers: AppInit.providers,
      child: const MyApp(),
    ),
  );
}
