import 'dart:convert';

class BookCaseModel {
  final int? bookCaseId;
  final int? userId;
  final int? bookId;
  final String? bookName;
  final String? bookAuthor;
  final bool? is_currently_reading;
  final int? readingCompleteDate;
  double? ratings;
  final bool? topShelf;
  final String? review;
  final String? reading_complete_date_String;
  final int? reRead;
  bool? toBeRead;

  BookCaseModel({
    this.bookCaseId,
    this.userId,
    this.bookId,
    this.bookName,
    this.bookAuthor,
    this.is_currently_reading,
    this.readingCompleteDate,
    this.ratings,
    this.topShelf,
    this.review,
    this.reading_complete_date_String,
    this.reRead,
    this.toBeRead = false,
  });

  factory BookCaseModel.fromJson(Map<String, dynamic> json) {
    return BookCaseModel(
      bookCaseId: json['bookcase_id'],
      userId: json['user_id'],
      bookId: json['book_id'],
      bookName: json['bookName'],
      bookAuthor: json['bookAuthor'],
      is_currently_reading: json['is_currently_reading'],
      /* readingCompleteDate: json['reading_complete_date'] != null
          ? DateTime.parse(json['reading_complete_date'])
          : null, */
      readingCompleteDate: json['reading_complete_date'],
      ratings: json['ratings'],
      topShelf: json['topShelf'],
      review: json['review'],
      reRead: json['reRead'],
      toBeRead: json['toBeRead'],
    );
  }

  BookCaseModel copyWith({
    int? userId,
    int? bookId,
    int? bookCaseId,
    String? bookName,
    String? bookAuthor,
    bool? is_currently_reading,
    int? readingCompleteDate,
    double? ratings,
    bool? topShelf,
    String? review,
    int? reRead,
    bool? toBeRead,
    String? reading_complete_date_String,
  }) {
    return BookCaseModel(
      userId: userId ?? this.userId,
      bookId: bookId ?? this.bookId,
      bookCaseId: bookCaseId ?? this.bookCaseId,
      bookName: bookName ?? this.bookName,
      bookAuthor: bookAuthor ?? this.bookAuthor,
      is_currently_reading: is_currently_reading ?? this.is_currently_reading,
      readingCompleteDate: readingCompleteDate ?? this.readingCompleteDate,
      ratings: ratings ?? this.ratings,
      topShelf: topShelf ?? this.topShelf,
      review: review ?? this.review,
      reRead: reRead ?? this.reRead,
      toBeRead: toBeRead ?? this.toBeRead,
      reading_complete_date_String:
          reading_complete_date_String ?? this.reading_complete_date_String,
    );
  }
}

// To parse this JSON data, do
//
//     final toBeReadModel = toBeReadModelFromJson(jsonString);

ToBeReadModel toBeReadModelFromJson(String str) =>
    ToBeReadModel.fromJson(json.decode(str));

String toBeReadModelToJson(ToBeReadModel data) => json.encode(data.toJson());

class ToBeReadModel {
  final String? message;
  final List<ToBeReadList>? data;
  final int? count;
  final int? statusCode;

  ToBeReadModel({
    this.message,
    this.data,
    this.count,
    this.statusCode,
  });

  factory ToBeReadModel.fromJson(Map<String, dynamic> json) => ToBeReadModel(
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<ToBeReadList>.from(
                json["data"]!.map((x) => ToBeReadList.fromJson(x))),
        count: json["count"],
        statusCode: json["statusCode"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "count": count,
        "statusCode": statusCode,
      };
}

class ToBeReadList {
  final int? bookcaseId;
  final int? userId;
  final int? bookId;
  final String? bookName;
  final String? bookAuthor;
  final bool? isCurrentlyReading;
  final String? readingCompleteDate;
  final int? ratings;
  final bool? topShelf;
  final String? review;
  final int? reRead;
  final bool? toBeRead;

  ToBeReadList({
    this.bookcaseId,
    this.userId,
    this.bookId,
    this.bookName,
    this.bookAuthor,
    this.isCurrentlyReading,
    this.readingCompleteDate,
    this.ratings,
    this.topShelf,
    this.review,
    this.reRead,
    this.toBeRead,
  });

  factory ToBeReadList.fromJson(Map<String, dynamic> json) => ToBeReadList(
        bookcaseId: json["bookcase_id"],
        userId: json["user_id"],
        bookId: json["book_id"],
        bookName: json["bookName"],
        bookAuthor: json["bookAuthor"],
        isCurrentlyReading: json["is_currently_reading"],
        readingCompleteDate: json["reading_complete_date"],
        ratings: json["ratings"],
        topShelf: json["topShelf"],
        review: json["review"],
        reRead: json["reRead"],
        toBeRead: json["toBeRead"],
      );

  Map<String, dynamic> toJson() => {
        "bookcase_id": bookcaseId,
        "user_id": userId,
        "book_id": bookId,
        "bookName": bookName,
        "bookAuthor": bookAuthor,
        "is_currently_reading": isCurrentlyReading,
        "reading_complete_date": readingCompleteDate,
        "ratings": ratings,
        "topShelf": topShelf,
        "review": review,
        "reRead": reRead,
        "toBeRead": toBeRead,
      };
}
