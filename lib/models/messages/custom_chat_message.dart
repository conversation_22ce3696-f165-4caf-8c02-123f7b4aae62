class CustomChatUser {
  final String id;
  String? firstName;
  String? profileImage;
  final Map<String, dynamic>? customUserProperties;

  CustomChatUser({
    required this.id,
    this.firstName,
    this.profileImage,
    this.customUserProperties,
  });

  factory CustomChatUser.fromJson(Map<String, dynamic> json) {
    if (!json.containsKey('id') || json['id'] == null) {
      throw ArgumentError('Required field "id" is missing or null');
    }
    return CustomChatUser(
      id: json['id'],
      firstName: json['firstName'],
      profileImage: json['profileImage'],
      customUserProperties: json['customUserProperties'] != null
          ? Map<String, dynamic>.from(json['customUserProperties'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'firstName': firstName,
      'profileImage': profileImage,
      'customUserProperties': customUserProperties,
    };
  }
}

class CustomChatMessage {
  final CustomChatUser user;
  final DateTime createdAt;
  final String text;
  final Map<String, dynamic>? customMessageProperties;

  CustomChatMessage({
    required this.user,
    required this.createdAt,
    this.text = '',
    this.customMessageProperties,
  });

  factory CustomChatMessage.fromJson(Map<String, dynamic> json) {
    if (!json.containsKey('user') || json['user'] == null) {
      throw ArgumentError('Required field "user" is missing or null');
    }
    if (!json.containsKey('createdAt') || json['createdAt'] == null) {
      throw ArgumentError('Required field "createdAt" is missing or null');
    }

    DateTime createdAt;
    try {
      createdAt = DateTime.parse(json['createdAt']);
    } catch (e) {
      throw ArgumentError(
          'Invalid date format for "createdAt": ${json['createdAt']}');
    }

    return CustomChatMessage(
      user: CustomChatUser.fromJson(json['user']),
      createdAt: createdAt,
      text: json['text'] ?? '',
      customMessageProperties: json['customMessageProperties'] != null
          ? Map<String, dynamic>.from(json['customMessageProperties'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user': user.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'text': text,
      'customMessageProperties': customMessageProperties,
    };
  }
}
