import 'dart:convert';

UserCredential userCredentialFromJson(String str) =>
    UserCredential.fromJson(json.decode(str));

String userCredentialToJson(UserCredential data) => json.encode(data.toJson());

class UserCredential {
  String? userName;
  String? userEmailId;
  final String? userPhoneNumber;
  final int? lastLogoutTime;
  String? jwttoken;
  final String? refreshToken;
  final dynamic entityId;
  int? userId;
  final List<dynamic>? roleActions;
  final dynamic firstName;
  final dynamic lastName;
  final int? tokenExpiryDate;
  final String? portalType;
  bool isUserNameAvailable;
  String? userHandler;

  UserCredential({
    this.userName,
    this.userEmailId,
    this.userPhoneNumber,
    this.lastLogoutTime,
    this.jwttoken,
    this.refreshToken,
    this.entityId,
    this.userId,
    this.roleActions,
    this.firstName,
    this.lastName,
    this.tokenExpiryDate,
    this.portalType,
    this.isUserNameAvailable = false,
    this.userHandler,
  });

  factory UserCredential.fromJson(Map<String, dynamic> json) => UserCredential(
      userName: json["userName"],
      userEmailId: json["userEmailId"],
      userPhoneNumber: json["userPhoneNumber"],
      lastLogoutTime: json["lastLogoutTime"],
      jwttoken: json["token"],
      refreshToken: json["refreshToken"],
      entityId: json["entityId"],
      userId: json["userId"],
      roleActions: json["roleActions"] == null
          ? []
          : List<dynamic>.from(json["roleActions"]!.map((x) => x)),
      firstName: json["firstName"],
      lastName: json["lastName"],
      tokenExpiryDate: json["tokenExpiryDate"],
      portalType: json["portalType"],
      isUserNameAvailable: json["isUserNameAvailable"],
      userHandler: json['userHandle']);

  Map<String, dynamic> toJson() => {
        "userName": userName,
        "userEmailId": userEmailId,
        "userPhoneNumber": userPhoneNumber,
        "lastLogoutTime": lastLogoutTime,
        "token": jwttoken,
        "refreshToken": refreshToken,
        "entityId": entityId,
        "userId": userId,
        "roleActions": roleActions == null
            ? []
            : List<dynamic>.from(roleActions!.map((x) => x)),
        "firstName": firstName,
        "lastName": lastName,
        "tokenExpiryDate": tokenExpiryDate,
        "portalType": portalType,
        "isUserNameAvailable": isUserNameAvailable,
        "userHandle": userHandler,
      };
}

// To parse this JSON data, do
//
//     final userModel = userModelFromJson(jsonString);

UserModel userModelFromJson(String str) => UserModel.fromJson(json.decode(str));

String userModelToJson(UserModel data) => json.encode(data.toJson());

class UserModel {
  final String? message;
  final int? statusCode;
  final Data? data;

  UserModel({
    this.message,
    this.statusCode,
    this.data,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        message: json["message"],
        statusCode: json["statusCode"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "statusCode": statusCode,
        "data": data?.toJson(),
      };
}

class Data {
  final int? userId;
  final String? userName;
  final String? userHandle;
  final int? userPhoneNumber;
  final String? userEmailId;
  final String? portalType;
  final bool? userIsDeleted;
  final bool? userIsActive;
  final bool? userIsLocked;
  final int? userLastSuccessLogin;
  final int? userCreatedDate;
  final int? userUpdatedDate;
  final int? user30DayTrialEndDate;
  final bool? userIsSubscriptionActive;
  final int? userSubscriptionStartedDate;
  final int? userSubscriptionEndedDate;
  final String? userProfilePicture;
  final String? userLocation;
  final String? userBio;
  final bool userClubInvitation;
  final bool? userProfileCompleted;
  final int? matches;
  final List<int>? currentlyReadingBooks;
  final List<int>? toBeReadBooks;
  final List<int>? fiveStarMatchBooks;

  Data({
    this.userId,
    this.userName,
    this.userHandle,
    this.userPhoneNumber,
    this.userEmailId,
    this.portalType,
    this.userIsDeleted,
    this.userIsActive,
    this.userIsLocked,
    this.userLastSuccessLogin,
    this.userCreatedDate,
    this.userUpdatedDate,
    this.user30DayTrialEndDate,
    this.userIsSubscriptionActive,
    this.userSubscriptionStartedDate,
    this.userSubscriptionEndedDate,
    this.userProfilePicture,
    this.userLocation,
    this.userBio,
    this.userClubInvitation = false,
    this.userProfileCompleted,
    this.matches,
    this.currentlyReadingBooks,
    this.toBeReadBooks,
    this.fiveStarMatchBooks,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        userId: json["userId"],
        userName: json["userName"],
        userHandle: json["userHandle"],
        userPhoneNumber: json["userPhoneNumber"],
        userEmailId: json["userEmailId"],
        portalType: json["portalType"],
        userIsDeleted: json["userIsDeleted"],
        userIsActive: json["userIsActive"],
        userIsLocked: json["userIsLocked"],
        userLastSuccessLogin: json["userLastSuccessLogin"],
        userCreatedDate: json["userCreatedDate"],
        userUpdatedDate: json["userUpdatedDate"],
        user30DayTrialEndDate: json["user30DayTrialEndDate"],
        userIsSubscriptionActive: json["userIsSubscriptionActive"],
        userSubscriptionStartedDate: json["userSubscriptionStartedDate"],
        userSubscriptionEndedDate: json["userSubscriptionEndedDate"],
        userProfilePicture: json["userProfilePicture"],
        userLocation: json["userLocation"],
        userBio: json["userBio"],
        userClubInvitation: json["userClubInvitation"],
        userProfileCompleted: json["userProfileCompleted"],
        matches: json["matches"],
        currentlyReadingBooks: json["currentlyReadingBooks"] == null
            ? []
            : List<int>.from(json["currentlyReadingBooks"]!.map((x) => x)),
        toBeReadBooks: json["toBeReadBooks"] == null
            ? []
            : List<int>.from(json["toBeReadBooks"]!.map((x) => x)),
        fiveStarMatchBooks: json["fiveStarMatchBooks"] == null
            ? []
            : List<int>.from(json["fiveStarMatchBooks"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "userName": userName,
        "userHandle": userHandle,
        "userPhoneNumber": userPhoneNumber,
        "userEmailId": userEmailId,
        "portalType": portalType,
        "userIsDeleted": userIsDeleted,
        "userIsActive": userIsActive,
        "userIsLocked": userIsLocked,
        "userLastSuccessLogin": userLastSuccessLogin,
        "userCreatedDate": userCreatedDate,
        "userUpdatedDate": userUpdatedDate,
        "user30DayTrialEndDate": user30DayTrialEndDate,
        "userIsSubscriptionActive": userIsSubscriptionActive,
        "userSubscriptionStartedDate": userSubscriptionStartedDate,
        "userSubscriptionEndedDate": userSubscriptionEndedDate,
        "userProfilePicture": userProfilePicture,
        "userLocation": userLocation,
        "userBio": userBio,
        "userClubInvitation": userClubInvitation,
        "userProfileCompleted": userProfileCompleted,
        "matches": matches,
        "currentlyReadingBooks": currentlyReadingBooks == null
            ? []
            : List<int>.from(currentlyReadingBooks!.map((x) => x)),
        "toBeReadBooks": toBeReadBooks == null
            ? []
            : List<int>.from(toBeReadBooks!.map((x) => x)),
        "fiveStarMatchBooks": fiveStarMatchBooks == null
            ? []
            : List<int>.from(fiveStarMatchBooks!.map((x) => x)),
      };
}
