import 'package:cached_network_image/cached_network_image.dart';
import 'package:eljunto/app/core/constants.dart';
import 'package:flutter/material.dart';

class CustomCachedNetworkImage extends StatelessWidget {
  final String? imageUrl, errorImage;
  final double width, height;
  final bool isPrfileVisible;

  const CustomCachedNetworkImage({
    super.key,
    required this.imageUrl,
    required this.width,
    required this.height,
    required this.errorImage,
    this.isPrfileVisible = false,
  });

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl ?? '',
      // cacheKey: imageUrl,
      width: width,
      height: height,
      imageBuilder: (context, imageProvider) => Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: imageProvider,
            fit: BoxFit.fill,
          ),
        ),
      ),
      placeholder: (context, url) => const Center(
        child: CircularProgressIndicator(
          color: AppConstants.primaryColor,
          strokeWidth: 1,
        ),
      ),
      errorWidget: (context, url, error) => Image.asset(
        errorImage ?? '',
        fit: BoxFit.fill,
      ),
    );
  }
}
