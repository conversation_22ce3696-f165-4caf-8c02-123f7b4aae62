import 'dart:developer';

import 'package:eljunto/app/core/helpers/datetime_helper.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/club_membership_model.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:month_picker_dialog/month_picker_dialog.dart';
import 'package:provider/provider.dart';

import '../../app/core/constants.dart';
import '../../app/core/utils/text_style.dart';
import '../../models/meeting_join_model.dart';
import '../custom_button.dart';
import '../marquee_text.dart';

class StandingClubMeetingInfo extends StatefulWidget {
  final BookClubModel? bookClubModel;
  final int? loggedinUserId;
  final bool isLoadingSkeleton;
  final String? userProfilePicture;
  final String? userHandle;

  const StandingClubMeetingInfo({
    super.key,
    this.bookClubModel,
    this.loggedinUserId,
    this.isLoadingSkeleton = false,
    this.userProfilePicture,
    this.userHandle,
  });

  @override
  State<StandingClubMeetingInfo> createState() =>
      _StandingClubMeetingInfoState();
}

class _StandingClubMeetingInfoState extends State<StandingClubMeetingInfo> {
  MeetingJoinModel? meetingJoinModel;
  List<ClubMembershipModel>? memberList;
  List<int>? memberIdsList;
  List<Map<int?, String?>> userHandles = [];
  List<Map<int?, String?>> userProfilePicture = [];

  @override
  void initState() {
    super.initState();
    getBookClubMembers();
  }

  bool isLoading = false;

  Future<bool> joinMeetingFunction(
      int meetingId, String channelName, BuildContext context) async {
    isLoading = true;
    if (mounted) setState(() {});
    bool value = await Provider.of<BookClubController>(context, listen: false)
        .joinMeeting(meetingId, widget.loggedinUserId ?? 0, channelName);
    isLoading = false;
    if (mounted) setState(() {});
    return value;
  }

  Future<void> getBookClubMembers() async {
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubMembers(widget.bookClubModel?.bookClubId ?? 0, context);
  }

  @override
  Widget build(BuildContext context) {
    String meetingHeading = '';
    bool isLeader = widget.bookClubModel?.userId == widget.loggedinUserId;
    bool isNotification = widget.bookClubModel?.incomingRequest ?? false;
    final startTime = DateTime.fromMillisecondsSinceEpoch(
        widget.bookClubModel?.meetingStartTime ?? 0);
    final now = DateTime.now();
    final endTime = DateTime.fromMillisecondsSinceEpoch(
        widget.bookClubModel?.meetingEndTime ?? 0);
    final isMeetingInProgress = now.isBetween(
      startTime,
      endTime.add(
        Duration(hours: 1),
      ),
    );
    final isUpcomingMeeting = startTime.isAfter(DateTime.now());
    final isPreviousMeeting = startTime.isBefore(now);
    if ((widget.bookClubModel?.meetingBookName?.isNotEmpty ?? false) &&
        widget.bookClubModel?.meetingStartTime != null &&
        isMeetingInProgress) {
      meetingHeading = 'Meeting in Progress';
    } else {
      if ((widget.bookClubModel?.meetingBookName?.isNotEmpty ?? false) &&
          widget.bookClubModel?.meetingStartTime != null &&
          isPreviousMeeting) {
        meetingHeading = 'Previous Meeting';
      } else {
        if ((widget.bookClubModel?.meetingBookName?.isNotEmpty ?? false)) {
          meetingHeading = 'Upcoming Meeting';
        } else {
          meetingHeading = 'First meeting not yet scheduled';
        }
      }
    }
    String formattedDate = DateTimeHelper.getDayMonthYearDateFormat(
        widget.bookClubModel?.meetingStartTime);

    final meetingTime = DateTimeHelper.getMeetingScheduleTime(
      widget.bookClubModel?.meetingStartTime ?? 0,
      widget.bookClubModel?.meetingEndTime,
    );

    final difference = startTime.difference(DateTime.now());
    bool showJoinButton =
        (difference <= const Duration(minutes: 30) && !difference.isNegative) ||
            difference.isNegative;

    return GestureDetector(
      onTap: () {
        Provider.of<BookClubController>(context, listen: false)
            .updateData(widget.bookClubModel!);
        context.pushNamed(
          'user-club-details',
          queryParameters: {
            'bookClubId': widget.bookClubModel?.bookClubId.toString(),
            'userId': widget.loggedinUserId.toString(),
          },
        );
      },
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Column(
            children: [
              const SizedBox(
                height: 10,
              ),
              Container(
                padding: const EdgeInsets.all(14),
                margin: const EdgeInsets.only(left: 10),
                width: 320,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: widget.isLoadingSkeleton
                      ? AppConstants.skeletonBackgroundColor
                      : Colors.transparent,
                  border: Border.all(
                    color: widget.isLoadingSkeleton
                        ? AppConstants.skeletonBackgroundColor
                        : AppConstants.primaryColor,
                    width: 1.5,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                          child: MarqueeList(
                            children: [
                              Text(
                                widget.bookClubModel?.bookClubName ?? '',
                                overflow: TextOverflow.ellipsis,
                                style: lbBold.copyWith(
                                  fontSize: 18,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          width: 8,
                        ),
                        isLeader
                            ? Image.asset(
                                AppConstants.leaderStar,
                                height: 40,
                                width: 40,
                                fit: BoxFit.cover,
                                filterQuality: FilterQuality.high,
                              )
                            : const SizedBox.shrink(),
                        const SizedBox(
                          width: 8,
                        ),
                        Image.asset(
                          (widget.bookClubModel?.totalVacancies ?? 0) > 0
                              ? AppConstants.clubOpeningLogoImagePath
                              : AppConstants.clubOpeningZero,
                          height: 40,
                          width: 40,
                          fit: BoxFit.contain,
                          filterQuality: FilterQuality.high,
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    Text(
                      meetingHeading,
                      overflow: TextOverflow.ellipsis,
                      style: lbRegular.copyWith(
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    Text(
                      widget.bookClubModel?.meetingBookName ?? '',
                      overflow: TextOverflow.ellipsis,
                      style: lbBold.copyWith(
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      widget.bookClubModel?.meetingBookAuthor ?? '',
                      overflow: TextOverflow.ellipsis,
                      style: lbRegular.copyWith(
                        fontSize: 12,
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.bookClubModel?.partsOfTheBookCovered ?? '',
                              overflow: TextOverflow.ellipsis,
                              style: lbRegular.copyWith(
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              (meetingHeading ==
                                      'First meeting not yet scheduled')
                                  ? ''
                                  : formattedDate,
                              overflow: TextOverflow.ellipsis,
                              style: lbItalic.copyWith(
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              (meetingHeading ==
                                      'First meeting not yet scheduled')
                                  ? ''
                                  : meetingTime,
                              overflow: TextOverflow.ellipsis,
                              style: lbItalic.copyWith(
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                        (isMeetingInProgress ||
                                isUpcomingMeeting && showJoinButton)
                            ? CustomLoaderButton(
                                buttonWidth: isLoading
                                    ? 45.0
                                    : MediaQuery.of(context).size.width / 4.5,
                                buttonRadius: isLoading ? 30 : 20.0,
                                buttonChild: isLoading
                                    ? const CircularProgressIndicator(
                                        valueColor: AlwaysStoppedAnimation(
                                            Colors.white),
                                        strokeWidth: 3.0,
                                      )
                                    : Text(
                                        'Join Now',
                                        style: lbBold.copyWith(
                                          fontSize: 12,
                                          color: AppConstants.primaryColor,
                                        ),
                                      ),
                                buttonPressed: () {
                                  final meetingId =
                                      widget.bookClubModel?.meetingId;
                                  final channelName =
                                      widget.bookClubModel?.channelName;

                                  log("Meeting ID : $meetingId");
                                  log("Channel Name : $channelName");

                                  joinMeetingFunction(meetingId ?? 0,
                                          channelName ?? '', context)
                                      .then(
                                    (value) {
                                      if (context.mounted) {
                                        meetingJoinModel =
                                            Provider.of<BookClubController>(
                                                    context,
                                                    listen: false)
                                                .meetingJoinModel;
                                      }

                                      if (value) {
                                        final profilePictureUrl =
                                            (widget.userProfilePicture ?? '');
                                        if (context.mounted) {
                                          context.pushNamed(
                                            'MeetingScreen',
                                            extra: {
                                              'bookClubId': widget.bookClubModel
                                                      ?.bookClubId ??
                                                  0,
                                              'bookName': widget.bookClubModel
                                                      ?.meetingBookName ??
                                                  '',
                                              'token': meetingJoinModel?.data,
                                              'userId': widget.loggedinUserId,
                                              'discussionQue': widget
                                                  .bookClubModel
                                                  ?.discussionQuestions,
                                              'channelName': channelName,
                                              'userName':
                                                  widget.userHandle ?? '',
                                              'profilePictureUrl':
                                                  ApiConstants.imageBaseUrl +
                                                      profilePictureUrl,
                                            },
                                          );
                                        }
                                      }
                                    },
                                  );
                                },
                              )
                            : const SizedBox.shrink(),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          Positioned(
            top: 3,
            right: -2,
            child: Visibility(
              visible: isNotification && isLeader,
              child: Align(
                alignment: Alignment.topRight,
                child: Image.asset(
                  AppConstants.notificationImagePath,
                  height: 18,
                  width: 18,
                  filterQuality: FilterQuality.high,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
