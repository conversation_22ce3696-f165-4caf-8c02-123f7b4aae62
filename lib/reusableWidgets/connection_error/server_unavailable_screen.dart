import 'package:flutter/material.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

import '../../app/core/constants.dart';
import '../../app/core/services/setup_locator.dart';
import '../../app/core/utils/text_style.dart';
import '../../controller/connectivity_controller.dart';

class ServerUnavailableScreen extends StatelessWidget {
  final VoidCallback? onTryAgain;
  // final bool showAppBar;

  const ServerUnavailableScreen({
    super.key,
    this.onTryAgain,
    // this.showAppBar = true,
  });

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.sizeOf(context).height;
    final screenWidth = MediaQuery.sizeOf(context).width;

    return Scaffold(
      // appBar: showAppBar
      //     ? AppBar(
      //         automaticallyImplyLeading: false,
      //         backgroundColor: AppConstants.textGreenColor,
      //       )
      //     : null,
      backgroundColor: Colors.transparent,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          image: DecorationImage(
            image: AssetImage(AppConstants.bgImagePath),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/Server_Not_Available.png',
                  height: screenHeight * 0.28,
                  width: screenWidth * 0.61,
                  filterQuality: FilterQuality.high,
                  fit: BoxFit.contain,
                ),
                SizedBox(height: 20),
                Text(
                  'Server Unavailable',
                  style: lbBold.copyWith(
                    fontSize: 20,
                    color: AppConstants.primaryColor,
                  ),
                ),
                SizedBox(height: 10),
                Text(
                  'Please try again.',
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                    color: AppConstants.primaryColor,
                  ),
                ),
                SizedBox(height: 40),
                GestureDetector(
                  // onTap: onTryAgain!,
                  onTap: () async {
                    final provider = locator<ConnectivityProvider>();
                    if (provider.status == InternetStatus.connected &&
                        onTryAgain != null) {
                      onTryAgain!();
                    }
                  },
                  child: Container(
                    height: 45,
                    decoration: BoxDecoration(
                      color: AppConstants.textGreenColor,
                      borderRadius: BorderRadius.circular(90),
                    ),
                    child: Center(
                      child: Text(
                        "Try Again",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(fontSize: 18),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
