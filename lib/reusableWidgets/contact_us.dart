import 'package:eljunto/app/core/constants.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../app/core/utils/text_style.dart';

class ContactUs extends StatelessWidget {
  const ContactUs({super.key});

  @override
  Widget build(BuildContext context) {
    return Text.rich(
      textAlign: TextAlign.center,
      TextSpan(
        text: 'Need help? Email us - ',
        style: lbRegular.copyWith(
          fontSize: 14,
        ),
        children: [
          TextSpan(
            text: '<EMAIL>',
            style: lbBold.copyWith(
              fontSize: 14,
              color: AppConstants.textGreenColor,
              decorationColor: AppConstants.textGreenColor,
            ),
            recognizer: TapGestureRecognizer()
              ..onTap = () async {
                final emailUri = Uri.parse('mailto:<EMAIL>');
                try {
                  final launched = await launchUrl(emailUri);
                  // Show error if URL couldn't be launched
                  if (!launched && context.mounted) {
                    _showErrorSnackBar(
                      context,
                      'Could not launch email app',
                      lbRegular,
                    );
                  }
                } catch (e) {
                  // Handle exceptions
                  if (context.mounted) {
                    _showErrorSnackBar(
                      context,
                      'Error: ${e.toString()}',
                      lbRegular,
                    );
                  }
                }
              },
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(
    BuildContext context,
    String message,
    TextStyle baseStyle,
  ) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 1),
        content: Text(
          message,
          style: baseStyle.copyWith(
            color: Colors.black,
            fontSize: 16,
          ),
        ),
        backgroundColor: AppConstants.textGreenColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
          side: const BorderSide(
            color: AppConstants.primaryColor,
          ),
        ),
      ),
    );
  }
}
