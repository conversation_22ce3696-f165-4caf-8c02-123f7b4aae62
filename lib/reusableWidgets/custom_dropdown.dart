import 'package:flutter/material.dart';

import '../app/core/constants.dart';
import '../app/core/utils/text_style.dart';

/// A styled, reusable, and generic DropdownMenu widget.
class CustomDropdownMenu<T> extends StatefulWidget {
  const CustomDropdownMenu({
    super.key,
    required this.items,
    required this.itemToString,
    required this.onSelected,
    this.controller,
    this.hintText,
    this.initialValue,
    this.menuHeight = 200.0,
  });

  final List<T> items;
  final String Function(T item) itemToString;
  final void Function(T? value) onSelected;
  final TextEditingController? controller;
  final String? hintText;
  final T? initialValue;
  final double menuHeight;

  @override
  State<CustomDropdownMenu<T>> createState() => _CustomDropdownMenuState<T>();
}

class _CustomDropdownMenuState<T> extends State<CustomDropdownMenu<T>> {
  late final TextEditingController _internalController;

  @override
  void initState() {
    super.initState();
    _internalController = widget.controller ?? TextEditingController();

    if (widget.controller == null && widget.initialValue != null) {
      _internalController.text = widget.itemToString(widget.initialValue as T);
    }
  }

  @override
  void didUpdateWidget(covariant CustomDropdownMenu<T> oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.controller == null &&
        widget.initialValue != oldWidget.initialValue &&
        widget.initialValue != null) {
      final selectedItem = widget.items.firstWhere(
        (item) => item == widget.initialValue,
        orElse: () => widget.initialValue as T,
      );
      _internalController.text = widget.itemToString(selectedItem);
    }
  }

  @override
  void dispose() {
    // Dispose the internal controller only if it was created by this widget.
    if (widget.controller == null) {
      _internalController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DropdownMenu<T>(
      // --- Core Functionality ---
      controller: _internalController,
      requestFocusOnTap: false,
      onSelected: widget.onSelected,
      dropdownMenuEntries: widget.items.map((T item) {
        return DropdownMenuEntry<T>(
          value: item,
          label: widget.itemToString(item),
          style: TextButton.styleFrom(
            visualDensity: VisualDensity.comfortable,
            side: const BorderSide(
              width: 0.5,
              color: AppConstants.primaryColor,
            ),
            textStyle: lbRegular.copyWith(
              overflow: TextOverflow.ellipsis,
              fontSize: 12,
            ),
          ),
        );
      }).toList(),

      // --- Style and Configuration ---
      expandedInsets: EdgeInsets.zero,
      menuHeight: widget.menuHeight,
      textStyle: lbRegular.copyWith(fontSize: 12),
      inputDecorationTheme: InputDecorationTheme(
        labelStyle: lbRegular.copyWith(
          fontSize: 14,
          color: Colors.grey.shade600,
        ),
        alignLabelWithHint: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 10),
        filled: true,
        fillColor: AppConstants.backgroundColor,
        focusColor: AppConstants.primaryColor,
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: AppConstants.primaryColor,
            width: 1.5,
          ),
          borderRadius: BorderRadius.all(Radius.circular(10)),
        ),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: AppConstants.primaryColor,
            width: 2.0,
          ),
          borderRadius: BorderRadius.all(Radius.circular(10)),
        ),
      ),
      menuStyle: const MenuStyle(
        surfaceTintColor: WidgetStatePropertyAll(Colors.transparent),
        shape: WidgetStatePropertyAll(
          RoundedRectangleBorder(
            side: BorderSide(
              color: AppConstants.primaryColor,
              width: 1,
            ),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
        ),
        padding: WidgetStatePropertyAll(EdgeInsets.zero),
        backgroundColor: WidgetStatePropertyAll(AppConstants.backgroundColor),
      ),
    );
  }
}
