import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:flutter/material.dart';

class UnorderedList extends StatelessWidget {
  final List<String> items;

  const UnorderedList(this.items, {super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: items.map((item) => UnorderedListItem(item)).toList(),
    );
  }
}

class UnorderedListItem extends StatelessWidget {
  final String item;

  const UnorderedListItem(this.item, {super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '• ',
            style: TextStyle(fontSize: 18),
          ),
          Expanded(
            child: Text(
              item,
              style: lbRegular.copyWith(
                fontSize: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
