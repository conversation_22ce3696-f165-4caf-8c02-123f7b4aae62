import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/models/messages/custom_chat_message.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart' as emoji;
import 'package:flutter/foundation.dart' as foundation;
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../app/core/constants.dart';

class CustomEmojiPicker extends StatefulWidget {
  final CustomChatMessage? message;

  const CustomEmojiPicker({
    super.key,
    this.message,
  });

  @override
  State<CustomEmojiPicker> createState() => _CustomEmojiPickerState();
}

class _CustomEmojiPickerState extends State<CustomEmojiPicker> {
  final ValueNotifier<List<emoji.Emoji>> _filteredEmojisNotifier =
      ValueNotifier<List<emoji.Emoji>>([]);

  @override
  void dispose() {
    _filteredEmojisNotifier.dispose();
    super.dispose();
  }

  IconData getEmojiIcon(
      emoji.Config emojiConfig, emoji.Category emojiCategory) {
    IconData emojiCategoryIcon;
    switch (emojiCategory) {
      case emoji.Category.RECENT:
        emojiCategoryIcon =
            emojiConfig.categoryViewConfig.categoryIcons.recentIcon;
      case emoji.Category.SMILEYS:
        emojiCategoryIcon =
            emojiConfig.categoryViewConfig.categoryIcons.smileyIcon;
      case emoji.Category.ANIMALS:
        emojiCategoryIcon =
            emojiConfig.categoryViewConfig.categoryIcons.animalIcon;
      case emoji.Category.FOODS:
        emojiCategoryIcon =
            emojiConfig.categoryViewConfig.categoryIcons.foodIcon;
      case emoji.Category.TRAVEL:
        emojiCategoryIcon =
            emojiConfig.categoryViewConfig.categoryIcons.travelIcon;
      case emoji.Category.ACTIVITIES:
        emojiCategoryIcon =
            emojiConfig.categoryViewConfig.categoryIcons.activityIcon;
      case emoji.Category.OBJECTS:
        emojiCategoryIcon =
            emojiConfig.categoryViewConfig.categoryIcons.objectIcon;
      case emoji.Category.SYMBOLS:
        emojiCategoryIcon =
            emojiConfig.categoryViewConfig.categoryIcons.symbolIcon;
      case emoji.Category.FLAGS:
        emojiCategoryIcon =
            emojiConfig.categoryViewConfig.categoryIcons.flagIcon;
    }
    return emojiCategoryIcon;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 10, right: 10, bottom: 20),
      child: emoji.EmojiPicker(
        config: emoji.Config(
          height: MediaQuery.sizeOf(context).height * 0.5,
          // bgColor: const Color(0xFFF2F2F2),
          checkPlatformCompatibility: true,
          emojiViewConfig: emoji.EmojiViewConfig(
            backgroundColor: AppConstants.primaryColor,
            buttonMode: emoji.ButtonMode.CUPERTINO,
            verticalSpacing: 10,
            horizontalSpacing: 10,
            emojiSizeMax: 25 *
                (foundation.defaultTargetPlatform == TargetPlatform.iOS
                    ? 1.10
                    : 1.0),
          ),
          viewOrderConfig: const emoji.ViewOrderConfig(
            top: emoji.EmojiPickerItem.searchBar,
            middle: emoji.EmojiPickerItem.emojiView,
            bottom: emoji.EmojiPickerItem.categoryBar,
          ),
          skinToneConfig: const emoji.SkinToneConfig(),
          categoryViewConfig: emoji.CategoryViewConfig(
            initCategory: emoji.Category.SMILEYS,
            customCategoryView: (config, state, tabController, pageController) {
              final categoryEmojis = state.categoryEmoji;
              return TabBar(
                controller: tabController,
                tabAlignment: TabAlignment.center,
                padding: const EdgeInsets.only(top: 10),
                isScrollable: true,
                indicator: BoxDecoration(
                  border: Border.all(color: Colors.white),
                  borderRadius: BorderRadius.circular(7),
                  color: Colors.white,
                ),
                dividerColor: Colors.transparent,
                indicatorSize: TabBarIndicatorSize.tab,
                unselectedLabelColor: Colors.white,
                labelColor: AppConstants.primaryColor,
                splashBorderRadius: BorderRadius.circular(7),
                onTap: (value) => pageController.jumpToPage(value),
                tabs: categoryEmojis.map((emo) {
                  final category = emo.category;
                  final emojiCategoryIcon = getEmojiIcon(config, category);
                  return Tab(
                    icon: Icon(emojiCategoryIcon),
                  );
                }).toList(),
              );
            },
          ),
          bottomActionBarConfig: emoji.BottomActionBarConfig(
            customBottomActionBar: (config, state, showSearchView) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 10),
                child: TextField(
                  onTap: showSearchView,
                  decoration: InputDecoration(
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(color: Colors.white),
                    ),
                    constraints: const BoxConstraints(maxHeight: 40),
                    labelText: 'Search emojis',
                    labelStyle:
                        lbRegular.copyWith(fontSize: 12, color: Colors.white),
                    prefixIcon: const Icon(
                      Icons.search,
                      color: Colors.white,
                      size: 20,
                    ),
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                  ),
                ),
              );
            },
          ),
          searchViewConfig: emoji.SearchViewConfig(
            customSearchView: (config, state, showEmojiView) {
              // Get all emojis across categories for searching
              List<emoji.Emoji> allEmojis = state.categoryEmoji
                  .expand((category) => category.emoji)
                  .toList();

              return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.viewInsetsOf(context).bottom),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: TextField(
                        cursorColor: Colors.white,
                        style: lbRegular.copyWith(
                          fontSize: 14,
                          color: Colors.white,
                        ),
                        autofocus: true,
                        maxLines: 1,
                        decoration: InputDecoration(
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: const BorderSide(color: Colors.white),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: const BorderSide(color: Colors.white),
                          ),
                          constraints: const BoxConstraints(maxHeight: 40),
                          hintText: 'Search emojis',
                          hintStyle: lbRegular.copyWith(
                              fontSize: 12, color: Colors.white),
                          prefixIcon: IconButton(
                            icon: const Icon(
                              Icons.arrow_back_ios_new,
                              color: Colors.white,
                              size: 20,
                            ),
                            onPressed: showEmojiView,
                          ),
                          contentPadding: EdgeInsets.zero,
                          isDense: true,
                        ),
                        onChanged: (value) {
                          if (value.isEmpty) {
                            _filteredEmojisNotifier.value = [];
                            return;
                          }

                          final filtered = allEmojis.where((emoji) {
                            // Search in emoji names and keywords
                            final searchTerm = value.toLowerCase();
                            return emoji.name
                                    .toLowerCase()
                                    .contains(searchTerm) ||
                                emoji.keywords.any((keyword) =>
                                    keyword.toLowerCase().contains(searchTerm));
                          }).toList();

                          _filteredEmojisNotifier.value = filtered;
                        },
                      ),
                    ),
                    // Search Results Display
                    ValueListenableBuilder(
                        valueListenable: _filteredEmojisNotifier,
                        builder: (context, filteredEmojis, _) {
                          return AnimatedSize(
                            duration: const Duration(milliseconds: 200),
                            child: filteredEmojis.isNotEmpty
                                ? Container(
                                    height: 50,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12),
                                    decoration: BoxDecoration(
                                      color: Colors.transparent,
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: SingleChildScrollView(
                                      scrollDirection: Axis.horizontal,
                                      child: Row(
                                        children: filteredEmojis
                                            .map(
                                              (e) => Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 4),
                                                child: NetworkAwareTap(
                                                  onTap: () => context.pop(e),
                                                  child: Text(
                                                    e.emoji,
                                                    style: const TextStyle(
                                                        fontSize: 24),
                                                  ),
                                                ),
                                              ),
                                            )
                                            .toList(),
                                      ),
                                    ),
                                  )
                                : const SizedBox.shrink(),
                          );
                        }),
                    const SizedBox(height: 10),
                  ],
                ),
              );
            },
          ),
        ),
        onEmojiSelected: (category, emoji) => context.pop(emoji),
      ),
    );
  }
}
