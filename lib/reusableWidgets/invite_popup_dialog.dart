import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class InvitePopupDialog extends StatefulWidget {
  final String userName;
  final List<BookClubModel> clubsLedByUser;
  bool bookClubValidation;
  bool invitationMsgValidation;
  bool isButtonLoading;
  final VoidCallback onCancel;
  final Future<void> Function(int, String) onInvite;

  InvitePopupDialog({
    super.key,
    required this.userName,
    required this.clubsLedByUser,
    this.bookClubValidation = false,
    this.invitationMsgValidation = false,
    this.isButtonLoading = false,
    required this.onCancel,
    required this.onInvite,
  });

  @override
  State<InvitePopupDialog> createState() => _InvitePopupDialogState();
}

class _InvitePopupDialogState extends State<InvitePopupDialog> {
  final TextEditingController bookClubController = TextEditingController();
  final TextEditingController invitationMsgController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 0),
      contentPadding: const EdgeInsets.fromLTRB(
        25.0,
        15.0,
        25.0,
        0,
      ),
      actionsPadding: const EdgeInsets.symmetric(
        vertical: 30,
        horizontal: 20,
      ),
      backgroundColor: AppConstants.backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: const BorderSide(
          color: AppConstants.popUpBorderColor,
          width: 1.5,
        ),
      ),
      surfaceTintColor: Colors.white,
      content: SingleChildScrollView(
        child: SizedBox(
          width: MediaQuery.sizeOf(context).width * .90,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Close Button
              Align(
                alignment: Alignment.centerRight,
                child: NetworkAwareTap(
                  onTap: () => context.pop(),
                  child: Image.asset(
                    AppConstants.closePopupImagePath,
                    height: 30,
                    width: 30,
                  ),
                ),
              ),
              const SizedBox(height: 5),

              // Title Text
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10.0),
                child: Text(
                  "Invite: ${widget.userName}",
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                  ),
                ),
              ),
              const SizedBox(height: 25),

              // Dropdown Menu for Book Clubs
              DropdownMenu(
                expandedInsets: EdgeInsets.zero,
                hintText: "Select one club",
                requestFocusOnTap: false,
                textStyle: lbRegular.copyWith(
                  fontSize: 12,
                ),
                inputDecorationTheme: const InputDecorationTheme(
                  alignLabelWithHint: true,
                  contentPadding: EdgeInsets.symmetric(
                    vertical: BorderSide.strokeAlignCenter,
                    horizontal: 10,
                  ),
                  focusColor: Color.fromRGBO(243, 104, 45, 1),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: AppConstants.primaryColor),
                    borderRadius: BorderRadius.all(Radius.circular(10)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Color.fromRGBO(
                        243,
                        104,
                        45,
                        1,
                      ),
                    ),
                  ),
                  isDense: true,
                  constraints: BoxConstraints(maxHeight: 50),
                  outlineBorder: BorderSide(color: AppConstants.primaryColor),
                ),
                menuStyle: const MenuStyle(
                  padding: WidgetStatePropertyAll(EdgeInsets.zero),
                  shape: WidgetStatePropertyAll(
                    RoundedRectangleBorder(
                      side: BorderSide(
                        color: AppConstants.primaryColor,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.all(
                        Radius.circular(12),
                      ),
                    ),
                  ),
                  backgroundColor:
                      WidgetStatePropertyAll(AppConstants.backgroundColor),
                ),
                onSelected: (newValue) {
                  if (newValue != null) {
                    bookClubController.text = newValue.toString();
                  }
                  setState(() {});
                },
                dropdownMenuEntries: widget.clubsLedByUser.map((club) {
                  return DropdownMenuEntry(
                    value: club.bookClubId,
                    label: club.bookClubName ?? '',
                    style: ButtonStyle(
                      backgroundColor:
                          WidgetStatePropertyAll(AppConstants.backgroundColor),
                      foregroundColor:
                          WidgetStatePropertyAll(AppConstants.primaryColor),
                      textStyle: WidgetStatePropertyAll(
                          lbRegular.copyWith(fontSize: 12)),
                      side: WidgetStatePropertyAll(
                        BorderSide(
                          color: AppConstants.primaryColor,
                          width: .5,
                        ),
                      ),
                    ),
                  );
                }).toList(),
                menuHeight: 200,
              ),

              const SizedBox(height: 5),

              // Validation Message for Dropdown
              // Displayed conditionally, which is cleaner than Stack/Positioned here.
              if (widget.bookClubValidation)
                Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: const EdgeInsets.only(left: 4.0),
                    child: Text(
                      '*Select book club',
                      style: lbRegular.copyWith(
                        fontSize: 14,
                        color: AppConstants.redColor,
                      ),
                    ),
                  ),
                ),

              const SizedBox(height: 25),

              // Invitation Message Label
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  "Write an invitation message:",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(fontSize: 15),
                ),
              ),
              const SizedBox(height: 10),

              // Invitation Message Text Field
              Stack(
                children: [
                  TextFormField(
                    controller: invitationMsgController,
                    maxLines: 6,
                    maxLength: 500,
                    style: lbRegular.copyWith(fontSize: 12),
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.all(10),
                      filled: true,
                      fillColor: const Color.fromRGBO(
                        255,
                        255,
                        255,
                        1,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: const BorderSide(
                          color: AppConstants.primaryColor,
                          width: 1.5,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: const BorderSide(
                          color: AppConstants.primaryColor,
                          width: 1.5,
                        ),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: const BorderSide(
                          color: AppConstants.primaryColor,
                          width: 1.5,
                        ),
                      ),
                      counterStyle: lbRegular.copyWith(
                        fontSize: 14,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    onChanged: (value) {
                      // Clear validation on change
                      setState(() {});
                    },
                  ),

                  // Validation Message for Text Field
                  if (widget.invitationMsgValidation)
                    Positioned(
                      bottom: 0,
                      left: 0,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Padding(
                          padding: const EdgeInsets.only(
                            top: 4.0,
                            left: 4.0,
                          ),
                          child: Text(
                            '*Enter a message',
                            style: lbRegular.copyWith(
                              fontSize: 14,
                              color: AppConstants.redColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
      actions: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Invite Button
            Flexible(
              child: CustomLoaderButton(
                buttonRadius: 30.0,
                buttonWidth: widget.isButtonLoading
                    ? 45.0
                    : MediaQuery.sizeOf(context).width / 3,
                buttonChild: widget.isButtonLoading
                    ? const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation(Colors.white),
                        strokeWidth: 3.0,
                      )
                    : Text(
                        "Invite",
                        style: lbBold.copyWith(fontSize: 18),
                      ),
                buttonPressed: () async {
                  final isBookClubEmpty = bookClubController.text.isEmpty;
                  final isMessageEmpty = invitationMsgController.text.isEmpty;

                  if (isBookClubEmpty || isMessageEmpty) {
                    setState(() {
                      widget.bookClubValidation = isBookClubEmpty;
                      widget.invitationMsgValidation = isMessageEmpty;
                    });
                    return;
                  }

                  setState(() {
                    widget.isButtonLoading = true;
                  });

                  // Await the async operation
                  await widget.onInvite(
                    int.parse(bookClubController.text),
                    invitationMsgController.text,
                  );

                  if (context.mounted) {
                    // context.pop();
                    widget.isButtonLoading = false;
                    setState(() {});
                  }
                },
              ),
            ),

            const SizedBox(width: 10),

            // Cancel Button
            widget.isButtonLoading
                ? const SizedBox.shrink()
                : Flexible(
                    child: NetworkAwareTap(
                      onTap: () => context.pop(),
                      child: Container(
                        height: 45,
                        width: MediaQuery.sizeOf(context).width / 3,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(30),
                          color: AppConstants.backgroundColor,
                          border: Border.all(color: AppConstants.primaryColor),
                        ),
                        child: Center(
                          child: Text(
                            "Cancel",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(fontSize: 18),
                          ),
                        ),
                      ),
                    ),
                  ),
          ],
        ),
      ],
    );
  }
}
