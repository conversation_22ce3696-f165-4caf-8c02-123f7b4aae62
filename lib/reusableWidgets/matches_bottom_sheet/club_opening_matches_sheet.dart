import 'dart:developer';

import 'package:eljunto/app/core/helpers/datetime_helper.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../app/core/constants.dart';
import '../../app/core/utils/text_style.dart';
import '../../models/home_model/home_screen1_model/new_club_opening_model.dart';
import '../club_join_popup.dart';

class ClubOpeningMatchesBottomSheet extends StatefulWidget {
  final String? clubName;
  final int? clubId;
  final String? memberRequestPrompt;
  final List<MatchedMeeting>? matchedMeetings;
  final NewClubOpeningList? clubList;
  const ClubOpeningMatchesBottomSheet({
    super.key,
    this.clubId,
    this.clubName,
    this.memberRequestPrompt,
    this.matchedMeetings,
    this.clubList,
  });

  @override
  State<ClubOpeningMatchesBottomSheet> createState() =>
      _ClubOpeningMatchesBottomSheetState();
}

class _ClubOpeningMatchesBottomSheetState
    extends State<ClubOpeningMatchesBottomSheet> {
  // List<NewClubOpeningList> clubList = [];

  BookClubController? bookClubController;

  @override
  void initState() {
    log("MeetingList : ${widget.matchedMeetings}");
    bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    super.initState();
  }

  Future<void> getMeetingData() async {
    // clubList = bookClubController?.openingList ?? [];
    // log("ClubData : $clubList");
    // setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      // height: 200,
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            height: 25,
          ),
          NetworkAwareTap(
            onTap: () {
              context.pop();
            },
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: AppConstants.textGreenColor,
              ),
              child: const Icon(
                Icons.keyboard_arrow_down_rounded,
                color: AppConstants.primaryColor,
                size: 30,
              ),
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            // mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                height: 25,
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                padding: const EdgeInsets.all(14),
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppConstants.primaryColor,
                    width: 1.5,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: MarqueeList(
                        children: [
                          Text(
                            widget.clubName ?? '',
                            // overflow: TextOverflow.ellipsis,
                            style: lbBold.copyWith(
                              fontSize: 18,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      width: 5,
                    ),
                    NetworkAwareTap(
                      onTap: () {
                        showJoinClubPopup(
                          widget.clubId ?? 0,
                          widget.memberRequestPrompt ?? '',
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 14.0, vertical: 7),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          color: AppConstants.textGreenColor,
                        ),
                        child: textFontBold14(
                          "Join Club",
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              MarqueeList(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: textWidget18(
                      'Upcoming Meeting Book/Author Match',
                    ),
                  ),
                ],
              ),

              const SizedBox(
                height: 10,
              ),

              /// CURRENTLY READING BOOKS/AUTHOR MATCH LIST VIEW BUILDER
              SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                scrollDirection: Axis.horizontal,
                child: Consumer<BookClubController>(
                  builder: (context, bookClubController, child) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: List.generate(
                        widget.matchedMeetings?.length ?? 0,
                        (index) {
                          final meetingTime =
                              DateTimeHelper.getMeetingScheduleTime(
                                  widget.matchedMeetings?[index]
                                          .meetingStartTime ??
                                      0,
                                  widget.matchedMeetings?[index]
                                          .meetingEndTime ??
                                      0);
                          final meetingDate =
                              DateTimeHelper.getDayMonthYearDateFormat(
                                  widget.matchedMeetings?[index].meetingDate);
                          return Container(
                            width: 250,
                            margin: const EdgeInsets.only(right: 10),
                            padding: const EdgeInsets.all(14),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: AppConstants.primaryColor,
                                width: 1.5,
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                MarqueeList(
                                  children: [
                                    textWidget16(
                                      widget.matchedMeetings?[index].bookName ??
                                          '',
                                    ),
                                  ],
                                ),
                                MarqueeList(
                                  children: [
                                    textFontSize14(
                                      widget.matchedMeetings?[index]
                                              .bookAuthor ??
                                          '',
                                    ),
                                  ],
                                ),
                                textFontSize14(
                                  widget.matchedMeetings?[index]
                                          .partOfBookCovered ??
                                      '',
                                ),
                                const SizedBox(
                                  height: 25,
                                ),
                                textFontSize12(
                                  meetingDate,
                                ),
                                textFontSize12(
                                  meetingTime,
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),

              const SizedBox(
                height: 25,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget textWidget18(String title) {
    return Text(
      title,
      overflow: TextOverflow.ellipsis,
      style: lbRegular.copyWith(
        color: AppConstants.primaryColor,
        fontSize: 18,
      ),
    );
  }

  Widget textWidget16(String title) {
    return Text(
      title,
      overflow: TextOverflow.ellipsis,
      style: lbBold.copyWith(
        color: AppConstants.primaryColor,
        fontSize: 16,
      ),
    );
  }

  Widget textFontSize14(String title) {
    return Text(
      title,
      overflow: TextOverflow.ellipsis,
      style: lbRegular.copyWith(
        color: AppConstants.primaryColor,
        fontSize: 14,
      ),
    );
  }

  Widget textFontBold14(String title) {
    return Text(
      title,
      overflow: TextOverflow.ellipsis,
      style: lbBold.copyWith(
        color: AppConstants.primaryColor,
        fontSize: 14,
      ),
    );
  }

  Widget textFontSize12(String title) {
    return Text(
      title,
      overflow: TextOverflow.ellipsis,
      style: lbRegular.copyWith(
        color: AppConstants.primaryColor,
        fontSize: 12,
      ),
    );
  }

  void showJoinClubPopup(int bookClubId, String requestPrompt) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState) {
          return ClubJoinPopup(
            bookClubId: bookClubId,
            memberRequestPrompt: requestPrompt,
          );
        });
      },
    );
  }
}
