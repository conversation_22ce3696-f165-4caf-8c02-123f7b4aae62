import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/models/club_membership_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/reusableWidgets/question_feedback_dialog.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class MessageScreenAppBar extends StatefulWidget {
  final String? bookName;
  final bool? isSetProfile;
  final bool clubNameFlag;
  final String? clubName;
  final String? impromptuCount;
  final bool showImpromptu;
  final VoidCallback? onTap;
  final List<ClubMembershipModel>? clubMembers;

  const MessageScreenAppBar({
    super.key,
    this.bookName,
    this.isSetProfile,
    this.clubName,
    this.clubNameFlag = false,
    this.impromptuCount,
    this.showImpromptu = false,
    this.onTap,
    this.clubMembers,
  });

  @override
  State<MessageScreenAppBar> createState() => _PreviousScreenAppBarState();
}

class _PreviousScreenAppBarState extends State<MessageScreenAppBar> {
  @override
  void dispose() {
    super.dispose();
    // isSetProfile = false;
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppConstants.textGreenColor,
      centerTitle: true,
      title: Padding(
        padding: const EdgeInsets.only(top: 15.0),
        child: Column(
          children: [
            MarqueeList(
              children: [
                NetworkAwareTap(
                  onTap: widget.onTap!,
                  child: Text(
                    widget.bookName ?? '',
                    overflow: TextOverflow.ellipsis,
                    style: lbBold.copyWith(
                      fontSize: 18,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 5),
            MarqueeList(
              children: widget.clubMembers?.asMap().entries.map(
                    (entry) {
                      final isLast =
                          entry.key == (widget.clubMembers!.length - 1);
                      final name = entry.value.userName;
                      return Text(
                        name?.isNotEmpty ?? false
                            ? '$name${isLast ? '' : ', '}'
                            : '',
                        overflow: TextOverflow.ellipsis,
                        style: lbBold.copyWith(fontSize: 10),
                      );
                    },
                  ).toList() ??
                  [],
            ),
            const SizedBox(
              height: 2,
            ),
            widget.showImpromptu
                ? Text(
                    widget.impromptuCount ?? '',
                    overflow: TextOverflow.ellipsis,
                    style: lbItalic.copyWith(fontSize: 14),
                  )
                : const SizedBox.shrink(),
            widget.clubNameFlag
                ? MarqueeList(
                    children: [
                      Text(
                        "(${widget.clubName ?? ''})",
                        overflow: TextOverflow.ellipsis,
                        style: lbBold.copyWith(
                          fontSize: 12,
                        ),
                      ),
                    ],
                  )
                : const SizedBox.shrink(),
          ],
        ),
      ),
      leading: Padding(
        padding: const EdgeInsets.only(left: 20.0, top: 10),
        child: NetworkAwareTap(
          onTap: () {
            context.goNamed('Message');
          },
          child: widget.isSetProfile ?? false
              ? Image.asset(
                  width: 73,
                  height: 65,
                  "assets/icons/Back.png",
                  fit: BoxFit.contain,
                  filterQuality: FilterQuality.high,
                )
              : Image.asset(
                  // width: 73,
                  // height: 65,
                  "assets/images/eljunto_logo_dark_teal.png",
                  fit: BoxFit.cover,
                  filterQuality: FilterQuality.high,
                ),
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 20.0, top: 10),
          child: NetworkAwareTap(
            onTap: () {
              showDialog(
                context: context,
                barrierColor: Colors.white60,
                builder: (BuildContext context) {
                  return const QuestionFeedbackDialog();
                },
              );
            },
            child: Image.asset(
              AppConstants.questionLogoImagePath,
              height: 34,
              width: 34,
            ),
          ),
        )
      ],
    );
  }
}
