import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:flutter/material.dart';

class NoDataWidget extends StatelessWidget {
  final String message;

  const NoDataWidget({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return Container(
      //padding: const EdgeInsets.all(16.0),
      width: MediaQuery.of(context).size.width,
      margin: const EdgeInsets.symmetric(
        horizontal: 20,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      height: 50,
      decoration: BoxDecoration(
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          message,
          textAlign: TextAlign.left,
          overflow: TextOverflow.ellipsis,
          style: lbBold.copyWith(fontSize: 14),
        ),
      ),
    );
  }
}
