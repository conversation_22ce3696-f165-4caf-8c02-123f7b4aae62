import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class SettingsItem extends StatelessWidget {
  final String label;
  final VoidCallback onTap;
  final Color? textColor;
  const SettingsItem({
    super.key,
    required this.label,
    required this.onTap,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return NetworkAwareTap(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.only(bottom: 25.0),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          height: 45,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: textColor != AppConstants.redColor
                  ? AppConstants.primaryColor
                  : AppConstants.redColor,
              width: 1.5,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: MarqueeList(
                  children: [
                    Text(
                      label,
                      overflow: TextOverflow.ellipsis,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                        color: textColor,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                width: 5,
              ),
              textColor != AppConstants.redColor
                  ? SvgPicture.asset(
                      'assets/icons/svg/Vector.svg',
                      height: 17,
                      width: 17,
                      fit: BoxFit.cover,
                    )
                  // ? Image.asset(
                  //     'assets/images/Vector_3.png',
                  //     height: 17,
                  //     width: 17,
                  //     fit: BoxFit.cover,
                  //     filterQuality: FilterQuality.high,
                  //   )
                  // : Image.asset(
                  //     'assets/images/Vector_3.png',
                  //     height: 17,
                  //     width: 17,
                  //     color: AppConstants.redColor,
                  //     fit: BoxFit.cover,
                  //     filterQuality: FilterQuality.high,
                  //   ),
                  : SvgPicture.asset(
                      'assets/icons/svg/Vector.svg',
                      height: 17,
                      width: 17,
                      fit: BoxFit.cover,
                      color: AppConstants.redColor,
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
