import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';

class TransitionPageWidget {
  static dynamic navigateTransitionPage({required Widget child}) {
    return Platform.isAndroid
        ? CustomTransitionPage(
            transitionDuration: const Duration(
              milliseconds: 250,
            ),
            reverseTransitionDuration: const Duration(
              milliseconds: 250,
            ),
            child: child,
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              return SlideTransition(
                // scale: animation,

                position: animation.drive(
                  Tween(
                    begin: const Offset(1, 0),
                    end: Offset.zero,
                  ).chain(
                    CurveTween(
                      curve: Curves.easeIn,
                    ),
                  ),
                ),
                child: child,
              );
            },
          )
        : CupertinoPage(child: child);
  }
}
