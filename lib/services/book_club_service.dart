import 'dart:developer';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/app/core/network/http/http_service.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/manage__meeting_model.dart';

import '../app/core/constants.dart';

class BookClubService {
  final String baseURL = ApiConstants.flavorBaseUrl;
  final _apiService = locator<HttpApiService>();

  Future<dio.Response> getNewClubs(
      int userId, String? bookClubType, int offset, int limit) async {
    return await _apiService.get(
      '$baseURL/home/<USER>/list?&userId=$userId&offset=$offset&limit=$limit',
    );
  }

  Future<dio.Response> getBookClubMembers(int bookClubId) async {
    return await _apiService.get(
      '$baseURL/book_club_members/list?bookClubId=$bookClubId',
    );
  }

  // CHANGES IN API
  Future<dio.Response> getBookClubUpcomingMeetings(
      int bookClubId, int limit, int offSet) async {
    return await _apiService.get(
      '$baseURL/book_club_meeting/meetings?bookClubId=$bookClubId&limit=$limit&offset=$offSet&meetingType=upcoming',
    );
  }

  Future<dio.Response> getBookClubPreviousMeetings(
      int bookClubId, int limit, int offSet) async {
    return await _apiService.get(
      '$baseURL/book_club_meeting/meetings?bookClubId=$bookClubId&limit=$limit&offset=$offSet&meetingType=previous',
    );
  }

  Future<dio.Response> getBookClubs(String? bookClubType, int? userId,
      int? bookClubId, int? offSet, int? limit) async {
    // print("API CALL 4");
    // return await _apiService.getRequest(
    //   '$baseURL/book_club/list?bookClubType=$bookClubType&userId=$userId&bookClubId=$bookClubId',
    // );

    // final queryParams = <String, String>{};

    if (offSet == null && limit == null && userId != null) {
      // log("IN API CALL Book Club ID 1 : ${bookClubId}");

      return await _apiService.get('$baseURL/book_club/list?userId=$userId');
    }

    if (bookClubType != null && bookClubType.isNotEmpty) {
      // log("IN API CALL Book Club ID 2 : ${bookClubId}");

      // queryParams['bookClubType'] = bookClubType;
      return await _apiService.get(
          '$baseURL/book_club/list?bookClubType=$bookClubType&userId=$userId&limit=$limit&offset=$offSet');
    }
    if (userId != null) {
      // log("IN API CALL Book Club ID 3 : ${bookClubId}");

      // queryParams['userId'] = userId.toString();
      return await _apiService.get(
          '$baseURL/book_club/list?userId=$userId&limit=$limit&offset=$offSet');
    }
    if (bookClubId != null) {
      // log("IN API CALL Book Club ID 4 : ${bookClubId}");
      // queryParams['bookClubId'] = bookClubId.toString();
      return await _apiService
          .get('$baseURL/book_club/list?&bookClubId=$bookClubId');
    }

    // queryParams['offset'] = offSet.toString();
    // queryParams['limit'] = limit.toString();

    // final uri = Uri.parse('$baseURL/book_club/list')
    //     .replace(queryParameters: queryParams);

    return await _apiService.get(
        '$baseURL/book_club/list?bookClubType=$bookClubType&userId=$userId&bookClubId=$bookClubId&limit=$limit&offset=$offSet');
  }

  Future<dio.Response> addMember(Map<String, dynamic> payload) async {
    return await _apiService.post('$baseURL/book_club_members/add', payload);
  }

  Future<dio.Response> getPendingInvitations(
      String? status, int? userId, int? bookClubId) async {
    final queryParams = <String, String>{};

    if (status != null && status.isNotEmpty) {
      queryParams['status'] = status;
    }
    if (userId != null) {
      queryParams['userId'] = userId.toString();
    }
    if (bookClubId != null) {
      queryParams['clubId'] = bookClubId.toString();
    }

    final uri = Uri.parse('$baseURL/book_club_members/invitations/list')
        .replace(queryParameters: queryParams);

    return await _apiService.get(uri.toString());
  }

  Future<dio.Response> updateInvitation(
      RequestManage clubMembershipModel) async {
    final Map<String, dynamic> payload = clubMembershipModel.toJson();
    return _apiService.post('$baseURL/book_club_members/update', payload);
  }

  Future<dio.Response> checkAavailabilityOfClubName(
      String bookClubName, int? bookId) async {
    final Map<String, dynamic> payload = {};
    if (bookClubName != '' && bookClubName.isNotEmpty) {
      payload['bookClubName'] = bookClubName;
    }
    if (bookId != null) {
      payload['bookId'] = bookId;
    }

    return _apiService.post('$baseURL/book_club/check-name-available', payload);
  }

  Future<dio.Response> addClub(
      BookClubModel newBookClub, ManageMeetingModel meetingPayload) async {
    //final Map<String, dynamic> payload = newBookClub.toJson();
    final Map<String, dynamic> payload = {
      ...newBookClub.toJson(),
      "meetingPayload": meetingPayload.toJson(),
    };
    return _apiService.post('$baseURL/book_club/add', payload);
  }

  Future<dio.Response> deleteMeeting(int meetingId) async {
    return _apiService.delete("$baseURL/book_club_meeting/delete/$meetingId");
  }

  Future<dio.Response> scheduleMeeting(ManageMeetingModel? payload) async {
    Map<String, dynamic> data = {
      'bookClubId': payload?.bookClubId,
      'bookId': payload?.bookId,
      'partOfBookCovered': payload?.partOfBookCovered,
      'meetingDate': payload?.meetingDate,
      'discussionQuestions': payload?.discussionQuestions,
      'meetingStartTime': payload?.meetingStartTime,
      // 'meetingEndTime': payload?.meetingEndTime,
      'meetingAlerts': payload?.meetingAlerts,
      'meetingStatus': payload?.meetingStatus,
      "userId": payload?.userId,
      "meetingDuration": payload?.meetingDuration,
    };
    return _apiService.post('$baseURL/book_club_meeting/add/meeting', data);
  }

  Future<dio.Response> updateMeeting(ManageMeetingModel? payload) async {
    /// This function is used to update a meeting in the Book Club.

    Map<String, dynamic> data;
    if (payload?.bookId == 0) {
      data = {
        'meetingId': payload?.meetingId,
        'bookClubId': payload?.bookClubId,
        // 'bookId': payload?.bookId,
        'partOfBookCovered': payload?.partOfBookCovered,
        'meetingDate': payload?.meetingDate,
        'discussionQuestions': payload?.discussionQuestions,
        'meetingStartTime': payload?.meetingStartTime,

        /// - `meetingDuration`: The duration of the meeting in milliseconds.
        'meetingEndTime': payload?.meetingEndTime,
        'meetingAlerts': payload?.meetingAlerts,
        'meetingStatus': payload?.meetingStatus,
        "userId": payload?.userId,
        "meetingDuration": payload?.meetingDuration,
        "channelName": payload?.channelName
      };
    } else {
      data = {
        'meetingId': payload?.meetingId,
        'bookClubId': payload?.bookClubId,
        'bookId': payload?.bookId,
        'partOfBookCovered': payload?.partOfBookCovered,
        'meetingDate': payload?.meetingDate,
        'discussionQuestions': payload?.discussionQuestions,
        'meetingStartTime': payload?.meetingStartTime,
        'meetingEndTime': payload?.meetingEndTime,
        'meetingAlerts': payload?.meetingAlerts,
        'meetingStatus': payload?.meetingStatus,
        "userId": payload?.userId,
        "meetingDuration": payload?.meetingDuration,
        "channelName": payload?.channelName
      };
    }
    log("Meeting Payload : $data");

    return _apiService.post('$baseURL/book_club_meeting/edit/meeting', data);
  }

  // "book_club_members/invitations/list?status=PENDING&$type=$clubId"
  Future<dio.Response> inComingRequest(int clubId, String pendingStatus,
      String reOpenedStatus, String requestType) async {
    if (pendingStatus.isEmpty) {}
    return _apiService.get(
        '$baseURL/book_club_members/invitations/list?$requestType=$clubId&statuses=$pendingStatus$reOpenedStatus');
  }

  Future<dio.Response> addRemoveOpening(bool flag, int bookClubId) async {
    Map<String, dynamic> payload = {
      'bookClubId': bookClubId,
      'isOpening': flag,
    };
    return _apiService.post('$baseURL/book_club/add-opening', payload);
  }

  Future<dio.Response> joinMeeting(
      int meetingId, int userId, String channelName) async {
    Map<String, dynamic> payload = {};
    return _apiService.post(
        "$baseURL/book_club_meeting/join?meetingId=$meetingId&userId=$userId&channelName=$channelName",
        payload);
  }

  Future<dio.Response> agoraLogs(Map<String, dynamic> payload) async {
    // Map<String, dynamic> payload = {};
    log("Agora Payload : $payload");
    return _apiService.post("$baseURL/error-logs/add", payload);
  }
}
