import 'package:dio/dio.dart' as dio;
import 'package:eljunto/app/core/network/http/http_service.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';

import '../app/core/constants.dart';

class LeaderAdminservice {
  final String baseURL = ApiConstants.flavorBaseUrl;
  final _apiService = locator<HttpApiService>();

  Future<dio.Response> updateCharter(
    // String bookClubName,
    int? bookClubId,
    String? clubCharter,
    String? memberReqPrompt,
    String? clubStatus,
  ) async {
    final Map<String, dynamic> payload = {
      'bookClubId': bookClubId,
      // 'bookClubName': bookClubName,
      'clubCharter': clubCharter,
      'memberReqPrompt': memberReqPrompt,
      'clubStatus': clubStatus,
    };
    return await _apiService.post('$baseURL/book_club/update', payload);
  }
}
