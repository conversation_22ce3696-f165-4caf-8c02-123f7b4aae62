import 'dart:developer';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/app/core/services/setup_locator.dart';

import '../app/core/constants.dart';
import '../app/core/network/http/http_service.dart';
import '../models/subscription_model/apple_purchase_response_model.dart';
import '../models/subscription_model/subscription_purchase_response_model.dart';

class SubscriptionServices {
  final String baseURL = ApiConstants.flavorBaseUrl;
  final _apiService = locator<HttpApiService>();

  Future<dio.Response> getSubscriptionDetails(String osType) async {
    return _apiService
        .get('$baseURL/user-subscription/user/check-validity?osType=$osType');
  }

  Future<dio.Response> isActiveSubscription() async {
    return _apiService.get('$baseURL/user-subscription/check-subscription');
  }

  Future<dio.Response> googlePlaypurchaseResponse(
      SubscriptionResponseModel obj) async {
    Map<String, dynamic> payload = {
      "purchasedId": obj.purchasedId,
      "subscriptionId": obj.subscriptionId,
      "transactionDate": obj.transactionDate,
      "verificationSource": obj.verificationSource,
      "verificationStatus": obj.verificationStatus,
      "productId": obj.productId,
      "VerificationPendingCompletePurchase":
          obj.verificationPendingCompletePurchase,
      "Verification error": obj.verificationError,
      "localVerificationData": obj.localVerificationData?.copyWith(
        acknowledged: obj.localVerificationData?.acknowledged,
        autoRenewing: obj.localVerificationData?.autoRenewing,
        orderId: obj.localVerificationData?.orderId,
        purchaseToken: obj.localVerificationData?.purchaseToken,
        purchaseAmount: obj.localVerificationData?.purchaseAmount,
        purchaseCode: obj.localVerificationData?.purchaseCode,
        purchaseState: obj.localVerificationData?.purchaseState,
        purchaseTime: obj.localVerificationData?.purchaseTime,
        packageName: obj.localVerificationData?.packageName,
        productId: obj.localVerificationData?.productId,
        quantity: obj.localVerificationData?.quantity,
      ),
    };
    log("Purchase Plan Object : $payload");
    return _apiService.post('$baseURL/billing-subscription/create', payload);
  }

  Future<dio.Response> appStorePurchaseResponse(
      ApplePurchaseResponseModel obj) async {
    Map<String, dynamic> payload = {
      "productId": obj.productId,
      "purchaseId": obj.purchaseId,
      "localVerificationData": obj.localVerificationData,
      "serverVerificationData": obj.serverVerificationData,
      "purchaseStatus": obj.purchaseStatus,
      "transactionDate": obj.transactionDate,
      "source": obj.source,
    };
    log("Purchase Plan Object : $payload");
    return _apiService.post(
        '$baseURL/billing-subscription/apple/create', payload);
  }
}
