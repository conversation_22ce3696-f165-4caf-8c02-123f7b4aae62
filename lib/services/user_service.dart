import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/app/core/network/http/http_service.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';

import '../app/core/constants.dart';
import '../models/profile_model/edit_profile/update_user_profile_model.dart';

class UserService {
  final String baseURL = ApiConstants.flavorBaseUrl;
  final _apiService = locator<HttpApiService>();

  Future<dio.Response> getUserDetailsByUserId(int userId) async {
    return await _apiService.get(
      '$baseURL/users/$userId',
    );
  }

  Future<dio.Response> getFellowReaders(Map<String, dynamic> payload) async {
    return await _apiService.post('$baseURL/home/<USER>/list', payload);
  }

  /// PROFILE UPDATE
  Future<dio.Response> updateProfileFunction(
      UserProfileUpdateModel profile) async {
    final Map<String, dynamic> payload = {
      "userId": profile.userId,
      "userEmailId": profile.userEmailId,
      "userProfilePicture": profile.userProfilePicture,
      "userName": profile.userName,
      "userLocation": profile.userLocation,
      "userBio": profile.userBio,
      "userClubInvitation": profile.userClubInvitation,
    };
    print("Profile Payload: $payload");
    return await _apiService.post('$baseURL/users/${profile.userId}', payload);
  }

  // ALL BOOK LIST
  Future<dio.Response> allBookFunction(
      int loggedInUserId, String search, int page, int limit) async {
    return _apiService
        .get('$baseURL/books/search?query=$search&page=0&size=$limit');
    // return _apiService.getRequest(
    //     '$baseURL/books/list?search=$search&page=$page&limit=500&userId=$loggedInUserId');
  }

  Future<dio.Response> uploadFile(File imageFile, int userId) async {
    return await _apiService.uploadFile(
      '$baseURL/file/uploadProfilePicture',
      imageFile,
      userId,
    );
  }

  // GET ALL LOCATION
  Future<dio.Response> getLocationData(String search) async {
    return _apiService.get('$baseURL/users/location?search=$search');
  }

  Future<dio.Response> deleteAccount(int? userId) async {
    return _apiService.delete('$baseURL/users/delete-user/$userId');
  }

  Future<dio.Response> controlNotification(Map<String, dynamic> payload) async {
    return _apiService.post(
        "$baseURL/notifications/control-notification", payload);
  }

  // SEARCH DATA API
  Future<dio.Response> searchFunction(
      String search, int userId, int offSet, int limit) async {
    final Map<String, dynamic> payload = {
      'searchTerm': search,
      'userId': userId,
      'offset': offSet,
      'limit': limit,
      "filters": ["profile"],
    };
    return _apiService.post('$baseURL/search/getAll', payload);
  }

  Future<dio.Response> searchClubsFunction(
      String search, int userId, int offSet, int limit) async {
    return _apiService.get(
        "$baseURL/book_club/club-list?userId=$userId&search=$search&limit=$limit&offset=$offSet");
  }

  Future<dio.Response> getNotificationSettings(int userId) async {
    return await _apiService.get(
      '$baseURL/notifications/get-notifications?userId=$userId',
    );
  }

  Future<dio.Response> postQuestionFeedback(
      Map<String, dynamic> payload) async {
    return _apiService.post("$baseURL/questions-feedback/add", payload);
  }

  Future<dio.Response> postSearchByBookAndAuthor(
      String search, int userId, int offSet, int limit, bool isBook) async {
    Map<String, dynamic> payload = {};
    if (isBook) {
      payload = {
        'whoIntoThisBook': search,
        'userId': userId,
        'offset': offSet,
        'limit': limit,
      };
    } else {
      payload = {
        'whoIntoThisAuthor': search,
        'userId': userId,
        'offset': offSet,
        'limit': limit,
      };
    }

    return _apiService.post('$baseURL/search/searchBy', payload);
  }

  /// GET BY DATA API
  Future<dio.Response> getByData(int userId, List<int>? bookId, String filter,
      int limit, int offset) async {
    final bookIdParam =
        bookId?.join(','); // Convert list to comma-separated string

    return await _apiService.get(
      '$baseURL/search/getDataBy?bookIds=$bookIdParam&filter=$filter&userId=$userId&limit=$limit&offset=$offset',
    );
  }

  Future<dio.Response> getMatchData(int userId, int limit, int offSet,
      String filterName, List<int> matchIds) async {
    log("matchIds 1 : $matchIds");
    final ids = matchIds.join(',');
    return await _apiService.get(
        "$baseURL/home/<USER>/data?$filterName=$ids&offset=$offSet&limit=$limit&userId=$userId");
  }
}
