import 'dart:developer';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/reusableWidgets/appbar.dart';
import 'package:eljunto/views/clubs/clubs_home/providers/clubs_home_provider.dart';
import 'package:eljunto/views/clubs/clubs_home/services/clubs_sync_service.dart';
import 'package:eljunto/views/clubs/clubs_home/widgets/club_invitations_section.dart';
import 'package:eljunto/views/clubs/clubs_home/widgets/impromptu_clubs_section.dart';
import 'package:eljunto/views/clubs/clubs_home/widgets/section_header.dart';
import 'package:eljunto/views/clubs/clubs_home/widgets/standing_clubs_section.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

/// Refactored Clubs Home Screen
/// Uses Provider pattern with cross-screen state synchronization and proper separation of concerns
class ClubsHomeScreen extends StatefulWidget {
  const ClubsHomeScreen({super.key});

  @override
  State<ClubsHomeScreen> createState() => _ClubsHomeScreenState();
}

class _ClubsHomeScreenState extends State<ClubsHomeScreen>
    with ClubSyncListener {
  @override
  void initState() {
    super.initState();
    initializeSyncListeners();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      log('INIT CALLED');
      final provider = Provider.of<ClubsHomeProvider>(context, listen: false);
      provider.initialize(context);
      provider.loadInitialData(context);
    });
  }

  @override
  void onClubSyncEvent(ClubSyncEvent event) {
    super.onClubSyncEvent(event);
    log('ClubsHomeScreen: Received sync event - ${event.type}');

    // Handle specific sync events if needed
    final provider = Provider.of<ClubsHomeProvider>(context, listen: false);

    switch (event.type) {
      case ClubSyncEventType.userLeftClub:
      case ClubSyncEventType.clubDataUpdated:
      case ClubSyncEventType.membershipChanged:
      case ClubSyncEventType.clubCreated:
        // Refresh clubs data when changes occur
        log('ClubsHomeScreen: Refreshing data due to ${event.type}');
        if (mounted) {
          provider.refreshAllData(context);
        }
        break;
      default:
        break;
    }
  }

  @override
  void onRefreshRequested(bool shouldRefresh) {
    super.onRefreshRequested(shouldRefresh);
    if (shouldRefresh && mounted) {
      final provider = Provider.of<ClubsHomeProvider>(context, listen: false);
      provider.refreshAllData(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ClubsHomeProvider>(
      builder: (context, provider, child) {
        return Container(
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(AppConstants.bgImagePath),
              filterQuality: FilterQuality.high,
              fit: BoxFit.cover,
            ),
          ),
          child: Skeletonizer(
            effect: SoldColorEffect(
              color: AppConstants.skeletonforgroundColor,
              lowerBound: 0.1,
              upperBound: 0.5,
            ),
            enabled: provider.isInitialLoading,
            child: Scaffold(
              backgroundColor: Colors.transparent,
              appBar: PreferredSize(
                preferredSize: const Size.fromHeight(80),
                child: Container(
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        width: 1.5,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  ),
                  child: const AppBarWidget(
                    appBarText: 'Clubs',
                  ),
                ),
              ),
              body: CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 25),

                        // Club Invitations Section
                        const ClubInvitationsSection(),

                        const SizedBox(height: 20),

                        // Divider
                        _buildDivider(),

                        const SizedBox(height: 20),

                        // Standing Book Clubs Section
                        SectionHeader(
                          title: "Standing Book Clubs",
                          clubType: ClubType.standing.value,
                        ),

                        const SizedBox(height: 10),

                        const StandingClubsSection(),

                        const SizedBox(height: 25),

                        // Divider
                        _buildDivider(),

                        const SizedBox(height: 20),

                        // Impromptu Book Clubs Section
                        SectionHeader(
                          title: "Impromptu Book Clubs",
                          clubType: ClubType.impromptu.value,
                        ),

                        const SizedBox(height: 10),

                        const ImpromptuClubsSection(),

                        const SizedBox(height: 25),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build divider with skeleton support
  Widget _buildDivider() {
    return const Skeleton.replace(
      replacement: Divider(
        thickness: 2,
        color: AppConstants.skeletonBackgroundColor,
      ),
      child: Divider(
        thickness: 1.5,
        color: AppConstants.primaryColor,
      ),
    );
  }

  /// Handle pull-to-refresh
  Future<void> _handleRefresh(ClubsHomeProvider provider) async {
    await provider.refreshAllData(context);
  }

  @override
  void dispose() {
    disposeSyncListeners();
    super.dispose();
  }
}
