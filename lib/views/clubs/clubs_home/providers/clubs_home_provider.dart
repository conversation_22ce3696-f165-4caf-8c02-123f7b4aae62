import 'dart:async';
import 'dart:developer';

import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/controller/session_manager.dart';
import 'package:eljunto/controller/user_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import 'package:eljunto/reusable_api_function/club/club_function.dart';
import 'package:eljunto/views/clubs/clubs_home/services/clubs_sync_service.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

/// Dedicated provider for Clubs Home Screen
/// Implements targeted refresh methods and cross-screen state synchronization
class ClubsHomeProvider with ChangeNotifier {
  // Dependencies
  BookClubController? _bookClubController;
  MessageController? _messageController;
  UserController? _userController;
  ClubController? _clubController;

  // Sync service for cross-screen communication
  final ClubsSyncService _syncService = ClubsSyncService();
  StreamSubscription<ClubSyncEvent>? _syncSubscription;
  StreamSubscription<bool>? _refreshSubscription;

  // User data
  int? _loggedInUserId;
  String _userProfilePicture = '';
  String _userHandle = '';

  // Clubs data
  List<BookClubModel>? _standingBookClubList = [];
  List<BookClubModel>? _impromptuBookClubList = [];

  // Invitations data
  List<RequestManage>? _pendingInvitations;
  List<RequestManage>? _outgoingRequestList;

  // Loading states
  bool _isInitialLoading = true;
  bool _isStandingClubsLoading = false;
  bool _isImpromptuClubsLoading = false;
  bool _isInvitationsLoading = false;
  bool _isUserDataLoading = false;

  // Pagination
  int _standingClubCount = 0;
  int _impromptuClubCount = 0;

  // Getters
  int? get loggedInUserId => _loggedInUserId;

  String get userProfilePicture => _userProfilePicture;

  String get userHandle => _userHandle;

  List<BookClubModel>? get standingBookClubList => _standingBookClubList;

  List<BookClubModel>? get impromptuBookClubList => _impromptuBookClubList;

  List<RequestManage>? get pendingInvitations => _pendingInvitations;

  List<RequestManage>? get outgoingRequestList => _outgoingRequestList;

  bool get isInitialLoading => _isInitialLoading;

  bool get isStandingClubsLoading => _isStandingClubsLoading;

  bool get isImpromptuClubsLoading => _isImpromptuClubsLoading;

  bool get isInvitationsLoading => _isInvitationsLoading;

  bool get isUserDataLoading => _isUserDataLoading;

  int get standingClubCount => _standingClubCount;

  int get impromptuClubCount => _impromptuClubCount;

  bool get hasStandingClubs => _standingBookClubList?.isNotEmpty ?? false;

  bool get hasImpromptuClubs => _impromptuBookClubList?.isNotEmpty ?? false;
  final _sessionManager = locator<SessionManager>();

  /// Initialize the provider with required dependencies
  void initialize(BuildContext context) {
    _bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    _messageController = Provider.of<MessageController>(context, listen: false);
    _userController = Provider.of<UserController>(context, listen: false);
    _clubController = Provider.of<ClubController>(context, listen: false);

    // Initialize cross-screen sync listeners
    _initializeSyncListeners();
  }

  /// Initialize cross-screen synchronization listeners
  void _initializeSyncListeners() {
    _syncSubscription = _syncService.syncStream.listen(_handleSyncEvent);
    _refreshSubscription =
        _syncService.refreshStream.listen(_handleRefreshRequest);
  }

  /// Handle sync events from other screens
  void _handleSyncEvent(ClubSyncEvent event) {
    log('ClubsHomeProvider: Handling sync event - ${event.type}');

    switch (event.type) {
      case ClubSyncEventType.userLeftClub:
        _handleUserLeftClub(event.data);
        break;
      case ClubSyncEventType.clubDataUpdated:
        _handleClubDataUpdated(event.data);
        break;
      case ClubSyncEventType.clubCreated:
        _handleClubCreated(event.data);
        break;
      case ClubSyncEventType.membershipChanged:
        _handleMembershipChanged(event.data);
        break;
      case ClubSyncEventType.invitationStatusChanged:
        _handleInvitationStatusChanged(event.data);
        break;
      case ClubSyncEventType.generalRefresh:
        refreshAllData();
        break;
    }
  }

  /// Handle refresh requests from other screens
  void _handleRefreshRequest(bool shouldRefresh) {
    if (shouldRefresh) {
      log('ClubsHomeProvider: Handling refresh request');
      refreshAllData();
    }
  }

  /// Handle user leaving a club
  void _handleUserLeftClub(Map<String, dynamic>? data) {
    if (data == null) return;

    final clubId = data['clubId'] as int?;
    final clubType = data['clubType'] as String?;

    if (clubId == null || clubType == null) return;

    // Remove club from appropriate list
    if (clubType == ClubType.standing.value) {
      _standingBookClubList?.removeWhere((club) => club.bookClubId == clubId);
      _standingClubCount = _standingBookClubList?.length ?? 0;
    } else if (clubType == ClubType.impromptu.value) {
      _impromptuBookClubList?.removeWhere((club) => club.bookClubId == clubId);
      _impromptuClubCount = _impromptuBookClubList?.length ?? 0;
    }

    notifyListeners();
  }

  /// Handle club data updates
  void _handleClubDataUpdated(Map<String, dynamic>? data) {
    if (data == null) return;

    final clubId = data['clubId'] as int?;
    final clubType = data['clubType'] as String?;

    if (clubId == null || clubType == null) return;

    // Refresh specific club data
    _refreshSpecificClub(clubId, clubType);
  }

  /// Handle new club creation
  void _handleClubCreated(Map<String, dynamic>? data) {
    if (data == null) return;

    // Request a general refresh since we need context for API calls
    _syncService.requestRefresh();
  }

  /// Handle membership changes
  void _handleMembershipChanged(Map<String, dynamic>? data) {
    if (data == null) return;

    final clubId = data['clubId'] as int?;
    final clubType = data['clubType'] as String?;

    if (clubId == null || clubType == null) return;

    // Refresh specific club data to update member counts
    _refreshSpecificClub(clubId, clubType);
  }

  /// Handle invitation status changes
  void _handleInvitationStatusChanged(Map<String, dynamic>? data) {
    if (data == null) return;

    final invitationType = data['invitationType'] as String?;

    if (invitationType == 'incoming') {
      refreshIncomingInvitations();
    } else if (invitationType == 'outgoing') {
      refreshOutgoingRequests();
    }
  }

  /// Refresh specific club data
  void _refreshSpecificClub(int clubId, String clubType) {
    // This would ideally make a targeted API call for a specific club
    // For now, refresh the entire list
    if (clubType == ClubType.standing.value) {
      refreshStandingClubs();
    } else if (clubType == ClubType.impromptu.value) {
      refreshImpromptuClubs();
    }
  }

  /// Initialize user data from local storage
  Future<void> initializeUserData() async {
    _isUserDataLoading = true;
    notifyListeners();

    try {
      _loggedInUserId = _sessionManager.userId;
      await _getUserDetails();
    } catch (e) {
      log('Error initializing user data: $e');
    } finally {
      _isUserDataLoading = false;
      notifyListeners();
    }
  }

  /// Get user details
  Future<void> _getUserDetails([BuildContext? context]) async {
    try {
      // Skip user details if no context available (will be loaded later)
      if (context != null) {
        await _userController?.getUserDetailsByUserId(
            _loggedInUserId ?? 0, context);
        _userProfilePicture =
            _userController?.userModel.data?.userProfilePicture ?? '';
        _userHandle = _userController?.userModel.data?.userHandle ?? '';
      }
    } catch (e) {
      log('Error getting user details: $e');
    }
  }

  /// Load all initial data
  Future<void> loadInitialData(BuildContext context) async {
    _isInitialLoading = true;
    notifyListeners();

    try {
      await initializeUserData();
      await _getUserDetails(context);

      // Load all data concurrently for better performance
      await Future.wait(
        [
          getStandingBookClubsByUserId(false, context),
          getImpromptuBookClubsByUserId(false, context),
          getOutgoingRequests(context),
        ],
      );
    } catch (e) {
      log('Error loading initial data: $e');
    } finally {
      _isInitialLoading = false;
      notifyListeners();
    }
  }

  /// Get standing book clubs with pagination support
  Future<void> getStandingBookClubsByUserId(
      bool isMore, BuildContext? context) async {
    // Check cache first
    // final cacheKey = 'standing_clubs_${_loggedInUserId}_$isMore';
    // final cachedData =
    //     _syncService.getCachedData<List<BookClubModel>>(cacheKey);
    //
    // if (cachedData != null && !isMore) {
    //   _standingBookClubList = cachedData;
    //   notifyListeners();
    //   return;
    // }

    _isStandingClubsLoading = true;
    if (isMore) {}
    notifyListeners();

    try {
      if (context != null) {
        await _clubController?.getBookClubsByUserId(
          context,
          _loggedInUserId ?? 0,
          ClubType.standing.value,
          isMore,
        );
      }

      _standingBookClubList = _clubController?.standingBookClubList;
      _standingClubCount = _clubController?.standingClubCount ?? 0;

      // Cache the data
      // if (_standingBookClubList != null) {
      //   _syncService.cacheClubsData(cacheKey, _standingBookClubList);
      // }

      // Check for incoming requests
      await _updateStandingClubRequests();
    } catch (e) {
      log('Error getting standing clubs: $e');
    } finally {
      _isStandingClubsLoading = false;
      notifyListeners();
    }
  }

  /// Get impromptu book clubs with pagination support
  Future<void> getImpromptuBookClubsByUserId(
      bool isMore, BuildContext? context) async {
    // Check cache first
    // final cacheKey = 'impromptu_clubs_${_loggedInUserId}_$isMore';
    // final cachedData =
    //     _syncService.getCachedData<List<BookClubModel>>(cacheKey);
    //
    // if (cachedData != null && !isMore) {
    //   _impromptuBookClubList = cachedData;
    //   notifyListeners();
    //   return;
    // }

    _isImpromptuClubsLoading = true;
    if (isMore) {}
    notifyListeners();

    try {
      if (context != null) {
        await _clubController?.getBookClubsByUserId(
          context,
          _loggedInUserId ?? 0,
          ClubType.impromptu.value,
          isMore,
        );
      }

      _impromptuBookClubList = _clubController?.impromptuBookClubList;
      _impromptuClubCount = _clubController?.impromptuClubCount ?? 0;

      // Cache the data
      // if (_impromptuBookClubList != null) {
      //   _syncService.cacheClubsData(cacheKey, _impromptuBookClubList);
      // }

      // Check for incoming requests
      await _updateImpromptuClubRequests();
    } catch (e) {
      log('Error getting impromptu clubs: $e');
    } finally {
      _isImpromptuClubsLoading = false;
      notifyListeners();
    }
  }

  /// Get outgoing requests
  Future<void> getOutgoingRequests(BuildContext? context) async {
    _isInvitationsLoading = true;
    notifyListeners();

    try {
      if (context != null) {
        await _bookClubController?.inComingRequest(
          _loggedInUserId ?? 0,
          ClubMembershipStatus.pending.value,
          ClubMembershipStatus.reOpened.value,
          ClubRequestType.outgoingClubRequestByUserId.value,
          context,
        );
      }

      _outgoingRequestList = _bookClubController?.incomingOutGoingRequest?.data;

      // Check status and update message controller
      final hasNewOutgoingRequests = await _checkOutgoingRequestStatus();
      await _messageController?.updateOutgoingRequests(hasNewOutgoingRequests);
    } catch (e) {
      log('Error getting outgoing requests: $e');
    } finally {
      _isInvitationsLoading = false;
      notifyListeners();
    }
  }

  /// Get incoming invitations
  Future<void> getIncomingInvitations(BuildContext? context) async {
    try {
      if (context != null) {
        await _bookClubController?.inComingRequest(
          _loggedInUserId ?? 0,
          ClubMembershipStatus.pending.value,
          ClubMembershipStatus.reOpened.value,
          ClubRequestType.incomingClubRequestByUserId.value,
          context,
        );
      }

      _pendingInvitations = _bookClubController?.incomingOutGoingRequest?.data;

      // Update notification status
      await _messageController?.incomingClubInvitationStatus(
        _pendingInvitations?.isNotEmpty ?? false,
      );
    } catch (e) {
      log('Error getting incoming invitations: $e');
    }
  }

  /// Update standing club requests status
  Future<void> _updateStandingClubRequests() async {
    final hasNewStandingRequests = _standingBookClubList?.any((element) =>
            element.incomingRequest == true &&
            element.userId == _loggedInUserId) ??
        false;

    await _messageController
        ?.updateStandingClubRequests(hasNewStandingRequests);
  }

  /// Update impromptu club requests status
  Future<void> _updateImpromptuClubRequests() async {
    final hasNewImpromptuRequests = _impromptuBookClubList?.any((element) =>
            element.incomingRequest == true &&
            element.userId == _loggedInUserId) ??
        false;

    await _messageController
        ?.updateImpromptuClubRequests(hasNewImpromptuRequests);
  }

  /// Check outgoing request status
  Future<bool> _checkOutgoingRequestStatus() async {
    if (_outgoingRequestList?.isNotEmpty ?? false) {
      for (var element in _outgoingRequestList!) {
        if (element.status == "REOPENED") {
          return true;
        }
      }
    }
    return false;
  }

  /// Refresh all data
  Future<void> refreshAllData([BuildContext? context]) async {
    // Clear cache
    _syncService.invalidateCache();

    // Refresh all data concurrently
    await Future.wait(
      [
        refreshStandingClubs(context),
        refreshImpromptuClubs(context),
        refreshOutgoingRequests(context),
      ],
    );
  }

  /// Targeted refresh methods
  Future<void> refreshStandingClubs([BuildContext? context]) async {
    await getStandingBookClubsByUserId(false, context);
  }

  Future<void> refreshImpromptuClubs([BuildContext? context]) async {
    await getImpromptuBookClubsByUserId(false, context);
  }

  Future<void> refreshIncomingInvitations([BuildContext? context]) async {
    await getIncomingInvitations(context);
  }

  Future<void> refreshOutgoingRequests([BuildContext? context]) async {
    await getOutgoingRequests(context);
  }

  /// Load more standing clubs (pagination)
  Future<void> loadMoreStandingClubs(BuildContext context) async {
    if (!_isStandingClubsLoading &&
        (_standingBookClubList?.length ?? 0) < _standingClubCount) {
      await getStandingBookClubsByUserId(true, context);
    }
  }

  /// Load more impromptu clubs (pagination)
  Future<void> loadMoreImpromptuClubs(BuildContext context) async {
    if (!_isImpromptuClubsLoading &&
        (_impromptuBookClubList?.length ?? 0) < _impromptuClubCount) {
      await getImpromptuBookClubsByUserId(true, context);
    }
  }

  @override
  void dispose() {
    _syncSubscription?.cancel();
    _refreshSubscription?.cancel();
    super.dispose();
  }
}
