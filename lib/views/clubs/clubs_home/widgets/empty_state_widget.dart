import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:flutter/material.dart';

/// Widget for displaying empty state when no clubs are available
class EmptyStateWidget extends StatelessWidget {
  final String message;

  const EmptyStateWidget({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 20),
      margin: const EdgeInsets.symmetric(horizontal: 20),
      constraints: BoxConstraints(
        minHeight: 50,
        maxWidth: MediaQuery.of(context).size.width,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: IntrinsicHeight(
        child: Align(
          alignment: Alignment.centerLeft,
          child: Text(
            message,
            textAlign: TextAlign.start,
            style: lbBold.copyWith(
              fontSize: 14,
              color: AppConstants.primaryColor,
            ),
          ),
        ),
      ),
    );
  }

  /// Create skeleton version for loading state
  static Widget skeleton({
    required BuildContext context,
    required String message,
  }) {
    return Container(
      padding: const EdgeInsets.only(left: 20),
      margin: const EdgeInsets.symmetric(horizontal: 20),
      constraints: BoxConstraints(
        minHeight: 50,
        maxWidth: MediaQuery.of(context).size.width,
      ),
      decoration: BoxDecoration(
        color: AppConstants.skeletonBackgroundColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: IntrinsicHeight(
        child: Align(
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Text(
              message,
              textAlign: TextAlign.start,
              style: lbRegular.copyWith(
                fontSize: 16,
                color: AppConstants.skeletonforgroundColor,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
