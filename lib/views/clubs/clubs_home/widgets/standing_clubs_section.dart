import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/reusableWidgets/club_ui/standing_club_info.dart';
import 'package:eljunto/views/clubs/clubs_home/providers/clubs_home_provider.dart';
import 'package:eljunto/views/clubs/clubs_home/widgets/empty_state_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

/// Widget for displaying standing clubs section with horizontal scrolling
class StandingClubsSection extends StatefulWidget {
  const StandingClubsSection({super.key});

  @override
  State<StandingClubsSection> createState() => _StandingClubsSectionState();
}

class _StandingClubsSectionState extends State<StandingClubsSection> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /// Handle scroll events for pagination
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent) {
      final provider = Provider.of<ClubsHomeProvider>(context, listen: false);
      provider.loadMoreStandingClubs(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ClubsHomeProvider>(
      builder: (context, provider, child) {
        // Show skeleton placeholders during initial loading
        if (provider.isInitialLoading) {
          return _buildSkeletonClubsList();
        }

        // Show actual clubs if available
        if (provider.hasStandingClubs) {
          return _buildClubsList(provider);
        }

        // Show empty state only when loading is complete and no clubs exist
        return _buildEmptyState(provider);
      },
    );
  }

  /// Build skeleton placeholders during initial loading
  Widget _buildSkeletonClubsList() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: List.generate(
          3, // Show 3 skeleton cards
          (index) => Padding(
            padding: const EdgeInsets.only(right: 10.0),
            child: Container(
              width: 320,
              height: 200,
              decoration: BoxDecoration(
                color: AppConstants.skeletonBackgroundColor,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: AppConstants.skeletonforgroundColor
                      .withValues(alpha: 0.3),
                  width: 1.5,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Club name skeleton
                    Container(
                      width: 180,
                      height: 16,
                      decoration: BoxDecoration(
                        color: AppConstants.skeletonforgroundColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Book title skeleton
                    Container(
                      width: 140,
                      height: 14,
                      decoration: BoxDecoration(
                        color: AppConstants.skeletonforgroundColor
                            .withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Member count and status skeleton
                    Row(
                      children: [
                        Container(
                          width: 80,
                          height: 12,
                          decoration: BoxDecoration(
                            color: AppConstants.skeletonforgroundColor
                                .withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const Spacer(),
                        Container(
                          width: 60,
                          height: 12,
                          decoration: BoxDecoration(
                            color: AppConstants.skeletonforgroundColor
                                .withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build the clubs list with horizontal scrolling
  Widget _buildClubsList(ClubsHomeProvider provider) {
    return SingleChildScrollView(
      controller: _scrollController,
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.only(left: 10, right: 20),
      child: Row(
        children: [
          // Club cards
          ...List.generate(
            provider.standingBookClubList?.length ?? 0,
            (index) {
              final club = provider.standingBookClubList![index];
              return Skeleton.replace(
                replacement: StandingClubMeetingInfo(
                  bookClubModel: club,
                  loggedinUserId: provider.loggedInUserId,
                  isLoadingSkeleton: true,
                ),
                child: StandingClubMeetingInfo(
                  bookClubModel: club,
                  loggedinUserId: provider.loggedInUserId,
                  userProfilePicture: provider.userProfilePicture,
                  userHandle: provider.userHandle,
                ),
              );
            },
          ),

          // Loading indicator for pagination
          if (provider.isStandingClubsLoading)
            const Padding(
              padding: EdgeInsets.only(left: 10.0),
              child: Center(
                child: CircularProgressIndicator(
                  color: AppConstants.primaryColor,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Build empty state when no clubs are available
  Widget _buildEmptyState(ClubsHomeProvider provider) {
    return Skeleton.replace(
      replacement: EmptyStateWidget.skeleton(
        context: context,
        message: "Join or create a standing club",
      ),
      child: const EmptyStateWidget(
        message: "Join or create a standing club",
      ),
    );
  }
}
