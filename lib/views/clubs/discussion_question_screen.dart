import 'dart:io';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class DiscussionQueScreen extends StatefulWidget {
  final String? bookClubName;
  final String? discussionQuestions;
  const DiscussionQueScreen({
    super.key,
    this.bookClubName,
    this.discussionQuestions,
  });

  @override
  State<DiscussionQueScreen> createState() => _DiscussionScreeQuenState();
}

class _DiscussionScreeQuenState extends State<DiscussionQueScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: PreviousScreenAppBar(
            bookName: widget.bookClubName,
            isSetProfile: true,
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                AppConstants.bgImagePath,
              ),
              filterQuality: FilterQuality.high,
              fit: BoxFit.cover,
            ),
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.only(left: 20.0, right: 20, bottom: 25),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 25,
                  ),
                  Text(
                    'Discussion Questions',
                    style: lbRegular.copyWith(
                      fontSize: 20,
                    ),
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  SelectableText(
                    widget.discussionQuestions ?? '',
                    textAlign: TextAlign.start,
                    style: lbRegular.copyWith(fontSize: 14),
                    selectionControls: Platform.isIOS
                        ? cupertinoTextSelectionControls
                        : materialTextSelectionControls,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
