import 'dart:developer';
import 'dart:io';

import 'package:animated_custom_dropdown/custom_dropdown.dart'
    as custom_dropdown;
import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/controller/book_case_controller.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/session_manager.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/manage__meeting_model.dart';
import 'package:eljunto/models/home_model/home_screen2_model/meeting_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:eljunto/reusableWidgets/paginated_book_typeahead.dart';
import 'package:eljunto/reusableWidgets/question_feedback_dialog.dart';
import 'package:eljunto/reusable_api_function/books_api_functions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../../models/profile_model/edit_bookcase/listof_book_model.dart';
import '../../../reusableWidgets/previous_screen_appbar.dart';
import '../user_club_details/providers/user_club_details_provider.dart';

class AddEditMeetingScreen extends StatefulWidget {
  final String? addEditName;
  final MeetingList? upcomingMeetings;
  final bool editMeetingFlag;
  final int? editMeetngId;

  const AddEditMeetingScreen({
    super.key,
    this.addEditName,
    this.upcomingMeetings,
    this.editMeetingFlag = false,
    this.editMeetngId,
  });

  @override
  State<AddEditMeetingScreen> createState() => _AddEditMeetingScreenState();
}

class User {
  final String name;
  final int id;

  User({
    required this.name,
    required this.id,
  });
}

class _AddEditMeetingScreenState extends State<AddEditMeetingScreen> {
  final partCoveredController = TextEditingController();
  final alertController = TextEditingController();
  final meetingDateController = TextEditingController();

  // final tempDateController = TextEditingController();
  final tempStartTimeController = TextEditingController();
  final tempEndTimeController = TextEditingController();
  final startTimeController = TextEditingController();

  // final endTimeController = TextEditingController();
  final durationController = TextEditingController();
  final discussController = TextEditingController();
  final bookController = TextEditingController();
  final tbdController = ValueNotifier<bool>(false);
  final alertMultiController = custom_dropdown.MultiSelectController([]);
  List<Map<String, String>> durationList = [];
  String iosDatePick = '';
  String? durationKey;
  bool isLoading = false;
  bool isBookIdNotEmpty = false;
  List<String> selectedOptions = [
    '15 Min',
    '1 Day',
    '1 Week',
  ];

  DateTime? formatedTime;
  int? bookId = 0;
  List<Books>? bookList = [];
  BookClubModel? bookClubModel;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool bookValidation = false;
  bool bookCoveredValidation = false;
  bool meetingStartValidation = false;
  bool meetingAlertValidation = false;
  bool discussionValidation = false;
  DateTime? pickDate;
  TimeOfDay? pickedStartTime;

  // TimeOfDay? pickedEndTime;
  List<MeetingList> upcomingMeetings = [];
  List<MeetingList> previousMeetings = [];
  DateTime? previousStartTime;
  DateTime? previousEndTime;
  SuggestionsController<Books> suggestionsController = SuggestionsController();
  BookCaseController? bookCaseController;
  int limit = 10;
  int offSet = 0;

  Set<String> filters = {'15 Min', '1 Day', '1 Week'};
  String channelName = '';
  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    _initializeUserId();
    super.initState();
    durationList = generateTimeIntervalsMap();
    // _generateStartTimeOptions();
    _generateTimeOptions();

    bookClubModel =
        Provider.of<BookClubController>(context, listen: false).bookClubModel;
    if (widget.editMeetingFlag) {
      assignData();
    } else {
      filters.addAll(selectedOptions);
      partCoveredController.text = "TBD";
      meetingDateController.text = "TBD";
      startTimeController.text = "TBD";
      durationController.text = "TBD";
      // endTimeController.text = "TBD";
      meetingDateController.text = "TBD";
      discussController.text = "TBD";
    }
  }

  int? logginedInUserId;

  Future<void> _initializeUserId() async {
    logginedInUserId = _sessionManager.userId;
  }

  String formatedDateFunction(DateTime date) {
    String daySuffix(int day) {
      if (day >= 11 && day <= 13) {
        return 'th';
      }
      switch (day % 10) {
        case 1:
          return 'st';
        case 2:
          return 'nd';
        case 3:
          return 'rd';
        default:
          return 'th';
      }
    }

    String formattedDate = DateFormat('EEE MMM d').format(date);
    String dayWithSuffix = daySuffix(pickDate?.day ?? 0);
    String yearAndTime = DateFormat('yyyy').format(
      pickDate ?? DateTime.now(),
    );
    return '$formattedDate$dayWithSuffix, $yearAndTime';
  }

  void assignData() {
    bookController.text = widget.upcomingMeetings?.bookName ?? '';
    log("Bok Name : ${bookController.text}");

    /// PASS BOOK ID 16 DEC 2024
    // bookId = widget.upcomingMeetings?.bookClubId ?? 0;
    log("Book ID : $bookId");
    partCoveredController.text =
        widget.upcomingMeetings?.partOfBookCovered ?? '';
    channelName = widget.upcomingMeetings?.channelName ?? '';
    print("Channel Name : $channelName");

    if (widget.upcomingMeetings?.meetingDate == null) {
      meetingDateController.text = "TBD";
    } else {
      final tempMeetingdate = DateTime.fromMillisecondsSinceEpoch(
          widget.upcomingMeetings?.meetingDate ?? 0);
      meetingDateController.text = formatedDateFunction(tempMeetingdate);
      // print("Update Meeting Date : ${tempMeetingdate}");

      pickDate = tempMeetingdate;
      _generateTimeOptions();
      setState(() {});
    }

    if (widget.upcomingMeetings?.meetingStartTime == null) {
      startTimeController.text = "TBD";
    } else {
      final tempStartTime = DateTime.fromMillisecondsSinceEpoch(
          widget.upcomingMeetings?.meetingStartTime ?? 0);

      startTimeController.text = DateFormat('h:mm a').format(tempStartTime);

      pickedStartTime = parseTimeOfDay(startTimeController.text);
      // print("Edit Start Time : $pickedStartTime");
    }

    if (widget.upcomingMeetings?.meetingDuration == null) {
      durationController.text = "TBD";
    } else {
      durationKey = widget.upcomingMeetings?.meetingDuration ?? '';
      for (int i = 0; i < durationList.length; i++) {
        if (durationList[i].containsKey(durationKey)) {
          durationController.text = durationList[i][durationKey] ?? '';
          break;
        }
      }
    }

    // Calculate alert times based on start time
    if (widget.upcomingMeetings?.meetingAlerts?.isNotEmpty ?? false) {
      selectedOptions.clear();
    }
    widget.upcomingMeetings?.meetingAlerts?.map((alertTime) {
          filters.add(alertTime);
          selectedOptions.add(alertTime);
          // final difference = tempStartTime.difference(alertDateTime);
        }).toList() ??
        [];

    // print("Alert Value : ${alertMultiController.value}");

    discussController.text = widget.upcomingMeetings?.discussionQuestions ?? '';
    setState(() {});
  }

  TimeOfDay parseTimeOfDay(String timeString) {
    final format = DateFormat('h:mm a'); // For 12-hour format with AM/PM
    final DateTime parsedTime = format.parse(timeString);
    // print("End Time : $parsedTime");
    return TimeOfDay.fromDateTime(parsedTime);
  }

  Future updateClubData() async {
    previousMeetings = Provider.of<BookClubController>(context, listen: false)
            .previousMeetingList ??
        [];
    upcomingMeetings = Provider.of<BookClubController>(context, listen: false)
            .upComingMeetingList ??
        [];
    bookClubModel =
        Provider.of<BookClubController>(context, listen: false).bookClubModel;
  }

  Future<List<String>> getAlertOptions(DateTime selectedMeetingDate) async {
    DateTime now = DateTime.now();
    log("Generate Alert Options : $selectedMeetingDate");
    // Calculate the difference between current date and selected meeting date
    Duration difference = selectedMeetingDate.difference(now);

    List<String> alertOptions = [];

    // If the meeting is more than 1 day away, show all options
    if (difference.inDays >= 7) {
      alertOptions = [
        '15 Min',
        '1 Day',
        '1 Week',
      ];
      filters = alertOptions.toSet();
    }
    // If the meeting is set for tomorrow, show 1 Day and 15 Min options
    else if (difference.inHours >= 24 && difference.inDays < 7) {
      alertOptions = [
        '15 Min',
        '1 Day',
      ];
      filters = alertOptions.toSet();
    }
    // If the meeting is today, show only the 15 Min option
    else if (difference.inHours <= 24) {
      alertOptions = ['15 Min'];
      filters = alertOptions.toSet();
    }

    return alertOptions;
  }

  Future<void> scheduleMeeting() async {
    if (conditionCheck()) {
      durationValidateFunction();
      return;
    }
    bool isSuccess = false;
    DateTime? startTime;
    if (pickDate != null && pickedStartTime != null) {
      startTime = DateTime(
        pickDate?.year ?? 0,
        pickDate?.month ?? 0,
        pickDate?.day ?? 0,
        pickedStartTime?.hour ?? 0,
        pickedStartTime?.minute ?? 0,
      );
    } else {
      startTime = null;
    }

    if (bookClubModel?.bookClubType == ClubType.impromptu.value) {
      bookId = bookClubModel?.bookId;
    }

    /// ADD OR SAVE MEETING CONDITION
    if (widget.editMeetingFlag) {
      isLoading = true;
      setState(() {});
      final payload = ManageMeetingModel(
        meetingId: widget.editMeetngId,
        bookClubId: bookClubModel?.bookClubId,
        bookId: bookId,
        discussionQuestions: discussController.text,
        meetingAlerts: filters.toList(),
        meetingDate: startTime?.millisecondsSinceEpoch,
        meetingStartTime: startTime?.millisecondsSinceEpoch,
        meetingDuration: durationKey,
        meetingStatus: 'SCHEDULED',
        partOfBookCovered: partCoveredController.text,
        userId: logginedInUserId,
        channelName: channelName,
      );

      isSuccess = await Provider.of<BookClubController>(context, listen: false)
          .updateMeeting(payload, context);
    } else {
      if (bookClubModel?.bookClubType == ClubType.standing.value) {
        if (bookController.text.isNotEmpty && bookId == 0) {
          isBookIdNotEmpty = true;
          setState(() {});
          return;
        }
      }

      final payload = ManageMeetingModel(
        bookClubId: bookClubModel?.bookClubId,
        bookId: bookId,
        discussionQuestions: discussController.text,
        meetingAlerts: filters.toList(),
        meetingDate: startTime?.millisecondsSinceEpoch,
        meetingStartTime: startTime?.millisecondsSinceEpoch,
        meetingDuration: durationKey,
        // meetingEndTime: endTime?.millisecondsSinceEpoch,
        meetingStatus: 'SCHEDULED',
        partOfBookCovered: partCoveredController.text,
        userId: logginedInUserId,
      );
      isLoading = true;
      log("Meeting payload : $payload");
      setState(() {});
      isSuccess = await Provider.of<BookClubController>(context, listen: false)
          .scheduleMeeting(payload, context);
    }
    log("isSuccess : $isSuccess");
    if (isSuccess) {
      await updateClubData();
      await updateClubData();
      if (mounted) {
        await Provider.of<UserClubDetailsProvider>(context, listen: false)
            .refreshSection(
                'upcoming_meetings', bookClubModel?.bookClubId ?? 0, context);
      }
      if (mounted) {
        context.pop();
      }
    }
    isLoading = false;
    setState(() {});
  }

  bool conditionCheck() {
    log("Pickeddate : $pickDate");
    log("PickedStartTime : $pickedStartTime");
    log("DurationKey : $durationKey");
    if (pickDate == null && pickedStartTime == null && durationKey == null) {
      return false;
    } else if (pickDate == null ||
        pickedStartTime == null ||
        durationKey == null) {
      return true;
    }
    return false;
  }

  @override
  void dispose() {
    partCoveredController.dispose();
    alertController.dispose();
    meetingDateController.dispose();
    startTimeController.dispose();
    // endTimeController.dispose();
    durationController.dispose();
    discussController.dispose();
    bookController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: PreviousScreenAppBar(
            bookName: widget.addEditName,
            clubName: bookClubModel?.bookClubName,
            clubNameFlag: true,
            isSetProfile: true,
          ),
        ),
      ),
      body: GestureDetector(
        onVerticalDragStart: (details) =>
            FocusManager.instance.primaryFocus?.unfocus(),
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                "assets/images/PaperBackground.png",
              ),
              filterQuality: FilterQuality.high,
              fit: BoxFit.fitWidth,
            ),
          ),
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    bookClubModel?.bookClubType == ClubType.standing.value
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(
                                height: 25,
                              ),
                              Text(
                                'Name of the book',
                                style: lbRegular.copyWith(
                                  fontSize: 18,
                                ),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  typeAheadWidget(),
                                  Consumer<BookCaseController>(builder:
                                      (context, bookCaseController, child) {
                                    return Positioned(
                                      top: 55,
                                      left: 0,
                                      right: 0,
                                      child: bookCaseController.isTypeAheadEmpty
                                          ? Text(
                                              "*Select book",
                                              style: lbRegular.copyWith(
                                                fontSize: 14,
                                                color: AppConstants.redColor,
                                              ),
                                            )
                                          : const SizedBox.shrink(),
                                    );
                                  }),
                                  Positioned(
                                    top: 55,
                                    left: 0,
                                    right: 0,
                                    child: isBookIdNotEmpty && bookId == 0
                                        ? Text(
                                            "*Invalid book",
                                            style: lbRegular.copyWith(
                                              fontSize: 14,
                                              color: AppConstants.redColor,
                                            ),
                                          )
                                        : const SizedBox.shrink(),
                                  ),
                                ],
                              ),
                            ],
                          )
                        : const SizedBox.shrink(),
                    Consumer<BookCaseController>(
                        builder: (context, bookCaseController, child) {
                      return bookCaseController.isTypeAheadEmpty
                          ? const SizedBox(
                              height: 15,
                            )
                          : const SizedBox.shrink();
                    }),
                    isBookIdNotEmpty && bookId == 0
                        ? const SizedBox(
                            height: 15,
                          )
                        : const SizedBox.shrink(),
                    const SizedBox(
                      height: 25,
                    ),
                    Text(
                      'Part of the book covered',
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    bookCoveredWidget(),
                    bookCoveredValidation
                        ? const SizedBox(
                            height: 15,
                          )
                        : const SizedBox.shrink(),
                    const SizedBox(
                      height: 25,
                    ),
                    Text(
                      'Meeting start date',
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    meetingStartDateWidget(),
                    meetingStartValidation
                        ? const SizedBox(
                            height: 15,
                          )
                        : const SizedBox.shrink(),
                    const SizedBox(
                      height: 25,
                    ),
                    meetingStartTime(),
                    const SizedBox(
                      height: 25,
                    ),
                    meetingAlertWidget(),
                    const SizedBox(
                      height: 25,
                    ),
                    Text(
                      "Discussion questions",
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    discussQueWidget(),
                    discussionValidation
                        ? const SizedBox(
                            height: 15,
                          )
                        : const SizedBox.shrink(),
                    const SizedBox(
                      height: 25,
                    ),
                    actionButton(),
                    const SizedBox(
                      height: 25,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget actionButton() {
    return !isLoading
        ? Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NetworkAwareTap(
                onTap: () {
                  onSave();
                },
                child: Container(
                  height: 45,
                  width: MediaQuery.of(context).size.width / 2.5,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(49),
                    color: AppConstants.textGreenColor,
                  ),
                  child: Center(
                    child: Text(
                      (widget.editMeetingFlag) ? "Save" : "Add",
                      textAlign: TextAlign.center,
                      style: lbBold.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
              ),
              NetworkAwareTap(
                onTap: () {
                  context.pop();
                  // context.goNamed('ClubsScreen3');
                },
                child: Container(
                  height: 45,
                  width: MediaQuery.of(context).size.width / 2.5,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(49),
                    color: Colors.transparent,
                    border: Border.all(
                      color: AppConstants.primaryColor,
                      width: 1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      "Cancel",
                      textAlign: TextAlign.center,
                      style: lbBold.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          )
        : CustomLoaderButton(
            // loginText: 'Login',
            buttonWidth: isLoading ? 45.0 : MediaQuery.of(context).size.width,
            buttonRadius: 30.0,
            buttonChild: isLoading
                ? const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation(Colors.white),
                    strokeWidth: 3.0,
                  )
                : Text(
                    'Add',
                    style: lbBold.copyWith(
                      fontSize: 18,
                      color: AppConstants.primaryColor,
                    ),
                  ),
            buttonPressed: () {
              // bool validation = _formKey.currentState!.validate();
            },
          );
  }

  Widget typeAheadWidget() {
    return PaginatedBookTypeahead(
      suggestionsController: suggestionsController,
      controller: bookController,
      fetchBooksCallback: (query, offset, limit) =>
          BooksApiFunctions.fetchBooks(
        query,
        offset,
        limit,
        context,
        logginedInUserId,
      ),
      isAddNewMeeting: true,
      onSelected: (book) => _handleBookSelection(book),
      onCantFindBook: () => questionFeedBox(),
    );
  }

  void _handleBookSelection(Books book) {
    setState(() {
      bookController.text = book.bookName.toString();
      bookId = book.bookId;
      isBookIdNotEmpty = true;
      log("Book Club Id : $bookId");
    });
  }

  void questionFeedBox() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const QuestionFeedbackDialog(isBookQuery: true);
      },
    );
  }

  Widget bookCoveredWidget() {
    return TextFormField(
      controller: partCoveredController,
      style: lbRegular.copyWith(
        fontSize: 18,
      ),
      decoration: InputDecoration(
        filled: true,
        fillColor: const Color.fromRGBO(255, 255, 255, 1),
        contentPadding: const EdgeInsets.all(10),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: const BorderSide(
            color: AppConstants.primaryColor,
            width: 1.5,
          ),
        ),
      ),
    );
  }

  Widget meetingStartDateWidget() {
    return TextFormField(
      style: lbRegular.copyWith(
        fontSize: 18,
      ),
      controller: meetingDateController,
      decoration: InputDecoration(
        suffixIcon: const Icon(
          Icons.calendar_month_outlined,
          size: 25,
          color: AppConstants.primaryColor,
        ),
        filled: true,
        fillColor: const Color.fromRGBO(255, 255, 255, 1),
        contentPadding: const EdgeInsets.all(10),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: const BorderSide(
            color: AppConstants.primaryColor,
            width: 1.5,
          ),
        ),
      ),
      readOnly: true,
      onTap: () async {
        await selectDate(true);
        formatedTime = DateTime(
          pickDate?.year ?? 0,
          pickDate?.month ?? 0,
          pickDate?.day ?? 0,
          pickedStartTime?.hour ?? 0,
          pickedStartTime?.minute ?? 0,
        );
        selectedOptions = await getAlertOptions(formatedTime ?? DateTime.now());
        if (pickDate == null) {
          meetingDateController.text = 'TBD';
        }
      },
    );
  }

  Widget meetingStartTime() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Start time',
              style: lbRegular.copyWith(fontSize: 18),
            ),
            const SizedBox(height: 10),
            SizedBox(
              height: 50,
              width: MediaQuery.of(context).size.width / 2.5,
              child: DropdownMenu(
                inputDecorationTheme: const InputDecorationTheme(
                  alignLabelWithHint: true,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 10,
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  focusColor: AppConstants.primaryColor,
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: AppConstants.primaryColor,
                      width: 1.5,
                    ),
                    borderRadius: BorderRadius.all(
                      Radius.circular(10),
                    ),
                  ),
                ),
                menuStyle: const MenuStyle(
                  surfaceTintColor: WidgetStatePropertyAll(Colors.transparent),
                  shape: WidgetStatePropertyAll(
                    RoundedRectangleBorder(
                      side: BorderSide(
                        color: AppConstants.primaryColor,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.all(
                        Radius.circular(10),
                      ),
                    ),
                  ),
                  padding: WidgetStatePropertyAll(
                    EdgeInsets.zero,
                  ),
                  backgroundColor: WidgetStatePropertyAll(
                    Colors.white,
                  ),
                ),
                expandedInsets: EdgeInsets.zero,
                controller: startTimeController,
                requestFocusOnTap: false,
                textStyle: lbRegular.copyWith(
                  fontSize: 18,
                ),
                onSelected: (newValue) async {
                  startTimeController.text = newValue.toString();
                  if (startTimeController.text == "TBD") {
                    pickedStartTime = null;
                  }
                  if (newValue != "TBD") {
                    final selectedTimeOfDay =
                        _parseTimeOfDay(newValue.toString());
                    pickedStartTime = selectedTimeOfDay;
                    formatedTime = DateTime(
                      pickDate?.year ?? 0,
                      pickDate?.month ?? 0,
                      pickDate?.day ?? 0,
                      pickedStartTime?.hour ?? 0,
                      pickedStartTime?.minute ?? 0,
                    );

                    selectedOptions =
                        await getAlertOptions(formatedTime ?? DateTime.now());
                  } else {}
                  setState(() {});
                },
                dropdownMenuEntries: _timeOptions.map((role) {
                  return DropdownMenuEntry(
                    style: TextButton.styleFrom(
                      surfaceTintColor: Colors.white,
                      visualDensity: VisualDensity.comfortable,
                      side: const BorderSide(
                        width: 0.5,
                        color: AppConstants.primaryColor,
                      ),
                      textStyle: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                    value: role,
                    label: role,
                  );
                }).toList(),
                menuHeight: 200,
              ),
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Duration',
              style: lbRegular.copyWith(fontSize: 18),
            ),
            const SizedBox(height: 10),
            SizedBox(
              height: 50,
              width: MediaQuery.of(context).size.width / 2.5,
              child: DropdownMenu(
                inputDecorationTheme: const InputDecorationTheme(
                  alignLabelWithHint: true,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 10,
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  focusColor: AppConstants.primaryColor,
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: AppConstants.primaryColor,
                      width: 1.5,
                    ),
                    borderRadius: BorderRadius.all(
                      Radius.circular(10),
                    ),
                  ),
                ),
                menuStyle: const MenuStyle(
                  surfaceTintColor: WidgetStatePropertyAll(Colors.transparent),
                  shape: WidgetStatePropertyAll(
                    RoundedRectangleBorder(
                      side: BorderSide(
                        color: AppConstants.primaryColor,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.all(
                        Radius.circular(10),
                      ),
                    ),
                  ),
                  padding: WidgetStatePropertyAll(
                    EdgeInsets.zero,
                  ),
                  backgroundColor: WidgetStatePropertyAll(
                    Colors.white,
                  ),
                ),
                expandedInsets: EdgeInsets.zero,
                controller: durationController,
                requestFocusOnTap: false,
                textStyle: lbRegular.copyWith(
                  fontSize: 18,
                ),
                onSelected: (newValue) async {
                  durationController.text = newValue?.values.first ?? '';
                  durationKey = newValue?.keys.first;
                  if (durationController.text == "TBD") {
                    durationKey = null;
                  }

                  setState(() {});
                },
                dropdownMenuEntries: durationList.map((role) {
                  return DropdownMenuEntry(
                    style: TextButton.styleFrom(
                      visualDensity: VisualDensity.comfortable,
                      side: const BorderSide(
                        width: 0.5,
                        color: AppConstants.primaryColor,
                      ),
                      surfaceTintColor: Colors.white,
                      textStyle: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                    value: role,
                    label: role.values.first,
                  );
                }).toList(),
                menuHeight: 200,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget meetingAlertWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Meeting alerts',
          style: lbRegular.copyWith(
            fontSize: 18,
          ),
        ),
        const SizedBox(
          height: 10,
        ),
        SizedBox(
          height: 40,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: selectedOptions.length,
            itemBuilder: (context, index) {
              final option = selectedOptions[index];
              final isSelected = filters.contains(option);

              return FilterChip(
                selectedColor: AppConstants.textGreenColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                  side: BorderSide(
                    color: isSelected
                        ? Colors.transparent
                        : AppConstants.primaryColor,
                    width: 1.5,
                  ),
                ),
                labelStyle: lbRegular.copyWith(
                  fontSize: 18,
                ),
                checkmarkColor: AppConstants.primaryColor,
                label: Text(
                  option,
                  style: lbRegular,
                ),
                backgroundColor: Colors.white,
                selected: isSelected,
                onSelected: (bool selected) {
                  setState(
                    () {
                      if (selected) {
                        filters.add(option);
                      } else {
                        filters.remove(option);
                      }
                    },
                  );
                },
              );
            },
            separatorBuilder: (context, index) => const SizedBox(width: 10),
          ),
        ),
      ],
    );
  }

  Widget discussQueWidget() {
    return TextFormField(
      controller: discussController,
      style: lbRegular.copyWith(
        fontSize: 18,
      ),
      maxLines: null,
      decoration: InputDecoration(
        filled: true,
        fillColor: const Color.fromRGBO(255, 255, 255, 1),
        contentPadding: const EdgeInsets.all(10),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: const BorderSide(
            color: AppConstants.primaryColor,
            width: 1.5,
          ),
        ),
      ),
    );
  }

  Future selectDate(bool isAndroid) async {
    setState(() {
      meetingStartValidation = false;
    });
    String daySuffix(int day) {
      if (day >= 11 && day <= 13) {
        return 'th';
      }
      switch (day % 10) {
        case 1:
          return 'st';
        case 2:
          return 'nd';
        case 3:
          return 'rd';
        default:
          return 'th';
      }
    }

    if (Platform.isAndroid) {
      pickDate = await showDatePicker(
        context: context,
        // initialDate: DateTime.now(),
        firstDate: DateTime.now(),
        lastDate: DateTime.now().add(const Duration(days: 365)),
        barrierColor: AppConstants.textGreenColor,
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              colorScheme: const ColorScheme.light(
                primary: AppConstants.primaryColor,
                surface: AppConstants.backgroundColor,
                onSurface: AppConstants.primaryColor,
              ),
            ),
            child: child!,
          );
        },
      );

      if (pickDate != null) {
        _selectedDate = pickDate!;

        // Only reset time if user selects current date (today)
        bool isToday = _selectedDate.year == DateTime.now().year &&
            _selectedDate.month == DateTime.now().month &&
            _selectedDate.day == DateTime.now().day;

        if (isToday && pickedStartTime != null) {
          // Reset time only if today is selected
          pickedStartTime = null;
          startTimeController.text = "TBD";
        }

        // REGENERATE THE TIME OPTIONS
        _generateTimeOptions();

        String formattedDate = DateFormat('EEE MMM d').format(_selectedDate);
        String dayWithSuffix = daySuffix(_selectedDate.day);
        String yearAndTime = DateFormat('yyyy').format(_selectedDate);

        meetingDateController.text =
            '$formattedDate$dayWithSuffix, $yearAndTime';

        setState(() {});
      }
    } else {
      showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        builder: (context) {
          bool dateWasChanged = false;
          DateTime tempPickDate = DateTime.now();
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            height: MediaQuery.of(context).size.height * .50,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    IconButton(
                      onPressed: () => context.pop(),
                      icon: Image.asset(
                        AppConstants.closePopupImagePath,
                        height: 30,
                        width: 30,
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10.0),
                  child: Column(
                    children: [
                      SizedBox(
                        height: MediaQuery.of(context).size.height * .30,
                        child: CupertinoDatePicker(
                          mode: CupertinoDatePickerMode.date,
                          showDayOfWeek: true,
                          // use24hFormat:false,
                          minimumYear: DateTime.now().year,
                          minimumDate: DateTime.now(),
                          initialDateTime: DateTime.now(),
                          maximumYear: DateTime.now().year + 1,
                          onDateTimeChanged: (value) {
                            dateWasChanged = true;
                            tempPickDate = value;

                            String formattedDate =
                                DateFormat('EEE MMM d').format(value);
                            String dayWithSuffix = daySuffix(value.day);
                            String yearAndTime =
                                DateFormat('yyyy').format(value);
                            String timeZone = DateFormat('z').format(value);
                            iosDatePick =
                                '$formattedDate$dayWithSuffix, $yearAndTime $timeZone';
                          },
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () async {
                          if (dateWasChanged) {
                            pickDate = tempPickDate;
                            _selectedDate = tempPickDate;

                            bool isToday = _selectedDate.year ==
                                    DateTime.now().year &&
                                _selectedDate.month == DateTime.now().month &&
                                _selectedDate.day == DateTime.now().day;

                            if (isToday && pickedStartTime != null) {
                              pickedStartTime = null;
                              startTimeController.text = "TBD";
                            }

                            meetingDateController.text = iosDatePick;
                          } else {
                            final currentDateTime = DateTime.now();
                            pickDate = currentDateTime;
                            _selectedDate = currentDateTime;

                            if (pickedStartTime != null) {
                              pickedStartTime = null;
                              startTimeController.text = "TBD";
                            }

                            String formattedDate =
                                DateFormat('EEE MMM d').format(currentDateTime);
                            String dayWithSuffix =
                                daySuffix(currentDateTime.day);
                            String yearAndTime =
                                DateFormat('yyyy').format(currentDateTime);
                            String timeZone =
                                DateFormat('z').format(currentDateTime);
                            meetingDateController.text =
                                '$formattedDate$dayWithSuffix, $yearAndTime $timeZone';
                          }

                          _generateTimeOptions();

                          formatedTime = DateTime(
                            pickDate?.year ?? 0,
                            pickDate?.month ?? 0,
                            pickDate?.day ?? 0,
                            pickedStartTime?.hour ?? 0,
                            pickedStartTime?.minute ?? 0,
                          );

                          selectedOptions = await getAlertOptions(
                              formatedTime ?? DateTime.now());
                          setState(() {});
                          if (context.mounted) {
                            context.pop();
                          }
                        },
                        child: Container(
                          height: 45,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Save",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                      NetworkAwareTap(
                        onTap: () {
                          meetingDateController.text = "TBD";
                          pickDate = null;
                          _generateTimeOptions();
                          context.pop();
                        },
                        child: Container(
                          height: 45,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: Colors.transparent,
                            border: Border.all(
                              color: AppConstants.primaryColor,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              "Cancel",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      );
    }
  }

  Future<void> timeFromPicker(
    bool isStartTime,
    BuildContext context,
    TextEditingController startTimeController,
    TextEditingController endTimeController,
  ) async {
    if (Platform.isAndroid) {
      // Android implementation using showTimePicker
      TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );

      if (pickedTime != null) {
        final formattedTime = DateFormat('h:mm a').format(
          DateTime(
            DateTime.now().year,
            DateTime.now().month,
            DateTime.now().day,
            pickedTime.hour,
            pickedTime.minute,
          ),
        );
        // print("Time : ${startTimeController.text}");

        if (isStartTime) {
          startTimeController.text = formattedTime;

          pickedStartTime = pickedTime;
        } else {
          endTimeController.text = formattedTime;
        }
      } else {
        // User cancelled, set "TBD" or null as needed
        if (isStartTime) {
          startTimeController.text = "TBD";
          endTimeController.text = "TBD";
          pickedStartTime = null;
        } else {
          endTimeController.text = "TBD";
        }
      }
      setState(() {});
    } else {
      // iOS implementation using CupertinoDatePicker
      showModalBottomSheet(
        context: context,
        builder: (BuildContext builder) {
          return SizedBox(
            height: MediaQuery.of(context).size.height / 3,
            child: CupertinoDatePicker(
              mode: CupertinoDatePickerMode.time,
              use24hFormat: false,
              onDateTimeChanged: (value) {
                final time = TimeOfDay.fromDateTime(value);
                final formattedTime = DateFormat('h:mm a').format(value);

                if (isStartTime) {
                  tempStartTimeController.text = formattedTime;
                  pickedStartTime = time;
                } else {
                  tempEndTimeController.text = formattedTime;
                }
                setState(() {});
              },
            ),
          );
        },
      ).whenComplete(() {
        // Handle case where user cancels the modal without picking
        if (isStartTime && startTimeController.text.isEmpty) {
          startTimeController.text = "TBD";
          pickedStartTime = null;
        } else if (!isStartTime && endTimeController.text.isEmpty) {
          endTimeController.text = "TBD";
          // pickedEndTime = null;
        }
        setState(() {});
      });
    }
  }

  void onSave() {
    bool validation = _formKey.currentState!.validate();

    if (bookClubModel?.bookClubType == ClubType.standing.value) {
      if (validation && bookController.text.isEmpty) {
        setState(() {
          bookCaseController?.updateTypeAheadFlag(true);
        });
      } else {
        scheduleMeeting();
      }
    } else {
      scheduleMeeting();
    }

    // if (validation && bookController.text.isEmpty) {
    //   setState(() {
    //     bookCaseController?.updateTypeAheadFlag(true);
    //   });
    // } else {
    //   scheduleMeeting();
    // }
  }

  void durationValidateFunction() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Column(
                    children: [
                      Text(
                        "Add meeting:",
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(
                          fontSize: 18,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                      const SizedBox(height: 25),
                      SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Center(
                          child: Text(
                            "Date, time and duration should either be marked as 'TBD' or have specific values assigned",
                            textAlign: TextAlign.center,
                            style: lbRegular.copyWith(
                              fontSize: 18,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3.5,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Ok",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  List<String> _timeOptions = [];

  // String? _selectedTime;
  DateTime _selectedDate = DateTime.now();

  /// Generate time options in 15-minute intervals based on the selected date
  void _generateTimeOptions() {
    _timeOptions.clear();
    _timeOptions = ['TBD'];
    const int interval = 15;
    const startTime = TimeOfDay(hour: 0, minute: 0);
    const endTime = TimeOfDay(hour: 23, minute: 45);
    final currentTime = pickedStartTime ?? TimeOfDay.now();

    var loopTime = startTime;
    while (loopTime.hour < endTime.hour ||
        (loopTime.hour == endTime.hour && loopTime.minute <= endTime.minute)) {
      if (!_isToday() || _isFutureTime(loopTime, currentTime)) {
        _timeOptions.add(_formatTimeOfDay(loopTime));
      }
      loopTime = _incrementTimeByMinutes(loopTime, interval);
    }
    setState(() {});
  }

  TimeOfDay _parseTimeOfDay(String time) {
    try {
      final format = DateFormat.jm(); // 12-hour format with AM/PM
      final dateTime = format.parseLoose(time);
      return TimeOfDay.fromDateTime(dateTime);
    } catch (e) {
      print("Error parsing time: $e");
      return const TimeOfDay(hour: 0, minute: 0); // or handle it as needed
    }
  }

// Check if the selected date is today
  bool _isToday() {
    final today = DateTime.now();
    return pickDate?.year == today.year &&
        pickDate?.month == today.month &&
        pickDate?.day == today.day;
  }

// Check if the loop time is a future time compared to the current time
  bool _isFutureTime(TimeOfDay loopTime, TimeOfDay currentTime) {
    return (loopTime.hour > currentTime.hour) ||
        (loopTime.hour == currentTime.hour &&
            loopTime.minute > currentTime.minute);
  }

// Format TimeOfDay to a readable string
  String _formatTimeOfDay(TimeOfDay time) {
    final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }

// Increment TimeOfDay by a given number of minutes
  TimeOfDay _incrementTimeByMinutes(TimeOfDay time, int minutes) {
    final totalMinutes = time.hour * 60 + time.minute + minutes;
    return TimeOfDay(hour: totalMinutes ~/ 60, minute: totalMinutes % 60);
  }

  List<Map<String, String>> generateTimeIntervalsMap() {
    List<Map<String, String>> timeIntervals = [
      {'TBD': 'TBD'}
    ]; // Start with "TBD" as the first item
    const int totalMinutes = 4 * 60; // 4 hours in minutes
    const int interval = 30; // 15 minutes interval

    for (int minutes = interval; minutes <= totalMinutes; minutes += interval) {
      int hours = minutes ~/ 60;
      int remainingMinutes = minutes % 60;

      // Format the key as "HH:mm"
      String key =
          '${hours.toString().padLeft(2, '0')}:${remainingMinutes.toString().padLeft(2, '0')}';

      // Format the display value as a human-readable time interval
      String value = '';
      if (hours > 0) {
        value += '$hours hour';
        if (hours > 1) value += 's';
      }
      if (remainingMinutes > 0) {
        if (hours > 0) value += ' ';
        value += '$remainingMinutes min';
      }

      timeIntervals.add({key: value});
    }

    return timeIntervals;
  }
}
