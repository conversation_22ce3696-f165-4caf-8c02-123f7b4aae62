import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/models/club_charter_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../app/core/constants.dart';
import '../../../reusableWidgets/previous_screen_appbar.dart';

class ImpromptuClubScreen extends StatefulWidget {
  final String? option;
  const ImpromptuClubScreen({
    super.key,
    this.option,
  });

  @override
  State<ImpromptuClubScreen> createState() => _ImpromptuClubScreenState();
}

class _ImpromptuClubScreenState extends State<ImpromptuClubScreen> {
  List<ClubCharterModel> clubCharterList = [
    ClubCharterModel(
      rules: 'How many sessions the book will be discussed over?',
    ),
    ClubCharterModel(
      rules: 'Membership cap: Eg 7 members only.',
    ),
  ];

  List<ClubCharterModel> memberRquestList = [
    ClubCharterModel(
      rules: 'Have they read the book?',
    ),
    ClubCharterModel(
      rules: 'When do they anticipate finishing the book?',
    ),
  ];

  List<ClubCharterModel> clubCreateList = [
    ClubCharterModel(
      rules: 'Searching for people to invite to fill your 9 club openings. ',
    ),
    ClubCharterModel(
      rules: 'Changing the number of club openings.',
    ),
  ];

  bool isNameAvailable = false;
  TextEditingController clubNameController = TextEditingController();
  TextEditingController clubCharterController = TextEditingController();
  TextEditingController clubMemberController = TextEditingController();
  void checkHandler(String handler) {
    setState(() {
      isNameAvailable = handler == clubNameController.text;
    });
  }

  Widget checkClubName() {
    return Column(
      children: isNameAvailable
          ? [
              checkMessage(
                  'There are 3 impromptu clubs for this book.\nFinal impromptu club # will be assigned on creation.',
                  isNameAvailable,
                  clubNameController.text.isNotEmpty)
            ]
          : [
              // const SizedBox(
              //   height: 10,
              // ),
              checkMessage('Name not available', isNameAvailable,
                  clubNameController.text.isNotEmpty)
            ],
    );
  }

  Widget checkMessage(String message, bool isValid, bool shouldShow) {
    if (!shouldShow) {
      return const SizedBox.shrink();
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon(
          //   isValid ? Icons.check_circle : Icons.error,
          //   color: isValid ? AppConstants.primaryColor : Colors.red,
          // ),
          // const SizedBox(width: 8.0),
          Expanded(
            child: Text(message, style: lbItalic),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: Color.fromRGBO(37, 57, 67, 1),
              ),
            ),
          ),
          child: PreviousScreenAppBar(
            bookName: widget.option,
            isSetProfile: true,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                "assets/images/PaperBackground.png",
              ),
              filterQuality: FilterQuality.high,
              fit: BoxFit.fitWidth,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Text(
                  'Book',
                  style: lbRegular.copyWith(
                    fontSize: 18,
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: TextField(
                  controller: clubNameController,
                  onChanged: checkHandler,
                  maxLines: null,
                  decoration: InputDecoration(
                    suffixIcon: const Icon(
                      Icons.search_rounded,
                      color: AppConstants.primaryColor,
                      size: 30,
                    ),
                    filled: true,
                    fillColor: const Color.fromRGBO(255, 255, 255, 1),
                    contentPadding: const EdgeInsets.all(10),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5),
                      borderSide: const BorderSide(
                        color: AppConstants.primaryColor,
                        width: 1.5,
                      ),
                    ),
                  ),
                ),
              ),
              clubNameController.text.isNotEmpty
                  ? const SizedBox(
                      height: 10,
                    )
                  : const SizedBox.shrink(),
              clubNameController.text.isNotEmpty
                  ? checkClubName()
                  : const SizedBox(),
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Row(
                  children: [
                    Text(
                      'Impromptu Club Charter',
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    NetworkAwareTap(
                      onTap: () {
                        impromptuCharterFunction();
                      },
                      child: Image.asset(
                        'assets/icons/information.png',
                        filterQuality: FilterQuality.high,
                        fit: BoxFit.cover,
                        height: 22,
                        width: 22,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: TextField(
                  controller: clubCharterController,
                  // onChanged: checkHandler,
                  maxLines: null,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: const Color.fromRGBO(255, 255, 255, 1),
                    contentPadding: const EdgeInsets.all(10),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5),
                      borderSide: const BorderSide(
                        color: AppConstants.primaryColor,
                        width: 1.5,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Row(
                  children: [
                    Text(
                      'Member request prompt',
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    NetworkAwareTap(
                      onTap: () {
                        memberRequestPromptFunction();
                      },
                      child: Image.asset(
                        'assets/icons/information.png',
                        filterQuality: FilterQuality.high,
                        fit: BoxFit.cover,
                        height: 22,
                        width: 22,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '(Optional)',
                      style: lbItalic.copyWith(
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: TextField(
                  controller: clubMemberController,
                  // onChanged: checkHandler,
                  maxLines: null,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: const Color.fromRGBO(255, 255, 255, 1),
                    contentPadding: const EdgeInsets.all(10),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5),
                      borderSide: const BorderSide(
                        color: AppConstants.primaryColor,
                        width: 1.5,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        context.pop();
                        clubCreationFunction();
                        // notSentInvitationFunction();
                        // blockFunction();
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 2.5,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor),
                        child: Center(
                          child: Text(
                            "Create club",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                    NetworkAwareTap(
                      onTap: () {
                        context.pop();
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 2.5,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(49),
                          color: Colors.transparent,
                          border: Border.all(color: AppConstants.primaryColor),
                        ),
                        child: Center(
                          child: Text(
                            "Cancel",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 25,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void impromptuCharterFunction() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30.0),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Center(
                      child: Text(
                        "Club Charter",
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        style: lbRegular.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 3,
                ),
                Center(
                  child: Text(
                    "(You can edit this later)",
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 23,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Text(
                    "Impromptu club charter includes:",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Column(
                  children: clubCharterList.map((e) {
                    return Padding(
                      padding: const EdgeInsets.only(left: 35, right: 20),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 3.0),
                            child: Align(
                              alignment: Alignment.topCenter,
                              child: Text(
                                "•",
                                textAlign: TextAlign.start,
                                style: lbRegular.copyWith(
                                  fontSize: 15,
                                  height: 0.8,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 5),
                          Expanded(
                            child: Text(
                              textAlign: TextAlign.start,
                              e.rules ?? '',
                              style: lbRegular.copyWith(
                                fontSize: 12,
                                height: 1.5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(
                  height: 25,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        context.pop();
                        // reportFunction();
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 3.5,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor),
                        child: Center(
                          child: Text(
                            "Ok",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void memberRequestPromptFunction() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30.0),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Center(
                      child: Text(
                        "Member Request Prompt",
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        style: lbRegular.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 3,
                ),
                Center(
                  child: Text(
                    "(You can edit this later)",
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 23,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Text(
                    "What do you want to know about people asking to join your club?",
                    textAlign: TextAlign.start,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Column(
                  children: memberRquestList.map((e) {
                    return Padding(
                      padding: const EdgeInsets.only(left: 35, right: 20),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 3.0),
                            child: Align(
                              alignment: Alignment.topCenter,
                              child: Text(
                                "•",
                                textAlign: TextAlign.start,
                                style: lbRegular.copyWith(
                                  fontSize: 15,
                                  height: 0.8,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 5),
                          Expanded(
                            child: Text(
                              textAlign: TextAlign.start,
                              e.rules ?? '',
                              style: lbRegular.copyWith(
                                fontSize: 12,
                                height: 1.5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(
                  height: 25,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        context.pop();
                        // reportFunction();
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 3.5,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor),
                        child: Center(
                          child: Text(
                            "Ok",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void clubCreationFunction() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30.0),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Center(
                      child: Text(
                        "${clubNameController.text} Created",
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        style: lbRegular.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Text(
                    "As Admin Leader consider:",
                    textAlign: TextAlign.start,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Column(
                  children: clubCreateList.map((e) {
                    return Padding(
                      padding: const EdgeInsets.only(left: 35, right: 20),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 3.0),
                            child: Align(
                              alignment: Alignment.topCenter,
                              child: Text(
                                "•",
                                textAlign: TextAlign.start,
                                style: lbRegular.copyWith(
                                  fontSize: 15,
                                  height: 0.8,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 5),
                          Expanded(
                            child: Text(
                              textAlign: TextAlign.start,
                              e.rules ?? '',
                              style: lbRegular.copyWith(
                                fontSize: 12,
                                height: 1.5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(
                  height: 25,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        context.pop();
                        // reportFunction();
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 3.5,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor),
                        child: Center(
                          child: Text(
                            "Ok",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }
}
