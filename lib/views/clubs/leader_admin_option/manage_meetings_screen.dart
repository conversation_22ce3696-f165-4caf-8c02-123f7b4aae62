import 'dart:developer';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/helpers/datetime_helper.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/home_model/home_screen2_model/meeting_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../controller/book_club_controller.dart';
import '../../../reusableWidgets/previous_screen_appbar.dart';
import '../user_club_details/providers/user_club_details_provider.dart';

class ManageMeetingScreen extends StatefulWidget {
  final String? buttonName;

  const ManageMeetingScreen({
    super.key,
    this.buttonName,
  });

  @override
  State<ManageMeetingScreen> createState() => _ManageMeetingScreenState();
}

class _ManageMeetingScreenState extends State<ManageMeetingScreen> {
  BookClubModel? bookClubModel;
  bool isLoading = false;
  final ScrollController _upcomingMeetingScrollController = ScrollController();
  final ScrollController _previousMeetingScrollController = ScrollController();
  late final UserClubDetailsProvider provider;

  @override
  void initState() {
    updateClubData();
    provider = Provider.of<UserClubDetailsProvider>(context, listen: false);

    provider.initialize(context);
    WidgetsBinding.instance.addPostFrameCallback((_) => loadData());

    _upcomingMeetingScrollController.addListener(_upComingOnScroll);
    _previousMeetingScrollController.addListener(_previousMOnScroll);
    super.initState();
  }

  // UPCOMING MEETING SCROLL
  void _upComingOnScroll() {
    if (_upcomingMeetingScrollController.position.pixels >=
            _upcomingMeetingScrollController.position.maxScrollExtent &&
        !provider.isUpcomingMeetingsLoading &&
        (provider.upcomingMeetings?.length ?? 0) <
            (provider.upcomingMeetingCount)) {
      provider.getUpcomingMeetings(
          bookClubModel?.bookClubId ?? 0, true, context);
      // getUpcomingMeetings(true);
    }
  }

  // PREVIOUS MEETING SCROLL
  void _previousMOnScroll() {
    if (_previousMeetingScrollController.position.pixels >=
            _previousMeetingScrollController.position.maxScrollExtent &&
        !provider.isPreviousMeetingsLoading &&
        (provider.previousMeetings?.length ?? 0) <
            (provider.previousMeetingCount)) {
      provider.getPreviousMeetings(
          bookClubModel?.bookClubId ?? 0, true, context);

      // getPreviousMeetings(true);
    }
  }

  Future updateClubData() async {
    bookClubModel =
        Provider.of<BookClubController>(context, listen: false).bookClubModel;
  }

  Future loadData() async {
    log("In load Data");
    // await Future.wait([
    setState(() {
      isLoading = true;
    });
    await provider.getUpcomingMeetings(
        bookClubModel?.bookClubId ?? 0, false, context);
    if (mounted) {
      await provider.getPreviousMeetings(
          bookClubModel?.bookClubId ?? 0, false, context);
      setState(() {
        isLoading = false;
      });
      // ]);
    }
  }

  @override
  void dispose() {
    _previousMeetingScrollController.dispose();
    _upcomingMeetingScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: PreviousScreenAppBar(
            bookName: widget.buttonName,
            clubName: bookClubModel?.bookClubName,
            clubNameFlag: true,
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              "assets/images/PaperBackground.png",
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          containersColor: AppConstants.skeletonBackgroundColor,
          enabled: isLoading,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      NetworkAwareTap(
                        onTap: () {
                          context.pushNamed(
                            'AddMeeting',
                            extra: {
                              'addMeeting': 'Add New Meeting',
                              'boolean': false,
                            },
                          );
                        },
                        child: Text(
                          'Add new meeting',
                          style: lbItalic.copyWith(
                            fontSize: 16,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                provider.upcomingMeetings?.isNotEmpty ?? false
                    ? const SizedBox(
                        height: 25,
                      )
                    : const SizedBox.shrink(),
                provider.upcomingMeetings?.isNotEmpty ?? false
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Text(
                          'Upcoming Meetings',
                          style: lbRegular.copyWith(
                            fontSize: 20,
                          ),
                        ),
                      )
                    : const SizedBox.shrink(),
                provider.upcomingMeetings?.isNotEmpty ?? false
                    ? const SizedBox(
                        height: 10,
                      )
                    : const SizedBox.shrink(),
                provider.upcomingMeetings?.isNotEmpty ?? false
                    ? SizedBox(
                        height: 222,
                        child: Consumer<UserClubDetailsProvider>(
                          builder: (context, provider, child) {
                            return ListView.builder(
                              controller: _upcomingMeetingScrollController,
                              scrollDirection: Axis.horizontal,
                              padding:
                                  const EdgeInsets.only(left: 10, right: 20),
                              itemCount: provider.isUpcomingMeetingsLoading
                                  ? (provider.upcomingMeetings?.length ?? 0) + 1
                                  : provider.upcomingMeetings?.length,
                              itemBuilder: (context, index) {
                                if (index ==
                                        provider.upcomingMeetings?.length &&
                                    provider.isUpcomingMeetingsLoading) {
                                  return const Padding(
                                    padding: EdgeInsets.only(left: 10.0),
                                    child: Center(
                                      child: CircularProgressIndicator(
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                  );
                                }

                                var list = provider.upcomingMeetings?[index];
                                final meetingTime =
                                    DateTimeHelper.getMeetingScheduleTime(
                                        list?.meetingStartTime ?? 0,
                                        list?.meetingEndTime ?? 0);
                                if (list?.meetingStartTime != null) {}

                                final date =
                                    DateTimeHelper.getDayMonthYearDateFormat(
                                        list?.meetingDate);

                                return Skeleton.replace(
                                  replacement: upcomingMeetingSkeleton(
                                    false,
                                    list,
                                    meetingTime,
                                    date,
                                    // meetingStartTime,
                                  ),
                                  child: upcomingMeetingSkeleton(
                                    true,
                                    list,
                                    meetingTime,
                                    date,
                                    // meetingStartTime,
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      )
                    : Padding(
                        padding: const EdgeInsets.only(top: 25.0),
                        child: Skeleton.replace(
                          replacement: Container(
                            margin: const EdgeInsets.symmetric(horizontal: 20),
                            padding: const EdgeInsets.only(left: 15),
                            height: 45,
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                              color: AppConstants.skeletonBackgroundColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                "No upcoming meetings",
                                textAlign: TextAlign.start,
                              ),
                            ),
                          ),
                          child: const NoDataWidget(
                            message: 'No upcoming meetings',
                          ),
                        ),
                      ),
                const SizedBox(
                  height: 25,
                ),
                const Divider(
                  thickness: 1.5,
                  color: AppConstants.primaryColor,
                ),
                provider.previousMeetings?.isNotEmpty ?? false
                    ? const SizedBox(
                        height: 25,
                      )
                    : const SizedBox.shrink(),
                provider.previousMeetings?.isNotEmpty ?? false
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Text(
                          'Previous Meetings',
                          style: lbRegular.copyWith(
                            fontSize: 20,
                          ),
                        ),
                      )
                    : const SizedBox.shrink(),
                provider.previousMeetings?.isNotEmpty ?? false
                    ? const SizedBox(
                        height: 10,
                      )
                    : const SizedBox.shrink(),
                provider.previousMeetings?.isNotEmpty ?? false
                    ? SizedBox(
                        height: 186,
                        child: Consumer<UserClubDetailsProvider>(
                          builder: (context, provider, child) {
                            return ListView.builder(
                              controller: _previousMeetingScrollController,
                              scrollDirection: Axis.horizontal,
                              padding:
                                  const EdgeInsets.only(left: 10, right: 20),
                              itemCount: provider.isPreviousMeetingsLoading
                                  ? (provider.previousMeetings?.length ?? 0) + 1
                                  : provider.previousMeetings?.length,
                              itemBuilder: (context, index) {
                                if (index ==
                                        provider.previousMeetings?.length &&
                                    provider.isPreviousMeetingsLoading) {
                                  return const Padding(
                                    padding: EdgeInsets.only(left: 10.0),
                                    child: Center(
                                      child: CircularProgressIndicator(
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                  );
                                }
                                var list = provider.previousMeetings?[index];
                                final date =
                                    DateTimeHelper.getDayMonthYearDateFormat(
                                        list?.meetingDate);
                                final meetingTime =
                                    DateTimeHelper.getMeetingScheduleTime(
                                        list?.meetingStartTime ?? 0,
                                        list?.meetingEndTime ?? 0);
                                return Skeleton.replace(
                                  replacement: previousMeetingSkeleton(
                                    false,
                                    list,
                                    date,
                                    meetingTime,
                                  ),
                                  child: previousMeetingSkeleton(
                                    true,
                                    list,
                                    date,
                                    meetingTime,
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      )
                    : Padding(
                        padding: const EdgeInsets.only(top: 25.0),
                        child: Skeleton.replace(
                          replacement: Container(
                            margin: const EdgeInsets.symmetric(horizontal: 20),
                            padding: const EdgeInsets.only(left: 15),
                            height: 45,
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                              color: AppConstants.skeletonBackgroundColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                "No previous meetings",
                                textAlign: TextAlign.start,
                              ),
                            ),
                          ),
                          child: const NoDataWidget(
                            message: 'No previous meetings',
                          ),
                        ),
                      ),
                const SizedBox(
                  height: 25,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void deleteBox(int? meetingId) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Delete Meeting",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Center(
                      child: Text(
                        "Are you sure you would like to delete this meeting?",
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Consumer<UserClubDetailsProvider>(
                      builder: (context, userDetailsProvider, _) {
                    return Row(
                      mainAxisAlignment:
                          userDetailsProvider.isDeleteMeetingLoading
                              ? MainAxisAlignment.center
                              : MainAxisAlignment.spaceBetween,
                      children: [
                        CustomLoaderButton(
                          buttonWidth:
                              userDetailsProvider.isDeleteMeetingLoading
                                  ? 45.0
                                  : MediaQuery.sizeOf(context).width / 3,
                          buttonRadius: 30.0,
                          buttonChild:
                              userDetailsProvider.isDeleteMeetingLoading
                                  ? const CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.white,
                                    )
                                  : Text(
                                      "Delete",
                                      textAlign: TextAlign.center,
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                          buttonPressed: () async {
                            final isDeleted = await provider.deleteMeeting(
                                meetingId ?? 0, context);
                            if (isDeleted) {
                              context.pop();
                              await loadData();
                              confirmDeleteBox();
                            }
                          },
                        ),
                        userDetailsProvider.isDeleteMeetingLoading
                            ? const SizedBox.shrink()
                            : NetworkAwareTap(
                                onTap: () => context.pop(),
                                child: Container(
                                  height: 45,
                                  width: MediaQuery.of(context).size.width / 3,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(49),
                                    color: AppConstants.backgroundColor,
                                    border: Border.all(
                                      color: AppConstants.primaryColor,
                                      width: 1,
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      "Cancel",
                                      textAlign: TextAlign.center,
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                      ],
                    );
                  }),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void confirmDeleteBox() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Delete Meeting",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Center(
                      child: Text(
                        "The meeting has been deleted",
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                    // upcomingMeetings.removeAt(index);
                    // setState(() {});
                    // context.goNamed('ClubsScreen3');
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  Widget previousMeetingSkeleton(
      bool flag, MeetingList? list, String date, String meetingTime) {
    return Container(
      width: 250,
      margin: const EdgeInsets.only(left: 10),
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: flag ? Colors.transparent : AppConstants.skeletonBackgroundColor,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MarqueeList(
            children: [
              Text(
                list?.bookName ?? '',
                overflow: TextOverflow.ellipsis,
                style: lbBold.copyWith(
                  fontSize: 18,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          MarqueeList(
            children: [
              Text(
                list?.bookAuthor ?? '',
                overflow: TextOverflow.ellipsis,
                style: lbRegular.copyWith(
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
            list?.partOfBookCovered ?? '',
            overflow: TextOverflow.ellipsis,
            style: lbRegular.copyWith(
              fontSize: 14,
            ),
          ),
          const SizedBox(
            height: 18,
          ),
          Text(
            date,
            overflow: TextOverflow.ellipsis,
            style: lbItalic.copyWith(
              fontSize: 12,
            ),
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
            meetingTime,
            overflow: TextOverflow.ellipsis,
            style: lbItalic.copyWith(
              fontSize: 12,
            ),
          ),
          const SizedBox(
            height: 5,
          ),
          (list?.discussionQuestions?.isNotEmpty ?? false)
              ? GestureDetector(
                  onTap: () {
                    context.pushNamed(
                      'discussionScreen',
                      extra: {
                        'clubName': bookClubModel?.bookClubName ?? '',
                        'discussionQue': list?.discussionQuestions,
                        // 'userName': userName,
                      },
                    );
                  },
                  child: Text(
                    'Discussion Questions',
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    style: lbItalic.copyWith(
                      decoration: TextDecoration.underline,
                      fontSize: 12,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                )
              : Text(
                  'TBD',
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
        ],
      ),
    );
  }

  Widget upcomingMeetingSkeleton(
    bool flag,
    MeetingList? list,
    String meetingTime,
    String date,
  ) {
    log("Meeting Date : $date");
    return Container(
      width: 320,
      margin: const EdgeInsets.only(left: 10),
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: flag ? Colors.transparent : AppConstants.skeletonBackgroundColor,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MarqueeList(
            children: [
              Text(
                list?.bookName ?? '',
                overflow: TextOverflow.ellipsis,
                style: lbBold.copyWith(
                  fontSize: 18,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          MarqueeList(
            children: [
              Text(
                list?.bookAuthor ?? '',
                overflow: TextOverflow.ellipsis,
                style: lbRegular.copyWith(
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
            list?.partOfBookCovered ?? '',
            overflow: TextOverflow.ellipsis,
            style: lbRegular.copyWith(
              fontSize: 14,
            ),
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
            list?.meetingDate != null ? date : 'TBD',
            overflow: TextOverflow.ellipsis,
            style: lbItalic.copyWith(
              fontSize: 12,
            ),
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
            meetingTime,
            overflow: TextOverflow.ellipsis,
            style: lbItalic.copyWith(
              fontSize: 12,
            ),
          ),
          const SizedBox(
            height: 15,
          ),
          list?.discussionQuestions?.isNotEmpty ?? false
              ? GestureDetector(
                  onTap: () {
                    context.pushNamed(
                      'discussionScreen',
                      extra: {
                        'clubName': bookClubModel?.bookClubName ?? '',
                        'discussionQue': list?.discussionQuestions,
                      },
                    );
                  },
                  child: Text(
                    'Discussion Questions',
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    style: lbItalic.copyWith(
                      decoration: TextDecoration.underline,
                      fontSize: 12,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                )
              : Text(
                  'TBD',
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
          const SizedBox(
            height: 19,
          ),
          Row(
            children: [
              meetingTime == 'TBD' ||
                      DateTime.now().isBefore(
                        DateTime.fromMillisecondsSinceEpoch(
                          list?.meetingStartTime ?? 0,
                        ).toLocal(),
                      )
                  ? NetworkAwareTap(
                      onTap: () {
                        context.pushNamed(
                          'AddMeeting',
                          extra: {
                            'addMeeting': 'Edit Meeting',
                            'editData': list,
                            'boolean': true,
                            'editMeetingId': list?.meetingId,
                          },
                        );
                      },
                      child: Text(
                        'Edit',
                        overflow: TextOverflow.ellipsis,
                        style: lbItalic.copyWith(
                          decoration: TextDecoration.underline,
                          fontSize: 14,
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
              const Spacer(),
              NetworkAwareTap(
                onTap: () {
                  deleteBox(list?.meetingId);
                },
                child: Text(
                  'Delete',
                  overflow: TextOverflow.ellipsis,
                  style: lbItalic.copyWith(
                    fontSize: 14,
                    decoration: TextDecoration.underline,
                    decorationColor: AppConstants.redColor,
                    color: AppConstants.redColor,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
