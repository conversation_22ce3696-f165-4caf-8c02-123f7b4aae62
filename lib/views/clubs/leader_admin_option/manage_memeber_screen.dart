import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/club_membership_model.dart';
import 'package:eljunto/reusableWidgets/cached_network_image.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:eljunto/reusableWidgets/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../app/core/constants.dart';
import '../../../app/core/utils/text_style.dart';
import '../../../reusableWidgets/previous_screen_appbar.dart';
import '../user_club_details/providers/user_club_details_provider.dart';

class ManageMemberScreen extends StatefulWidget {
  final String? buttonName;

  const ManageMemberScreen({
    super.key,
    this.buttonName,
  });

  @override
  State<ManageMemberScreen> createState() => _ManageMemberScreenState();
}

class _ManageMemberScreenState extends State<ManageMemberScreen> {
  late final UserClubDetailsProvider _provider;
  late final BookClubModel? _bookClubModel;
  late final int _bookClubId;

  final TextEditingController _roleController = TextEditingController();
  final String _imageUrlBase = ApiConstants.imageBaseUrl;

  @override
  void initState() {
    super.initState();
    _provider = Provider.of<UserClubDetailsProvider>(context, listen: false);
    _bookClubModel =
        Provider.of<BookClubController>(context, listen: false).bookClubModel;
    _bookClubId = _bookClubModel?.bookClubId ?? 0;

    _roleController.text = 'Select any one';

    // Fetch data after the first frame is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchMembers();
    });
  }

  Future<void> _fetchMembers() async {
    if (_bookClubId != 0) {
      // The provider will handle the loading state
      await _provider.getBookClubMembers(_bookClubId, context);
    }
  }

  @override
  void dispose() {
    _roleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Use a Consumer at a higher level to manage the UI state
    return Consumer<UserClubDetailsProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          appBar: _buildAppBar(),
          body: Container(
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(AppConstants.bgImagePath),
                filterQuality: FilterQuality.high,
                fit: BoxFit.fitWidth,
              ),
            ),
            child: Skeletonizer(
              enabled: provider.isLoading,
              effect: const SoldColorEffect(
                color: AppConstants.skeletonforgroundColor,
                lowerBound: 0.1,
                upperBound: 0.5,
              ),
              child: Padding(
                padding: const EdgeInsets.only(left: 20.0, right: 20, top: 25),
                child: Column(
                  children: [
                    _buildHeader(provider),
                    Expanded(
                      child: _buildMemberList(provider),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  PreferredSize _buildAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(80),
      child: Container(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(
              width: 1.5,
              color: AppConstants.primaryColor,
            ),
          ),
        ),
        child: PreviousScreenAppBar(
          bookName: widget.buttonName,
          clubName: _bookClubModel?.bookClubName,
          clubNameFlag: true,
          isSetProfile: true,
        ),
      ),
    );
  }

  Widget _buildHeader(UserClubDetailsProvider provider) {
    // Use a placeholder when loading
    final vacancies = provider.isLoading ? 0 : provider.totalVacancies;
    final totalPositions =
        provider.isLoading ? 0 : provider.memberList?.first.totalPositions ?? 0;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          "No of openings ($vacancies)",
          style: lbItalic.copyWith(fontSize: 16),
        ),
        if ((provider.memberList?.length ?? 0) + vacancies < totalPositions)
          NetworkAwareTap(
            onTap: () async {
              await provider.addRemoveOpening(true, _bookClubId, context);
              if (!provider.isSuccess) {
                _showErrorMessage(provider.apiResponse);
              }
            },
            child: Text(
              "Add opening",
              style: lbItalic.copyWith(
                fontSize: 16,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildMemberList(UserClubDetailsProvider provider) {
    // Show a few skeleton items while loading
    if (provider.isLoading) {
      return ListView.builder(
        itemCount: 5,
        itemBuilder: (context, index) => _buildMemberListItem(null, index),
      );
    }

    if (provider.memberList == null || provider.memberList!.isEmpty) {
      return const Center(child: Text("No members found."));
    }

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 25),
      itemCount: provider.totalListItems,
      itemBuilder: (context, index) {
        final membersCount = provider.memberList!.length;
        if (index < membersCount) {
          // It's a member
          final member = provider.memberList![index];
          return _buildMemberListItem(member, index);
        } else {
          // It's a "Club Opening"
          return _buildClubOpeningItem();
        }
      },
    );
  }

  // Helper method to build a single member list item
  Widget _buildMemberListItem(ClubMembershipModel? member, int index) {
    final provider = context.read<UserClubDetailsProvider>();
    final isLeader = (index == 0);
    final imageUrl = _imageUrlBase + (member?.userProfilePicture ?? '');
    final name = member?.userName ?? 'Loading...';

    void handleTap() {
      if (isLeader) {
        if (provider.memberList!.length > 1) {
          _assignLeaderBox();
        } else {
          _showSingleLeaderMessage();
        }
      } else {
        _removeMemberBox(name, index);
      }
    }

    return Container(
      margin: const EdgeInsets.only(top: 25),
      height: 84,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppConstants.primaryColor, width: 1.5),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  name,
                  style: lbBold.copyWith(
                      fontSize: 18, color: AppConstants.primaryColor),
                ),
                const SizedBox(height: 10),
                NetworkAwareTap(
                  onTap: handleTap,
                  child: Text(
                    isLeader ? 'Assign New Leader' : 'Remove',
                    style: lbItalic.copyWith(
                      fontSize: 12,
                      decoration: TextDecoration.underline,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
            const Spacer(),
            CustomCachedNetworkImage(
              imageUrl: imageUrl,
              width: 45,
              height: 45,
              errorImage: AppConstants.profileLogoImagePath,
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to build a "Club Opening" item
  Widget _buildClubOpeningItem() {
    final provider = context.read<UserClubDetailsProvider>();
    return Container(
      margin: const EdgeInsets.only(top: 25),
      height: 84,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppConstants.primaryColor, width: 1.5),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Club Opening',
                  style: lbBold.copyWith(
                      fontSize: 18, color: AppConstants.primaryColor),
                ),
                const SizedBox(height: 10),
                if (provider.canRemoveOpening)
                  NetworkAwareTap(
                    onTap: () async {
                      await provider.addRemoveOpening(
                          false, _bookClubId, context);
                      if (!provider.isSuccess) {
                        _showErrorMessage(provider.apiResponse);
                      }
                    },
                    child: Text(
                      'Remove',
                      style: lbItalic.copyWith(
                        fontSize: 12,
                        decoration: TextDecoration.underline,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  ),
              ],
            ),
            const Spacer(),
            const CustomCachedNetworkImage(
              imageUrl: '',
              width: 45,
              height: 45,
              errorImage: AppConstants.clubOpeningLogoImagePath,
            ),
          ],
        ),
      ),
    );
  }

  // --- DIALOGS ---
  // Note: I've kept the dialog logic largely the same as requested, but cleaned up the structure.

  void _assignLeaderBox() {
    final provider = context.read<UserClubDetailsProvider>();
    _roleController.clear();
    int? selectedUserId;
    int? selectedMemberId;

    showDialog(
      context: context,
      barrierColor: Colors.white60,
      barrierDismissible: false,
      builder: (dialogContext) {
        return AlertDialog(
          actionsPadding:
              const EdgeInsets.only(bottom: 30, left: 20, right: 20),
          insetPadding: const EdgeInsets.all(16),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
                color: AppConstants.popUpBorderColor, width: 1.5),
          ),
          surfaceTintColor: Colors.white,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: const EdgeInsets.only(top: 10, right: 10),
                  child: NetworkAwareTap(
                    onTap: () => Navigator.of(dialogContext).pop(),
                    child: Image.asset(AppConstants.closePopupImagePath,
                        height: 30, width: 30),
                  ),
                ),
              ),
              Text(
                "Assign New Club Leader",
                textAlign: TextAlign.center,
                style: lbRegular.copyWith(
                    fontSize: 18, color: AppConstants.primaryColor),
              ),
              const SizedBox(height: 25),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30.0),
                child: CustomDropdownMenu(
                  items: provider.clubMemberList ?? [],
                  itemToString: (item) => item.userName!,
                  controller: _roleController,
                  menuHeight: 200,
                  onSelected: (newValue) {
                    selectedUserId = newValue?.userId;
                    selectedMemberId = newValue?.bookClubMemberId;
                    _roleController.text = newValue?.userName ?? '';
                  },
                ),
              ),
              const SizedBox(height: 25),
            ],
          ),
          actions: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildDialogButton(
                  "Save",
                  isPrimary: true,
                  onTap: () async {
                    if (selectedUserId != null) {
                      Navigator.of(dialogContext).pop();
                      await provider.assignLeader(
                          _bookClubId,
                          selectedUserId,
                          _bookClubModel?.userId ?? 0,
                          selectedMemberId,
                          context);
                      if (provider.isSuccess) {
                        _confirmAssignLeaderBox();
                      }
                    }
                  },
                ),
                const SizedBox(width: 10),
                _buildDialogButton("Cancel",
                    onTap: () => Navigator.of(dialogContext).pop()),
              ],
            ),
          ],
        );
      },
    );
  }

  void _removeMemberBox(String name, int index) {
    showDialog(
      context: context,
      barrierColor: Colors.white60,
      barrierDismissible: false,
      builder: (dialogContext) {
        return Consumer<UserClubDetailsProvider>(
            builder: (context, provider, _) {
          return AlertDialog(
            actionsPadding:
                const EdgeInsets.only(bottom: 30, left: 20, right: 20),
            insetPadding: const EdgeInsets.all(16),
            contentPadding: EdgeInsets.zero,
            backgroundColor: AppConstants.backgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: const BorderSide(
                  color: AppConstants.popUpBorderColor, width: 1.5),
            ),
            surfaceTintColor: Colors.white,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Align(
                  alignment: Alignment.topRight,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 10, right: 10),
                    child: NetworkAwareTap(
                      onTap: () => Navigator.of(dialogContext).pop(),
                      child: Image.asset(AppConstants.closePopupImagePath,
                          height: 30, width: 30),
                    ),
                  ),
                ),
                Text(
                  "Remove Member",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                      fontSize: 18, color: AppConstants.primaryColor),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Text(
                    "Are you sure you would like to remove:\n$name",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                        fontSize: 12, color: AppConstants.primaryColor),
                  ),
                ),
                const SizedBox(height: 25),
              ],
            ),
            actions: [
              Row(
                mainAxisAlignment: provider.isLoading
                    ? MainAxisAlignment.center
                    : MainAxisAlignment.spaceBetween,
                children: [
                  CustomLoaderButton(
                    buttonRadius: 35,
                    buttonWidth: provider.isLoading
                        ? 45.0
                        : MediaQuery.sizeOf(context).width / 3,
                    buttonChild: provider.isLoading
                        ? const CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          )
                        : Text(
                            "Remove",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                    buttonPressed: () async {
                      await provider.removeMember(_bookClubId, index, context);
                      if (context.mounted) {
                        await provider.refreshSection(
                            'members', _bookClubId, context);
                      }
                      if (context.mounted) {
                        dialogContext.pop();
                      }
                      if (provider.isSuccess) {
                        _confirmRemoveBox(name);
                      }
                    },
                  ),
                  // _buildDialogButton("Remove", isPrimary: true,
                  //     onTap: () async {
                  //   Navigator.of(dialogContext).pop();
                  //   await provider.removeMember(_bookClubId, index, context);
                  //   if (provider.isSuccess) {
                  //     _confirmRemoveBox(name);
                  //   }
                  // }),
                  const SizedBox(width: 10),
                  provider.isLoading
                      ? const SizedBox.shrink()
                      : _buildDialogButton(
                          "Cancel",
                          onTap: () => dialogContext.pop(),
                        ),
                ],
              ),
            ],
          );
        });
      },
    );
  }

  void _showConfirmationDialog(
      {required String title,
      required String message,
      required VoidCallback onOk}) {
    showDialog(
      context: context,
      barrierColor: Colors.white60,
      barrierDismissible: false,
      builder: (dialogContext) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(bottom: 30),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
                color: AppConstants.popUpBorderColor, width: 1.5),
          ),
          surfaceTintColor: Colors.white,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: const EdgeInsets.only(top: 10, right: 10),
                  child: NetworkAwareTap(
                    onTap: () => Navigator.of(dialogContext).pop(),
                    child: Image.asset(AppConstants.closePopupImagePath,
                        height: 30, width: 30),
                  ),
                ),
              ),
              Text(
                title,
                textAlign: TextAlign.center,
                style: lbRegular.copyWith(
                    fontSize: 18, color: AppConstants.primaryColor),
              ),
              const SizedBox(height: 25),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Text(
                  message,
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                      fontSize: 12, color: AppConstants.primaryColor),
                ),
              ),
              const SizedBox(height: 25),
            ],
          ),
          actions: [
            Center(
              child: _buildDialogButton("Ok", isPrimary: true, onTap: () {
                Navigator.of(dialogContext).pop();
                onOk();
              }),
            ),
          ],
        );
      },
    );
  }

  void _confirmAssignLeaderBox() {
    _showConfirmationDialog(
      title: "Assign New Club Leader",
      message:
          "New Club leader has been assigned. You no longer have Leader admin privileges.",
      onOk: () => context.goNamed('Clubs'),
    );
  }

  void _confirmRemoveBox(String name) {
    _showConfirmationDialog(
      title: "Remove Member",
      message: "$name has been removed.",
      onOk: () {}, // Simply closes the dialog
    );
  }

  void _showSingleLeaderMessage() {
    _showConfirmationDialog(
      title: "Assign New Club Leader",
      message:
          "You have no other club members, you cannot assign a new leader.",
      onOk: () {}, // Simply closes the dialog
    );
  }

  void _showErrorMessage(String message) {
    _showConfirmationDialog(
      title: "Error",
      message: message,
      onOk: () {},
    );
  }

  // Helper to create consistent dialog buttons
  Widget _buildDialogButton(String text,
      {bool isPrimary = false, required VoidCallback onTap}) {
    return NetworkAwareTap(
      onTap: onTap,
      child: Container(
        height: 45,
        width: MediaQuery.of(context).size.width / 3,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(49),
          color: isPrimary
              ? AppConstants.textGreenColor
              : AppConstants.backgroundColor,
          border: isPrimary
              ? null
              : Border.all(color: AppConstants.popUpBorderColor),
        ),
        child: Center(
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: lbBold.copyWith(fontSize: 18),
          ),
        ),
      ),
    );
  }
}
