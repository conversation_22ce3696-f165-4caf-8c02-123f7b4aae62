import 'dart:ui';

import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import 'package:eljunto/reusableWidgets/cached_network_image.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../app/core/constants.dart';
import '../../../app/core/utils/text_style.dart';
import '../../../reusableWidgets/custom_button.dart';
import '../../../reusableWidgets/previous_screen_appbar.dart';

class ManageOutGoningScreen extends StatefulWidget {
  final String? buttonName;
  final int? bookClubId;
  const ManageOutGoningScreen({
    super.key,
    this.buttonName,
    this.bookClubId,
  });

  @override
  State<ManageOutGoningScreen> createState() => _ManageOutGoningScreenState();
}

class _ManageOutGoningScreenState extends State<ManageOutGoningScreen> {
  BookClubModel? bookCaseModel;
  String image = ApiConstants.imageBaseUrl;
  List<RequestManage>? outGoingRequestList;
  TextEditingController requestController = TextEditingController();
  bool invitationMsgValidation = false;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  BookClubController? bookClubController;
  bool isButtonLoading = false;

  @override
  void initState() {
    bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    super.initState();
    getOutGoingrequest();
    bookCaseModel = bookClubController?.bookClubModel;
    // Provider.of<BookClubController>(context, listen: false).bookClubModel;
  }

  bool isLoading = false;
  Future<void> getOutGoingrequest() async {
    isLoading = true;
    setState(() {});
    await Provider.of<BookClubController>(context, listen: false)
        .inComingRequest(
            widget.bookClubId ?? 0,
            ClubMembershipStatus.pending.value,
            ClubMembershipStatus.inActive.value,
            ClubRequestType.outGoingRequestByClubId.value,
            context)
        .then((value) {
      outGoingRequestList = bookClubController?.incomingOutGoingRequest?.data;
    });
    isLoading = false;
    setState(() {});
  }

  Future<void> rescindInvitation(int index, bool statusFlag) async {
    String? memberStatus;
    if (statusFlag) {
      memberStatus = ClubMembershipStatus.pending.value;
    } else {
      memberStatus = ClubMembershipStatus.reScind.value;
    }
    final payload = RequestManage(
      bookClubId: bookCaseModel?.bookClubId,
      bookClubMemberId: outGoingRequestList?[index].bookClubMemberId,
      userId: outGoingRequestList?[index].userId,
      initiatedBy: bookCaseModel?.userId,
      status: memberStatus,
      userType: ClubMemberType.member.value, // UNCOMMENT
    );

    await Provider.of<BookClubController>(context, listen: false)
        .updateInvitation(payload, context)
        .then((value) {
      if (statusFlag) {
        getOutGoingrequest();
      } else {
        getOutGoingrequest();
        confirmRescineBox(index);
      }
    });
  }

  bool checkStatus(int index) {
    if (outGoingRequestList?[index].status ==
        ClubMembershipStatus.isInActive.value) {
      return true;
    } else {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: PreviousScreenAppBar(
            bookName: widget.buttonName,
            clubName: bookCaseModel?.bookClubName,
            clubNameFlag: true,
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              "assets/images/PaperBackground.png",
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          enabled: isLoading,
          child: Column(
            children: [
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: NetworkAwareTap(
                  onTap: () {
                    context
                        .pushNamed(
                      'fellow-reader',
                    )
                        .then((_) {
                      getOutGoingrequest();
                    });
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      color: AppConstants.textGreenColor,
                      borderRadius: BorderRadius.circular(90),
                    ),
                    child: Center(
                      child: Text(
                        "Find Fellow Readers To Invite",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              outGoingRequestList?.isNotEmpty ?? false
                  ? Expanded(
                      child: Consumer<BookClubController>(
                          builder: (context, bookClubController, child) {
                        return ListView.builder(
                          scrollDirection: Axis.vertical,
                          padding: const EdgeInsets.only(
                            left: 20,
                            right: 20,
                          ),
                          itemCount: outGoingRequestList?.length,
                          itemBuilder: (context, index) {
                            final name = outGoingRequestList?[index].userName;

                            final urlImage = image +
                                (outGoingRequestList?[index]
                                        .userProfilePicture ??
                                    '');
                            return NetworkAwareTap(
                              onTap: () {
                                checkStatus(index)
                                    ? clubOpeningPopUp(name, index)
                                    : context.pushNamed(
                                        'club-member-profile',
                                        extra: {
                                          'userId': outGoingRequestList?[index]
                                              .userId,
                                          'userName':
                                              outGoingRequestList?[index]
                                                  .userName
                                        },
                                      );
                              },
                              child: Skeleton.replace(
                                replacement:
                                    cardSkeleton(index, urlImage, true),
                                child: cardSkeleton(index, urlImage, false),
                              ),
                            );
                          },
                        );
                      }),
                    )
                  : Padding(
                      padding: const EdgeInsets.only(top: 25.0),
                      child: Skeleton.replace(
                        replacement: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 20),
                          padding: const EdgeInsets.only(left: 15),
                          height: 45,
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                            color: AppConstants.skeletonBackgroundColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "No pending invitations",
                              textAlign: TextAlign.start,
                            ),
                          ),
                        ),
                        child: const NoDataWidget(
                          message: 'No pending invitations',
                        ),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  void rescindRequestBox(String? name, int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Cancel Invitation:",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Center(
                      child: Text(
                        "Are you sure you would like to cancel the invitation of:",
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Text(
                  "$name",
                  style: lbRegular.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: isButtonLoading
                        ? MainAxisAlignment.center
                        : MainAxisAlignment.spaceBetween,
                    children: [
                      CustomLoaderButton(
                        buttonWidth: isButtonLoading
                            ? 45.0
                            : MediaQuery.of(context).size.width / 3.2,
                        buttonRadius: 30.0,
                        buttonChild: isButtonLoading
                            ? const CircularProgressIndicator(
                                valueColor:
                                    AlwaysStoppedAnimation(Colors.white),
                                strokeWidth: 3.0,
                              )
                            : Text(
                                "Cancel",
                                style: lbBold.copyWith(
                                  fontSize: 18,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                        buttonPressed: () async {
                          setState(() {
                            isButtonLoading = true;
                          });
                          context.pop();
                          await rescindInvitation(index, false);

                          setState(() {
                            isButtonLoading = false;
                          });
                        },
                      ),
                      !isButtonLoading
                          ? NetworkAwareTap(
                              onTap: () {
                                context.pop();
                              },
                              child: Container(
                                height: 45,
                                width: MediaQuery.of(context).size.width / 3.2,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(49),
                                  border: Border.all(
                                    color: AppConstants.popUpBorderColor,
                                  ),
                                  color: AppConstants.backgroundColor,
                                ),
                                child: Center(
                                  child: Text(
                                    "Go Back",
                                    textAlign: TextAlign.center,
                                    style: lbBold.copyWith(
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                              ),
                            )
                          : const SizedBox.shrink(),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void confirmRescineBox(int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Cancel Invitation:",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Center(
                    child: Text(
                      "Invitation has been rescinded.",
                      style: lbRegular.copyWith(
                        fontSize: 12,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    setState(() {});
                    context.pop();
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void clubOpeningPopUp(String? name, int index) {
    showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        invitationMsgValidation = false;
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            actionsPadding: const EdgeInsets.only(right: 10),
            insetPadding: const EdgeInsets.all(20),
            contentPadding: EdgeInsets.zero,
            backgroundColor: AppConstants.backgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: const BorderSide(
                color: AppConstants.popUpBorderColor,
                width: 1.5,
              ),
            ),
            surfaceTintColor: Colors.white,
            actions: [
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        context.pop();
                      },
                      child: Container(
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(
                          top: 10,
                        ),
                        child: Image.asset(
                          AppConstants.closePopupImagePath,
                          height: 30,
                          width: 30,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width,
                            child: Center(
                              child: Text(
                                "Re-invite: ${outGoingRequestList?[index].userName}",
                                textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                                style: lbRegular.copyWith(
                                  fontSize: 18,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 15,
                          ),
                          Center(
                            child: Text(
                              '${outGoingRequestList?[index].bookClubName}',
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                              style: lbRegular.copyWith(
                                fontSize: 12,
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 25,
                          ),
                          Text(
                            'Write an invitation message:',
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                            style: lbRegular.copyWith(
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Stack(
                            clipBehavior: Clip.none,
                            children: [
                              TextFormField(
                                controller: requestController,
                                maxLines: 5,
                                style: lbRegular.copyWith(
                                  fontSize: 12,
                                ),
                                decoration: InputDecoration(
                                  contentPadding: const EdgeInsets.all(10),
                                  filled: true,
                                  fillColor: Colors.white,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                ),
                                onChanged: (value) {
                                  setState(() {
                                    invitationMsgValidation = false;
                                  });
                                },
                                validator: (value) {
                                  invitationMsgValidation = false;
                                  if (value!.isEmpty) {
                                    invitationMsgValidation = true;
                                  } else {
                                    invitationMsgValidation = false;
                                  }
                                  setState(() {});
                                  return null;
                                },
                              ),
                              Positioned(
                                top: 110,
                                left: 0,
                                right: 0,
                                child: invitationMsgValidation
                                    ? Text(
                                        '*Enter a message',
                                        style: lbRegular.copyWith(
                                          fontSize: 14,
                                          color: AppConstants.redColor,
                                        ),
                                      )
                                    : const SizedBox.shrink(),
                              ),
                            ],
                          ),
                          invitationMsgValidation
                              ? const SizedBox(
                                  height: 25,
                                )
                              : const SizedBox.shrink(),
                          const SizedBox(
                            height: 25,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              NetworkAwareTap(
                                onTap: () async {
                                  bool validation =
                                      _formKey.currentState!.validate();
                                  if (validation &&
                                      requestController.text.isNotEmpty) {
                                    await rescindInvitation(index, true);
                                    context.pop();
                                  }
                                  //else {
                                  //   setState(
                                  //     () {
                                  //       invitationMsgValidation = true;
                                  //     },
                                  //   );
                                  // }
                                },
                                child: Container(
                                  height: 45,
                                  width: MediaQuery.of(context).size.width / 3,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(49),
                                    color: AppConstants.textGreenColor,
                                  ),
                                  child: Center(
                                    child: Text(
                                      "Reinvite",
                                      textAlign: TextAlign.center,
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              NetworkAwareTap(
                                onTap: () {
                                  context.pop();
                                  // confirmBox();
                                },
                                child: Container(
                                  height: 45,
                                  width: MediaQuery.of(context).size.width / 3,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(49),
                                    color: Colors.transparent,
                                    border: Border.all(
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      "Go Back",
                                      textAlign: TextAlign.center,
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 25,
                          ),
                          NetworkAwareTap(
                            onTap: () {
                              context.pop();
                              rescindRequestBox(name, index);
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: Colors.transparent,
                                border: Border.all(
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  "Cancel Invitation",
                                  textAlign: TextAlign.center,
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 30,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )
            ],
          );
        });
      },
    );
  }

  Widget cardSkeleton(int index, String urlImage, bool isSkeleton) {
    return Container(
      padding: const EdgeInsets.all(14),
      margin: const EdgeInsets.only(top: 25),
      height: 87,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isSkeleton
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: checkStatus(index) || isSkeleton
              ? AppConstants.isActiveRequestColor
              : AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                outGoingRequestList?[index].userName ?? '',
                style: lbBold.copyWith(
                  fontSize: 18,
                  color: checkStatus(index)
                      ? AppConstants.isActiveRequestColor
                      : AppConstants.primaryColor,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              NetworkAwareTap(
                onTap: () {
                  final name = outGoingRequestList?[index].userName;
                  checkStatus(index)
                      ? clubOpeningPopUp(name, index)
                      : rescindRequestBox(name, index);
                },
                child: Text(
                  'Cancel Invitation',
                  style: lbItalic.copyWith(
                    fontSize: 14,
                    decorationColor: checkStatus(index)
                        ? AppConstants.isActiveRequestColor
                        : AppConstants.primaryColor,
                    decoration: TextDecoration.underline,
                    color: checkStatus(index)
                        ? AppConstants.isActiveRequestColor
                        : AppConstants.primaryColor,
                  ),
                ),
              )
            ],
          ),
          const Spacer(),
          ClipRRect(
            borderRadius: BorderRadius.circular(49),
            child: ImageFiltered(
              imageFilter: checkStatus(index)
                  ? ImageFilter.blur(sigmaX: 2, sigmaY: 2)
                  : ImageFilter.blur(),
              child: CustomCachedNetworkImage(
                imageUrl: urlImage,
                width: 45,
                height: 45,
                errorImage: AppConstants.profileLogoImagePath,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
