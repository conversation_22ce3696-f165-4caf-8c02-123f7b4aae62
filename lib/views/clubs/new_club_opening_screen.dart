import 'dart:developer';

import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/helpers/datetime_helper.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/controller/session_manager.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../app/core/constants.dart';
import '../../app/core/utils/text_style.dart';
import '../../controller/book_club_controller.dart';
import '../../models/home_model/home_screen1_model/new_club_opening_model.dart';
import '../../reusableWidgets/marquee_text.dart';
import '../../reusableWidgets/matches_bottom_sheet/club_opening_matches_sheet.dart';
import '../../reusableWidgets/no_data_widget.dart';
import '../../reusableWidgets/previous_screen_appbar.dart';

class NewClubOpeningScreen extends StatefulWidget {
  const NewClubOpeningScreen({super.key});

  @override
  State<NewClubOpeningScreen> createState() => _NewClubOpeningScreenState();
}

class _NewClubOpeningScreenState extends State<NewClubOpeningScreen> {
  final ScrollController _scrollController = ScrollController();
  bool isLoadingMore = false;
  BookClubController? bookClubController;
  int? loggedinUserId;
  int offset = 0;
  int clubOpeningLimit = 10;
  bool clubOpenigLoading = true;
  int clubOpeningcount = 0;
  bool isLoading = true;
  List<MatchedMeeting>? matchedMeetings;

  List<NewClubOpeningList> clubList = [];
  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    _scrollController.addListener(_onScroll);
    _initializeUserId();
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _initializeUserId() async {
    loggedinUserId = _sessionManager.userId;
    await getNewClubOpenings(false);
    setState(() {
      isLoading = false;
    });
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent &&
        !isLoadingMore &&
        clubList.length < (bookClubController?.clubOpeningCount ?? 0)) {
      getNewClubOpenings(true);
      // getFellowReaders(true); // Fetch more data
    }
  }

  Future<void> getNewClubOpenings(bool isMore) async {
    if (clubList.length <= (bookClubController?.clubOpeningCount ?? 0) ||
        !isMore) {
      isLoadingMore = true;

      if (isMore) {
        clubOpeningLimit += 10; // Increment the limit for the next load
      }
    }
    // isLoading = true;
    await Provider.of<BookClubController>(context, listen: false)
        .getNewClubs(loggedinUserId ?? 0, offset, clubOpeningLimit, context)
        .then((responseMap) {
      clubList.clear();
      clubList = bookClubController?.clubList ?? [];
    }).whenComplete(() {
      isLoadingMore = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: "Outgoing Club Requests",
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          containersColor: AppConstants.skeletonBackgroundColor,
          enabled: isLoading,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 25,
                ),
                Text(
                  "New Club Openings",
                  style: lbRegular.copyWith(
                    fontSize: 20,
                  ),
                ),
                const SizedBox(
                  height: 12,
                ),
                clubList.isNotEmpty
                    ? Expanded(
                        child: Consumer<BookClubController>(
                          builder: (context, homeController, child) {
                            return ListView.builder(
                              controller: _scrollController,
                              // padding: const EdgeInsets.only(bottom: 25),
                              scrollDirection: Axis.vertical,
                              itemCount: isLoadingMore
                                  ? clubList.length + 1
                                  : clubList.length,
                              itemBuilder: (context, index) {
                                if (index == clubList.length && isLoadingMore) {
                                  return const Padding(
                                    padding: EdgeInsets.only(bottom: 10.0),
                                    child: Center(
                                      child: CircularProgressIndicator(
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                  );
                                }

                                matchedMeetings =
                                    clubList[index].matchedMeetings ?? [];

                                String meetingTime = '';
                                String? authorName = '';
                                String? clubCount = '';
                                String? selectedBook = '';
                                String? selectedBookAuthor = '';
                                String? partCovered = '';

                                partCovered =
                                    clubList[index].latestPartsOfBookCovered ??
                                        '';

                                meetingTime =
                                    DateTimeHelper.getMeetingScheduleTime(
                                        clubList[index]
                                                .latestMeetingStartTime ??
                                            0,
                                        clubList[index].latestMeetingEndTime ??
                                            0);
                                if (clubList[index].clubType ==
                                    ClubType.impromptu.value) {
                                  authorName = clubList[index].latestBookAuthor;
                                  clubCount = clubList[index].clubCount ?? '';
                                } else if (clubList[index].clubType ==
                                    ClubType.standing.value) {
                                  selectedBook = clubList[index].latestBookName;
                                  selectedBookAuthor =
                                      clubList[index].latestBookAuthor;
                                }

                                int currentDate =
                                    DateTime.now().millisecondsSinceEpoch;

                                String meetingHeading = ((clubList[index]
                                                .latestMeetingDate ??
                                            0) >=
                                        currentDate)
                                    ? 'Next Meeting:'
                                    : ((clubList[index].latestMeetingDate) !=
                                                null &&
                                            ((clubList[index]
                                                        .latestMeetingDate) !=
                                                    null &&
                                                (clubList[index]
                                                            .latestMeetingDate ??
                                                        0) <=
                                                    currentDate))
                                        ? 'Previous Meeting:'
                                        : 'No new meeting scheduled';
                                String formattedDate = (meetingHeading ==
                                        'Next Meeting:')
                                    ? DateTimeHelper.getDayMonthYearDateFormat(
                                        clubList[index].latestMeetingDate)
                                    : (meetingHeading == 'Previous Meeting:')
                                        ? DateTimeHelper
                                            .getDayMonthYearDateFormat(
                                                clubList[index]
                                                    .latestMeetingDate)
                                        : 'TBD';

                                meetingTime =
                                    DateTimeHelper.getMeetingScheduleTime(
                                        clubList[index]
                                                .latestMeetingStartTime ??
                                            0,
                                        clubList[index].latestMeetingEndTime ??
                                            0);

                                // String meetingHeading = (clubList[index]
                                //             .nextMeetingDate !=
                                //         null)
                                //     ? 'Next Meeting:'
                                //     : (clubList[index].previousMeetingDate != null)
                                //         ? 'Previous Meeting:'
                                //         : 'No new meeting scheduled';
                                // String formattedDate =
                                //     (meetingHeading == 'Next Meeting:')
                                //         ? CommonHelper.getDayMonthYearDateFormat(
                                //             clubList[index].nextMeetingDate)
                                //         : (meetingHeading == 'Previous Meeting:')
                                //             ? CommonHelper.getDayMonthYearDateFormat(
                                //                 clubList[index].previousMeetingDate)
                                //             : 'TBD';
                                // String? authorName = '';
                                // String? clubCount = '';
                                // String? selectedBook = '';
                                // String? selectedBookAuthor = '';
                                // if (clubList[index].clubType == ClubType.impromptu) {
                                //   authorName = clubList[index].bookAuthor;
                                //   clubCount = clubList[index].clubCount;
                                // } else if (clubList[index].clubType ==
                                //     ClubType.standing) {
                                //   selectedBook = clubList[index].bookName;
                                //   selectedBookAuthor = clubList[index].bookAuthor;
                                // }
                                // final meetingTime =
                                //     CommonHelper.getMeetingScheduleTime(
                                //         clubList[index].meetingStartTime ?? 0,
                                //         clubList[index].meetingEndTime ?? 0);

                                return NetworkAwareTap(
                                  onTap: () {
                                    context.pushNamed(
                                      'club-details',
                                      // queryParameters: {
                                      //   'bookClubId': clubList[index].clubId,
                                      // },
                                      extra: {
                                        'bookClubId': clubList[index].clubId,
                                        'bookClubName':
                                            clubList[index].clubName,
                                        'impromptuCount': clubCount,
                                      },
                                    );
                                  },
                                  child: Skeleton.replace(
                                    replacement: standingSkeleton(
                                      false,
                                      homeController,
                                      index,
                                      authorName ?? '',
                                      clubCount,
                                      selectedBook ?? '',
                                      meetingTime,
                                      formattedDate,
                                      meetingHeading,
                                      selectedBookAuthor ?? '',
                                      partCovered,
                                    ),
                                    child: standingSkeleton(
                                      true,
                                      homeController,
                                      index,
                                      authorName ?? '',
                                      clubCount,
                                      selectedBook ?? '',
                                      meetingTime,
                                      formattedDate,
                                      meetingHeading,
                                      selectedBookAuthor ?? '',
                                      partCovered,
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      )
                    : Skeleton.replace(
                        replacement: Container(
                          padding: const EdgeInsets.only(left: 20),
                          margin: const EdgeInsets.symmetric(horizontal: 20),
                          height: 50,
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                            color: AppConstants.skeletonBackgroundColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "No new club openings",
                              textAlign: TextAlign.start,
                            ),
                          ),
                        ),
                        child: const NoDataWidget(
                          message: "No new club openings",
                        ),
                      ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget standingSkeleton(
    bool isBorder,
    BookClubController homeController,
    int index,
    String authorName,
    String clubCount,
    String selectedBook,
    String meetingTime,
    String formattedDate,
    String meetingHeading,
    String selectedBookAuthor,
    String partCovered,
  ) {
    return Container(
      padding: const EdgeInsets.all(14),
      margin: const EdgeInsets.only(bottom: 25),
      width: 320,
      height: 217,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? Colors.transparent
            : AppConstants.skeletonBackgroundColor,
        border: isBorder
            ? Border.all(
                color: AppConstants.primaryColor,
                width: 1.5,
              )
            : Border.all(
                color: Colors.transparent,
              ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Wrap the text in a Flexible to prevent overflow
              Flexible(
                fit: FlexFit.tight,
                flex: 15,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MarqueeList(
                      children: [
                        Text(
                          clubList[index].clubName ?? '',
                          overflow: TextOverflow.ellipsis,
                          style: lbBold.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                    if (clubList[index].clubType == ClubType.impromptu &&
                        authorName.isNotEmpty &&
                        clubCount.isNotEmpty) ...[
                      Text(
                        authorName,
                        overflow: TextOverflow.ellipsis,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        clubCount,
                        overflow: TextOverflow.ellipsis,
                        style: lbItalic.copyWith(
                          fontSize: 12,
                        ),
                      ),
                    ] else ...[
                      const SizedBox.shrink(),
                    ],
                  ],
                ),
              ),
              const SizedBox(width: 5),
              const Spacer(),
              // Ensure the image does not exceed space
              SizedBox(
                height: 50,
                width: 50,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(49),
                  child: Image.asset(
                    //clubList[index].clubLogo ?? '',
                    AppConstants.clubOpeningLogoImagePath,
                    fit: BoxFit.cover,
                    filterQuality: FilterQuality.high,
                  ),
                ),
              ),
            ],
          ),
          (clubList[index].clubType == ClubType.impromptu)
              ? const SizedBox(height: 31)
              : const SizedBox.shrink(),
          const SizedBox(
            height: 5,
          ),
          Text(
            meetingHeading,
            overflow: TextOverflow.ellipsis,
            style: lbRegular.copyWith(fontSize: 12),
          ),
          if (meetingHeading != "No new meeting scheduled") ...[
            if (clubList[index].clubType == ClubType.standing) ...[
              const SizedBox(
                height: 5,
              ),
              Text(
                selectedBook,
                overflow: TextOverflow.ellipsis,
                style: lbBold.copyWith(
                  fontSize: 12,
                ),
              ),
              const SizedBox(
                height: 5,
              ),
              Text(
                selectedBookAuthor,
                overflow: TextOverflow.ellipsis,
                style: lbRegular.copyWith(fontSize: 12),
              ),
            ] else ...[
              const SizedBox.shrink(),
            ],
            const SizedBox(height: 5),
            Text(
              partCovered,
              overflow: TextOverflow.ellipsis,
              style: lbRegular.copyWith(fontSize: 12),
            ),
            const SizedBox(height: 5),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              // crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      formattedDate,
                      overflow: TextOverflow.ellipsis,
                      style: lbItalic.copyWith(fontSize: 12),
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Text(
                      meetingTime,
                      overflow: TextOverflow.ellipsis,
                      style: lbItalic.copyWith(fontSize: 12),
                    ),
                  ],
                ),
                const Spacer(),
                NetworkAwareTap(
                  onTap: () {
                    /// SHOW BOTTOM SHEET
                    if ((clubList[index].isCurrentlyReadingMatch ?? 0) > 0) {
                      matchedMeetings = clubList[index].matchedMeetings;
                      clubMatchBottomSheet(index).then((_) {
                        getNewClubOpenings(false);
                        setState(() {});
                      });
                    } else {
                      log("No Match Found");
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color:
                          ((clubList[index].isCurrentlyReadingMatch ?? 0) > 0)
                              ? AppConstants.textGreenColor
                              : AppConstants.isActiveRequestColor,
                    ),

                    /// ${clubList[index].isCurrentlyReadingMatch ?? 0}
                    child: Text(
                      ((clubList[index].isCurrentlyReadingMatch ?? 0) == 0)
                          ? "No Match"
                          : ((clubList[index].isCurrentlyReadingMatch ?? 0) >
                                      0 &&
                                  (clubList[index].isCurrentlyReadingMatch ??
                                          0) <
                                      2)
                              ? "${clubList[index].isCurrentlyReadingMatch ?? 0} Match"
                              : "${clubList[index].isCurrentlyReadingMatch ?? 0} Matches",
                      overflow: TextOverflow.ellipsis,
                      style: lbBold.copyWith(
                        fontSize: 12,
                        color:
                            ((clubList[index].isCurrentlyReadingMatch ?? 0) > 0)
                                ? AppConstants.primaryColor
                                : Colors.black38,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            // Text(
            //   formattedDate,
            //   overflow: TextOverflow.ellipsis,
            //   style: lbItalic.copyWith(fontSize: 12),
            // ),
            // const SizedBox(
            //   height: 5,
            // ),
            // Text(
            //   meetingTime,
            //   overflow: TextOverflow.ellipsis,
            //   style: lbItalic.copyWith(fontSize: 12),
            // ),
          ] else ...[
            const Spacer(),
            Align(
              alignment: Alignment.bottomRight,
              child: NetworkAwareTap(
                onTap: () {
                  /// SHOW BOTTOM SHEET
                  if ((clubList[index].isCurrentlyReadingMatch ?? 0) > 0) {
                    matchedMeetings = clubList[index].matchedMeetings;
                    clubMatchBottomSheet(index).then((_) {
                      getNewClubOpenings(false);
                      setState(() {});
                    });
                  } else {
                    log("No Match Found");
                  }
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    color: ((clubList[index].isCurrentlyReadingMatch ?? 0) > 0)
                        ? AppConstants.textGreenColor
                        : AppConstants.isActiveRequestColor,
                  ),

                  /// ${clubList[index].isCurrentlyReadingMatch ?? 0}
                  child: Text(
                    ((clubList[index].isCurrentlyReadingMatch ?? 0) == 0)
                        ? "No Match"
                        : ((clubList[index].isCurrentlyReadingMatch ?? 0) > 0 &&
                                (clubList[index].isCurrentlyReadingMatch ?? 0) <
                                    2)
                            ? "${clubList[index].isCurrentlyReadingMatch ?? 0} Match"
                            : "${clubList[index].isCurrentlyReadingMatch ?? 0} Matches",
                    overflow: TextOverflow.ellipsis,
                    style: lbBold.copyWith(
                      fontSize: 12,
                      color:
                          ((clubList[index].isCurrentlyReadingMatch ?? 0) > 0)
                              ? AppConstants.primaryColor
                              : Colors.black38,
                    ),
                  ),
                ),
              ),
            ),
          ]
        ],
      ),
    );
  }

  Future<void> clubMatchBottomSheet(int index) async {
    log("Matches : ${matchedMeetings?.length ?? 0}");
    return showModalBottomSheet(
      barrierColor: Colors.white60,
      isScrollControlled: true,
      useRootNavigator: true,
      context: context,
      builder: (context) {
        return ClubOpeningMatchesBottomSheet(
          clubId: clubList[index].clubId ?? 0,
          clubName: clubList[index].clubName ?? '',
          memberRequestPrompt: clubList[index].memberRequestPrompt ?? '',
          matchedMeetings: matchedMeetings ?? [],
          clubList: clubList[index],
        );
      },
    );
  }
}
