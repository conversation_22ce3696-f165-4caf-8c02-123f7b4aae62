import 'dart:developer';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/controller/session_manager.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/club_membership_model.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import 'package:eljunto/models/home_model/home_screen2_model/meeting_model.dart';
import 'package:eljunto/models/meeting_join_model.dart';
import 'package:eljunto/views/clubs/clubs_home/services/clubs_sync_service.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

/// Dedicated provider for User Club Details screen
/// Implements targeted refresh methods and event-based updates
class UserClubDetailsProvider with ChangeNotifier {
  // Dependencies
  BookClubController? _bookClubController;
  MessageController? _messageController;

  // State variables
  BookClubModel? _bookClubModel;
  List<ClubMembershipModel>? _memberList;
  List<RequestManage>? _incomingRequestList;
  List<MeetingList>? _upcomingMeetings;
  List<MeetingList>? _previousMeetings;
  MeetingJoinModel? _meetingJoinModel;

  // User data
  int? _loggedInUserId;
  String? _userName;
  String? _userHandle;

  // Loading states
  bool _isInitialLoading = false;
  final bool _isMembersLoading = false;
  bool _isUpcomingMeetingsLoading = false;
  bool _isPreviousMeetingsLoading = false;
  final Map<int, bool> _meetingLoadingStates = {};

  // Pagination
  int _upcomingMeetingLimit = 10;
  int _previousMeetingLimit = 10;
  int _upcomingMeetingCount = 0;
  int _previousMeetingCount = 0;
  final int _offset = 0;

  // Getters
  BookClubModel? get bookClubModel => _bookClubModel;

  List<ClubMembershipModel>? get memberList => _memberList;

  List<RequestManage>? get incomingRequestList => _incomingRequestList;

  List<MeetingList>? get upcomingMeetings => _upcomingMeetings;

  List<MeetingList>? get previousMeetings => _previousMeetings;

  MeetingJoinModel? get meetingJoinModel => _meetingJoinModel;

  int? get loggedInUserId => _loggedInUserId;

  String? get userName => _userName;

  String? get userHandle => _userHandle;

  bool get isInitialLoading => _isInitialLoading;

  bool get isMembersLoading => _isMembersLoading;

  bool get isUpcomingMeetingsLoading => _isUpcomingMeetingsLoading;

  bool get isPreviousMeetingsLoading => _isPreviousMeetingsLoading;

  bool isMeetingLoading(int meetingId) =>
      _meetingLoadingStates[meetingId] ?? false;

  bool _isLoading = false;

  bool get isLoading => _isLoading;

  // Getter for the total number of items in the list (members + openings)
  int get totalListItems =>
      (memberList?.length ?? 0) + (memberList?.first.totalVacancies ?? 0);

  // Getter for total vacancies (safer than accessing from the list directly)
  int get totalVacancies => memberList?.first.totalVacancies ?? 0;

  // Getter to safely check if the remove button for openings should be shown
  bool get canRemoveOpening => (memberList?.length ?? 0) + totalVacancies > 1;

  int get upcomingMeetingCount => _upcomingMeetingCount;

  int get previousMeetingCount => _previousMeetingCount;

  bool get hasNewStandingClubRequest =>
      _messageController?.hasNewStandingClubRequest ?? false;

  BookClubController? get bookClubController => _bookClubController;

  final _sessionManager = locator<SessionManager>();

  /// Initialize the provider with required dependencies
  void initialize(BuildContext context) {
    _bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    _messageController = Provider.of<MessageController>(context, listen: false);
  }

  /// Initialize user data from local storage
  Future<void> initializeUserData() async {
    _userName = _sessionManager.userName;
    _userHandle = _sessionManager.userHandle;
    _loggedInUserId = _sessionManager.userId;
    notifyListeners();
  }

  /// Load all initial data for the club details screen
  Future<void> loadInitialData(int bookClubId, BuildContext context) async {
    _isInitialLoading = true;
    notifyListeners();

    try {
      await initializeUserData();

      // Load all data concurrently for better performance
      if (context.mounted) {
        await Future.wait([
          getClubDetails(bookClubId, context),
          getUpcomingMeetings(bookClubId, false, context),
          getPreviousMeetings(bookClubId, false, context),
          getBookClubMembers(bookClubId, context),
          getIncomingRequest(bookClubId, context),
        ]);
      }
    } catch (e) {
      log('Error loading initial data: $e');
    } finally {
      _isInitialLoading = false;
      notifyListeners();
    }
  }

  /// Get club details - targeted refresh method
  Future<void> getClubDetails(int bookClubId, BuildContext context) async {
    try {
      final responseMap = await _bookClubController?.getBookClubs(
        '',
        null,
        bookClubId,
        context,
        null,
        null,
      );

      if (responseMap?["statusCode"] == 200) {
        if (responseMap?["data"].isNotEmpty) {
          final bookClubList = (responseMap?["data"] as List)
              .map((item) => BookClubModel.fromJson(item))
              .toList();

          if (bookClubList.isNotEmpty) {
            _bookClubModel = bookClubList[0];
            _bookClubController?.updateData(_bookClubModel!);
            notifyListeners();
          }
        }
      }
    } catch (e) {
      log('Error getting club details: $e');
    }
  }

  /// Get book club members - targeted refresh method
  Future<void> getBookClubMembers(int bookClubId, BuildContext context) async {
    _isLoading = true;
    notifyListeners();

    try {
      final responseMap = await _bookClubController?.getBookClubMembers(
        bookClubId,
        context,
      );

      if (responseMap?["statusCode"] == 200) {
        _memberList = (responseMap?["data"] as List)
            .map((item) => ClubMembershipModel.fromJson(item))
            .toList();

        filterNonLeaderMembers();
      }
    } catch (e) {
      log('Error getting book club members: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Get upcoming meetings with pagination support
  Future<void> getUpcomingMeetings(
      int bookClubId, bool isLoadMore, BuildContext context) async {
    log("Provider bookClubId : $bookClubId");
    if (isLoadMore &&
        (_upcomingMeetings?.length ?? 0) >= _upcomingMeetingCount) {
      log("Provider bookClubId1 : $bookClubId");
      return; // No more data to load
    }
    log("Provider bookClubId 2: $bookClubId");
    if (isLoadMore &&
        (_upcomingMeetings?.length ?? 0) <= _upcomingMeetingCount) {
      _isUpcomingMeetingsLoading = true;
      _upcomingMeetingLimit += 10;
    }
    notifyListeners();

    try {
      await _bookClubController?.getBookClubUpcomingMeetings(
        bookClubId,
        _upcomingMeetingLimit,
        _offset,
        context,
      );

      _upcomingMeetingCount =
          _bookClubController?.upcomingMeetingModel?.count ?? 0;
      _upcomingMeetings = _bookClubController?.upcomingMeetingModel?.data;
      log("Upcoming meetings provider : $_upcomingMeetings");
    } catch (e) {
      log('Error getting upcoming meetings: $e');
    } finally {
      _isUpcomingMeetingsLoading = false;
      notifyListeners();
    }
  }

  /// Get previous meetings with pagination support
  Future<void> getPreviousMeetings(
      int bookClubId, bool isLoadMore, BuildContext context) async {
    if (isLoadMore &&
        (_previousMeetings?.length ?? 0) >= _previousMeetingCount) {
      return; // No more data to load
    }

    _isPreviousMeetingsLoading = true;
    if (isLoadMore) {
      _previousMeetingLimit += 10;
    }
    notifyListeners();

    try {
      await _bookClubController?.getBookClubPreviousMeetings(
        bookClubId,
        _previousMeetingLimit,
        _offset,
        context,
      );

      _previousMeetingCount =
          _bookClubController?.previousMeetingModel?.count ?? 0;
      _previousMeetings = _bookClubController?.previousMeetingModel?.data;
    } catch (e) {
      log('Error getting previous meetings: $e');
    } finally {
      _isPreviousMeetingsLoading = false;
      notifyListeners();
    }
  }

  /// Get incoming requests - targeted refresh method
  Future<void> getIncomingRequest(int bookClubId, BuildContext context) async {
    try {
      await _bookClubController?.inComingRequest(
        bookClubId,
        ClubMembershipStatus.pending.value,
        '',
        ClubRequestType.incomingRequestByClubId.value,
        context,
      );

      _incomingRequestList = _bookClubController?.incomingOutGoingRequest?.data;

      // Update message controller with request status
      await _messageController?.updateStandingClubRequests(
        _incomingRequestList?.isNotEmpty ?? false,
      );
      if (context.mounted) {
        await _messageController?.manageIncomingRequestStatus(
          _incomingRequestList?.isNotEmpty ?? false,
          context,
        );
      }

      _bookClubController?.incomingRequestFunction(_incomingRequestList);
      notifyListeners();
    } catch (e) {
      log('Error getting incoming requests: $e');
    }
  }

  /// Join meeting functionality
  Future<bool> joinMeeting(int meetingId, String channelName) async {
    // Set loading state for this specific meeting
    _meetingLoadingStates[meetingId] = true;
    notifyListeners();

    try {
      final result = await _bookClubController?.joinMeeting(
        meetingId,
        _loggedInUserId ?? 0,
        channelName,
      );

      if (result == true) {
        _meetingJoinModel = _bookClubController?.meetingJoinModel;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      log('Error joining meeting: $e');
      return false;
    } finally {
      // Clear loading state for this meeting
      _meetingLoadingStates[meetingId] = false;
      notifyListeners();
    }
  }

  /// Remove member from club
  Future<bool> removeMember(
      int bookClubId, int memberIndex, BuildContext context) async {
    try {
      _isLoading = true;
      notifyListeners();
      final member = _memberList?[memberIndex];
      if (member == null) return false;

      final payload = RequestManage(
        bookClubId: bookClubId,
        bookClubMemberId: member.bookClubMemberId,
        userId: member.userId,
        initiatedBy: _loggedInUserId,
        status: ClubMembershipStatus.left.value,
      );

      final result =
          await _bookClubController?.updateInvitation(payload, context);

      if (result?["statusCode"] == 200) {
        // Remove member from local list
        _memberList?.removeAt(memberIndex);

        // Notify other screens about the club membership change
        final clubType = _bookClubModel?.bookClubType ?? '';
        ClubsSyncService().onUserLeftClub(bookClubId, clubType);

        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      log('Error removing member: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Check if current user is club leader
  bool isCurrentUserLeader() {
    return _bookClubModel?.userId == _loggedInUserId;
  }

  /// Get member profile picture URL
  String getMemberProfilePictureUrl(int index) {
    final member = _memberList?[index];
    if (member?.userProfilePicture != null) {
      return '${ApiConstants.imageBaseUrl}${member!.userProfilePicture!}';
    }
    return AppConstants.profileLogoImagePath;
  }

  /// Refresh specific section
  Future<void> refreshSection(
      String section, int bookClubId, BuildContext context) async {
    log("Section Type 1: $section");

    switch (section) {
      case 'members':
        await getBookClubMembers(bookClubId, context);
        break;
      case 'upcoming_meetings':
        log("Section Type 2: $section");
        _upcomingMeetingLimit = 10; // Reset pagination
        await getUpcomingMeetings(bookClubId, false, context);
        break;
      case 'previous_meetings':
        _previousMeetingLimit = 10; // Reset pagination
        await getPreviousMeetings(bookClubId, false, context);
        break;
      case 'incoming_requests':
        await getIncomingRequest(bookClubId, context);
        break;
      case 'club_details':
        await getClubDetails(bookClubId, context);
        break;
    }
  }

  /// MEETING DELETE FUNCTION
  bool isDeleteMeetingLoading = false;

  Future<bool> deleteMeeting(int meetingId, BuildContext context) async {
    bool isDeleted = false;
    try {
      isDeleteMeetingLoading = true;
      notifyListeners();
      isDeleted = await Provider.of<BookClubController>(context, listen: false)
          .deleteMeeting(meetingId, context);

      if (isDeleted && _bookClubModel != null) {
        // Refresh the meeting lists after successful deletion
        await refreshMeetingLists(_bookClubModel!.bookClubId!, context);
      }
    } catch (e) {
      log("Delete Call  ${e.toString()}");
      isDeleted = false;
    }
    log("IsDeleted : $isDeleted");
    isDeleteMeetingLoading = false;
    notifyListeners();
    return isDeleted;
  }

  /// Refresh meeting lists
  Future<void> refreshMeetingLists(int bookClubId, BuildContext context) async {
    await Future.wait(
      [
        getUpcomingMeetings(bookClubId, true, context),
        getPreviousMeetings(bookClubId, true, context),
      ],
    );
    notifyListeners();
  }

  List<ClubMembershipModel>? clubMemberList;

  void filterNonLeaderMembers() {
    if (memberList != null) {
      // Assuming the leader is the first member in the memberList
      clubMemberList = memberList?.sublist(1) // Skip the first member (leader)
          ;
    }
  }

  // Future<void> removeMember(int index) async {
  //   final payload = RequestManage(
  //     bookClubId: bookCaseModel?.bookClubId,
  //     bookClubMemberId: provider.memberList?[index].bookClubMemberId,
  //     userId: provider.memberList?[index].userId,
  //     initiatedBy: bookCaseModel?.userId,
  //     status: ClubMembershipStatus.removed,
  //   );

  //   await Provider.of<BookClubController>(context, listen: false)
  //       .updateInvitation(payload, context)
  //       .then((value) async {
  //     confirmRemoveBox(index);
  //     await provider.getBookClubMembers(
  //         bookCaseModel?.bookClubId ?? 0, context);
  //   });
  // }

  bool addRemoveOpeningLoading = false;
  String apiResponse = '';
  bool isSuccess = true;

  Future<void> addRemoveOpening(
      bool removeAdd, int bookClubId, BuildContext context) async {
    addRemoveOpeningLoading = true;
    notifyListeners();
    await Provider.of<BookClubController>(context, listen: false)
        .addRemoveOpening(removeAdd, bookClubId, context)
        .then((value) async {
      isSuccess = value;
      apiResponse = _bookClubController?.apiResponse ?? '';
      addRemoveOpeningLoading = false;
      await getBookClubMembers(bookClubId, context);
      notifyListeners();
    });
  }

  Future<void> assignLeader(
    int bookClubId,
    int? selectedUserId,
    int initiatedUserId,
    int? selectedMemberId,
    BuildContext context,
  ) async {
    final payload = RequestManage(
      bookClubId: bookClubId,
      bookClubMemberId: selectedMemberId,
      userId: selectedUserId,
      userType: ClubMemberType.leader.value,
      initiatedBy: initiatedUserId,
      status: ClubMembershipStatus.active.value,
    );

    await Provider.of<BookClubController>(context, listen: false)
        .updateInvitation(payload, context)
        .then((value) async {
      await getBookClubMembers(bookClubId, context);
    });
  }
}
