import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/views/clubs/user_club_details/providers/user_club_details_provider.dart';
import 'package:eljunto/views/clubs/user_club_details/widgets/member_card.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../models/club_membership_model.dart';

/// Widget for displaying club members section
class ClubMembersSection extends StatelessWidget {
  final int bookClubId;

  const ClubMembersSection({
    super.key,
    required this.bookClubId,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<UserClubDetailsProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader(provider),
            const SizedBox(height: 10),
            _buildMembersList(context, provider),
          ],
        );
      },
    );
  }

  /// Build section header with member count and openings
  Widget _buildSectionHeader(UserClubDetailsProvider provider) {
    final memberList = provider.memberList;
    final totalVacancies =
        memberList?.isNotEmpty == true ? memberList![0].totalVacancies : null;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            "Members",
            style: lbRegular.copyWith(
              fontSize: 20,
              color: AppConstants.primaryColor,
            ),
          ),
          const Spacer(),
          Text(
            (totalVacancies != null && totalVacancies > 0)
                ? "($totalVacancies Club Openings)"
                : "(No Openings)",
            style: lbRegular.copyWith(
              fontSize: 14,
              color: AppConstants.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Build horizontal scrollable members list
  Widget _buildMembersList(
      BuildContext context, UserClubDetailsProvider provider) {
    final memberList = provider.memberList;

    return SizedBox(
      height: 112,
      child: Skeleton.replace(
        replacement: ListView.builder(
          padding: const EdgeInsets.only(left: 10, right: 20),
          scrollDirection: Axis.horizontal,
          itemCount: 3, // Show 3 skeleton items
          itemBuilder: (context, index) => _buildSkeletonMemberCard(),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.only(left: 10, right: 20),
          scrollDirection: Axis.horizontal,
          itemCount: memberList?.length,
          itemBuilder: (context, index) {
            return MemberCard(
              member: memberList?[index] ?? ClubMembershipModel(),
              memberIndex: index,
              bookClubId: bookClubId,
              onTap: () => _handleMemberTap(context, provider, index),
              onLeaveClub: () => _handleLeaveClub(context, provider, index),
            );
          },
        ),
      ),
    );
  }

  /// Handle member card tap
  void _handleMemberTap(
      BuildContext context, UserClubDetailsProvider provider, int index) {
    final member = provider.memberList?[index];
    if (member == null) return;

    if (provider.loggedInUserId == member.userId) {
      // Navigate to own profile if needed
      // context.pushNamed('ProfileScreen');
    } else {
      context.pushNamed('club-member-profile', extra: {
        'userId': member.userId,
        'userName': member.userName,
      });
    }
  }

  /// Handle leave club action
  void _handleLeaveClub(
      BuildContext context, UserClubDetailsProvider provider, int index) {
    _showLeaveClubDialog(context, provider, index);
  }

  /// Show leave club confirmation dialog
  void _showLeaveClubDialog(
      BuildContext context, UserClubDetailsProvider provider, int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () => dialogContext.pop(),
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "Leave Club",
                      textAlign: TextAlign.center,
                      style: lbBold.copyWith(fontSize: 18),
                    ),
                  ),
                ),
                const SizedBox(height: 28),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Text(
                    "Are you sure you would like to leave this club?",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(fontSize: 12),
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NetworkAwareTap(
                        onTap: () async {
                          dialogContext.pop();
                          final success = await provider.removeMember(
                            bookClubId,
                            index,
                            context,
                          );
                          if (success && context.mounted) {
                            _showLeaveSuccessDialog(context);
                          }
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Leave",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(fontSize: 18),
                            ),
                          ),
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () => Navigator.of(dialogContext).pop(),
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.backgroundColor,
                            border: Border.all(
                              color: AppConstants.primaryColor,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              "Cancel",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(fontSize: 18),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 30),
              ],
            )
          ],
        );
      },
    );
  }

  /// Show leave club success dialog
  void _showLeaveSuccessDialog(BuildContext context) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    dialogContext.pop();
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "Leave Club",
                      textAlign: TextAlign.center,
                      style: lbBold.copyWith(fontSize: 18),
                    ),
                  ),
                ),
                const SizedBox(height: 28),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Text(
                    "You have left the club",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(fontSize: 12),
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      NetworkAwareTap(
                        onTap: () {
                          final messageController =
                              context.read<MessageController>();
                          if (messageController.isFromChat) {
                            context.pop();
                            context.pushReplacementNamed('Message');
                            messageController.setIsFromChat(false);
                          } else {
                            dialogContext.pop();
                            context.pop();
                          }
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3.5,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Ok",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(fontSize: 18),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 30),
              ],
            )
          ],
        );
      },
    );
  }

  /// Build skeleton member card for loading state
  Widget _buildSkeletonMemberCard() {
    return Padding(
      padding: const EdgeInsets.only(left: 10.0),
      child: Container(
        width: 200,
        decoration: BoxDecoration(
          color: AppConstants.skeletonBackgroundColor,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: AppConstants.primaryColor,
            width: 1.5,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(14.0),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: AppConstants.skeletonforgroundColor,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(49),
                  child: Image.asset(
                    AppConstants.profileLogoImagePath,
                    width: 45,
                    height: 45,
                  ),
                ),
              ),
              SizedBox(height: 10),
              Text("Loading........"),
            ],
          ),
        ),
      ),
    );
  }
}
