import 'dart:developer';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/helpers/datetime_helper.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/models/home_model/home_screen2_model/meeting_model.dart';
import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/views/clubs/user_club_details/providers/user_club_details_provider.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

/// Individual meeting card widget for both upcoming and previous meetings
class MeetingCard extends StatelessWidget {
  final MeetingList meeting;
  final int meetingIndex;
  final int bookClubId;
  final bool isUpcoming;
  final bool isSkeletonShow;

  const MeetingCard({
    super.key,
    required this.meeting,
    required this.meetingIndex,
    required this.bookClubId,
    required this.isUpcoming,
    required this.isSkeletonShow,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<UserClubDetailsProvider>(
      builder: (context, provider, child) {
        final meetingTime = DateTimeHelper.getMeetingScheduleTime(
          meeting.meetingStartTime ?? 0,
          meeting.meetingEndTime ?? 0,
        );

        final formattedDate = DateTimeHelper.getDayMonthYearDateFormat(
          meeting.meetingDate,
        );

        return Container(
          margin: const EdgeInsets.only(left: 10),
          width: 250,
          decoration: BoxDecoration(
            color: isSkeletonShow
                ? AppConstants.skeletonBackgroundColor
                : Colors.transparent,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: AppConstants.primaryColor,
              width: 1.5,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(14),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                _buildBookTitle(),
                const SizedBox(height: 5),
                _buildBookAuthor(),
                const SizedBox(height: 5),
                _buildBookCoverage(),
                const SizedBox(height: 15),
                _buildMeetingDate(formattedDate),
                const SizedBox(height: 5),
                _buildMeetingTime(meetingTime),
                const SizedBox(height: 15),
                _buildDiscussionQuestions(context, provider),
                if (isUpcoming) ...[
                  const SizedBox(height: 15),
                  _buildJoinButton(context, provider, meetingTime),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  /// Build book title
  Widget _buildBookTitle() {
    return MarqueeList(
      children: [
        Text(
          meeting.bookName ?? '',
          style: lbBold.copyWith(
            fontSize: 18,
            color: AppConstants.primaryColor,
          ),
        ),
      ],
    );
  }

  /// Build book author
  Widget _buildBookAuthor() {
    return MarqueeList(
      children: [
        Text(
          meeting.bookAuthor ?? '',
          textAlign: TextAlign.start,
          overflow: TextOverflow.ellipsis,
          style: lbRegular.copyWith(
            fontSize: 14,
            color: AppConstants.primaryColor,
          ),
        ),
      ],
    );
  }

  /// Build book coverage
  Widget _buildBookCoverage() {
    return Text(
      meeting.partOfBookCovered ?? (isUpcoming ? 'TBD' : ''),
      textAlign: TextAlign.start,
      overflow: TextOverflow.ellipsis,
      style: lbRegular.copyWith(
        fontSize: 14,
        color: AppConstants.primaryColor,
      ),
    );
  }

  /// Build meeting date
  Widget _buildMeetingDate(String formattedDate) {
    return Text(
      meeting.meetingDate != null ? formattedDate : 'TBD',
      textAlign: TextAlign.center,
      overflow: TextOverflow.ellipsis,
      style: lbItalic.copyWith(
        fontSize: 12,
        color: AppConstants.primaryColor,
      ),
    );
  }

  /// Build meeting time
  Widget _buildMeetingTime(String meetingTime) {
    return Text(
      meetingTime,
      textAlign: TextAlign.center,
      overflow: TextOverflow.ellipsis,
      style: lbItalic.copyWith(
        fontSize: 12,
        color: AppConstants.primaryColor,
      ),
    );
  }

  /// Build discussion questions section
  Widget _buildDiscussionQuestions(
      BuildContext context, UserClubDetailsProvider provider) {
    final hasDiscussionQuestions =
        meeting.discussionQuestions?.isNotEmpty ?? false;

    if (hasDiscussionQuestions) {
      return GestureDetector(
        onTap: () {
          context.pushNamed(
            'discussionScreen',
            extra: {
              'clubName': provider.bookClubModel?.bookClubName ?? '',
              'discussionQue': meeting.discussionQuestions,
              'userName': provider.userName,
            },
          );
        },
        child: Text(
          'Discussion Questions',
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          style: lbItalic.copyWith(
            decoration: TextDecoration.underline,
            fontSize: 12,
            color: AppConstants.primaryColor,
          ),
        ),
      );
    } else {
      return Text(
        'TBD',
        textAlign: TextAlign.center,
        overflow: TextOverflow.ellipsis,
        style: lbRegular.copyWith(
          fontSize: 12,
          color: AppConstants.primaryColor,
        ),
      );
    }
  }

  /// Build join meeting button (only for upcoming meetings)
  Widget _buildJoinButton(BuildContext context,
      UserClubDetailsProvider provider, String meetingTime) {
    final showJoinButton = _shouldShowJoinButton();

    if (showJoinButton && meetingTime != "TBD") {
      return CustomLoaderButton(
        buttonWidth: provider.isMeetingLoading(meeting.meetingId ?? 0)
            ? 45.0
            : MediaQuery.of(context).size.width,
        buttonRadius: 30.0,
        buttonChild: provider.isMeetingLoading(meeting.meetingId ?? 0)
            ? const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation(Colors.white),
                strokeWidth: 3.0,
              )
            : Text(
                'Join Now',
                style: lbBold.copyWith(
                  fontSize: 18,
                  color: AppConstants.primaryColor,
                ),
              ),
        buttonPressed: () => _handleJoinMeeting(context, provider),
      );
    } else {
      return Container(
        width: 222,
        padding: const EdgeInsets.symmetric(vertical: 10.5, horizontal: 20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(49),
          color: AppConstants.isActiveRequestColor,
        ),
        child: Center(
          child: MarqueeList(
            children: [
              Text(
                'Join Now (Available 30 mins before start)',
                overflow: TextOverflow.ellipsis,
                style: lbBold.copyWith(
                  fontSize: 16,
                  color: Colors.black38,
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  /// Check if join button should be shown
  bool _shouldShowJoinButton() {
    if (meeting.meetingStartTime == null) return false;

    final startTime =
        DateTime.fromMillisecondsSinceEpoch(meeting.meetingStartTime!);
    final difference = startTime.difference(DateTime.now());

    // Show join button if meeting is within 30 minutes or already started (but not ended)
    return (difference <= const Duration(minutes: 30) &&
            !difference.isNegative) ||
        difference.isNegative;
  }

  /// Handle join meeting action
  void _handleJoinMeeting(
      BuildContext context, UserClubDetailsProvider provider) async {
    final meetingId = meeting.meetingId;
    final channelName = meeting.channelName;

    if (meetingId == null || channelName == null) {
      log("Missing meeting ID or channel name");
      return;
    }

    log("Meeting ID: $meetingId");
    log("Channel Name: $channelName");

    final success = await provider.joinMeeting(meetingId, channelName);

    if (success && context.mounted) {
      final profilePictureUrl = _getProfilePictureUrl(provider);

      context.pushNamed(
        'MeetingScreen',
        extra: {
          'bookClubId': meeting.bookClubId,
          'bookName': meeting.bookName ?? '',
          'token': provider.meetingJoinModel?.data,
          'userId': provider.loggedInUserId,
          'discussionQue': meeting.discussionQuestions,
          'channelName': channelName,
          'userName': provider.userHandle ?? '',
          'profilePictureUrl': profilePictureUrl,
        },
      );
    }
  }

  /// Get current user's profile picture URL
  String _getProfilePictureUrl(UserClubDetailsProvider provider) {
    try {
      final currentUserMember = provider.memberList?.firstWhere(
        (e) => e.userId == provider.loggedInUserId,
      );

      final profilePicture = currentUserMember?.userProfilePicture ?? '';
      return '${ApiConstants.imageBaseUrl}$profilePicture';
    } catch (e) {
      return AppConstants.profileLogoImagePath;
    }
  }
}
