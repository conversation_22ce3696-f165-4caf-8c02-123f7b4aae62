import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/models/club_membership_model.dart';
import 'package:eljunto/reusableWidgets/cached_network_image.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/views/clubs/user_club_details/providers/user_club_details_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

/// Individual member card widget
class MemberCard extends StatelessWidget {
  final ClubMembershipModel member;
  final int memberIndex;
  final int bookClubId;
  final VoidCallback onTap;
  final VoidCallback onLeaveClub;

  const MemberCard({
    super.key,
    required this.member,
    required this.memberIndex,
    required this.bookClubId,
    required this.onTap,
    required this.onLeaveClub,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<UserClubDetailsProvider>(
      builder: (context, provider, child) {
        final memberProfilePicture = _getMemberProfilePictureUrl();
        final isLeader = member.userType == ClubMemberType.leader.value;
        final isCurrentUser = provider.loggedInUserId == member.userId;
        final isClubLeader = provider.isCurrentUserLeader();

        return NetworkAwareTap(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.only(left: 10.0),
            child: Container(
              width: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: AppConstants.primaryColor,
                  width: 1.5,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.only(
                  left: 14.0,
                  top: 14,
                  bottom: 14,
                  right: 14,
                ),
                child: Column(
                  children: [
                    _buildMemberHeader(
                      memberProfilePicture,
                      isLeader,
                      isCurrentUser,
                      isClubLeader,
                    ),
                    const SizedBox(height: 10),
                    _buildMemberName(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build member header with profile picture and indicators
  Widget _buildMemberHeader(
    String memberProfilePicture,
    bool isLeader,
    bool isCurrentUser,
    bool isClubLeader,
  ) {
    return Stack(
      children: [
        // Leader star indicator
        if (isLeader)
          Align(
            alignment: Alignment.centerLeft,
            child: Image.asset(
              AppConstants.leaderStar,
              height: 43,
              width: 43,
              fit: BoxFit.cover,
              filterQuality: FilterQuality.high,
            ),
          ),

        // Leave club option for current user (if not leader)
        if (!isLeader && !isClubLeader && isCurrentUser)
          Align(
            alignment: Alignment.centerLeft,
            child: NetworkAwareTap(
              onTap: onLeaveClub,
              child: Align(
                alignment: Alignment.topLeft,
                child: Text(
                  'Leave Club',
                  style: lbItalic.copyWith(
                    fontSize: 10,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ),
          ),

        // Profile picture
        Align(
          alignment: Alignment.center,
          child: CustomCachedNetworkImage(
            imageUrl: memberProfilePicture,
            width: 45,
            height: 45,
            errorImage: AppConstants.profileLogoImagePath,
          ),
        ),
      ],
    );
  }

  /// Build member name
  Widget _buildMemberName() {
    return Text(
      member.userName ?? '',
      textAlign: TextAlign.center,
      overflow: TextOverflow.ellipsis,
      style: lbBold.copyWith(
        fontSize: 18,
        color: AppConstants.primaryColor,
      ),
    );
  }

  /// Get member profile picture URL
  String _getMemberProfilePictureUrl() {
    if (member.userProfilePicture != null) {
      return '${ApiConstants.imageBaseUrl}${member.userProfilePicture!}';
    }
    return AppConstants.profileLogoImagePath;
  }
}
