import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../reusableWidgets/profile_appbar.dart';

class BookReview extends StatefulWidget {
  final String? userName;
  final String? userHandle;
  final bool? userClubInvitation;
  final String? bookName;
  final String? bookAuthor;
  final double? ratings;
  final String? review;
  final String? userProfilePicture;
  final bool? userOwnProfile;

  const BookReview({
    super.key,
    this.userName,
    this.userHandle,
    this.userClubInvitation,
    this.bookName,
    this.bookAuthor,
    this.ratings,
    this.review,
    this.userProfilePicture,
    this.userOwnProfile,
  });

  @override
  State<BookReview> createState() => _BookReviewState();
}

class _BookReviewState extends State<BookReview> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: Color.fromRGBO(37, 57, 67, 1),
              ),
            ),
          ),
          child: ProfileAppBar(
            userName: widget.userName,
            userHandle: widget.userHandle,
            isOpenToClubInvitation: widget.userClubInvitation ?? false,
            userOwnProfile: true,
            userProfilePicture: widget.userProfilePicture,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Text(
                  "Review:",
                  textAlign: TextAlign.center,
                  style: GoogleFonts.libreBaskerville(
                    fontWeight: FontWeight.w400,
                    fontSize: 20,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Text(
                  widget.bookName ?? '',
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                  style: GoogleFonts.libreBaskerville(
                    fontWeight: FontWeight.w700,
                    fontSize: 20,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
              const SizedBox(
                height: 5,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Row(
                  children: [
                    Text(
                      widget.bookAuthor ?? '',
                      textAlign: TextAlign.center,
                      style: GoogleFonts.libreBaskerville(
                        fontWeight: FontWeight.w700,
                        fontSize: 14,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    const Spacer(),
                    RatingBar(
                      ignoreGestures: true,
                      itemCount: 5,
                      itemSize: 25,
                      allowHalfRating: true,
                      initialRating: widget.ratings ?? 0,
                      minRating: 0,
                      ratingWidget: RatingWidget(
                        full: const Icon(
                          Icons.star,
                          color: AppConstants.textGreenColor,
                        ),
                        half: const Icon(
                          Icons.star_half,
                          color: AppConstants.textGreenColor,
                        ),
                        empty: const Icon(
                          Icons.star_border_outlined,
                          color: AppConstants.textGreenColor,
                        ),
                      ),
                      onRatingUpdate: (double value) {},
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Text(
                  //"Heart. Grit. Friendships. My favorite book. I listen to it every year on my audio cassette.",
                  widget.review ?? '',
                  textAlign: TextAlign.start,
                  style: GoogleFonts.libreBaskerville(
                    fontWeight: FontWeight.w700,
                    fontSize: 14,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Go Back",
                        textAlign: TextAlign.center,
                        style: GoogleFonts.libreBaskerville(
                          fontWeight: FontWeight.w700,
                          fontSize: 18,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(
                height: 25,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
