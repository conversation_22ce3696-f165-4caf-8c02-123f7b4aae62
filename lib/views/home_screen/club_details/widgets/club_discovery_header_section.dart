import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/reusableWidgets/club_join_popup.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/views/home_screen/club_details/providers/club_details_provider.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

/// Widget for displaying club discovery header with charter and join buttons
class ClubDiscoveryHeaderSection extends StatelessWidget {
  final int bookClubId;
  final String bookClubName;

  const ClubDiscoveryHeaderSection({
    super.key,
    required this.bookClubId,
    required this.bookClubName,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ClubDetailsProvider>(
      builder: (context, provider, child) {
        final bookClubName = provider.bookClubModel?.bookClubName ?? '';
        final clubCharter = provider.bookClubModel?.clubCharter ?? '';
        final hasVacancies = provider.hasVacancies;

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildCharterButton(context, bookClubName, clubCharter),
              _buildJoinButton(context, provider, hasVacancies),
            ],
          ),
        );
      },
    );
  }

  /// Build club charter button
  Widget _buildCharterButton(
    BuildContext context,
    String bookClubName,
    String clubCharter,
  ) {
    return NetworkAwareTap(
      onTap: () {
        context.pushNamed('club-charter', extra: {
          'bookClubName': bookClubName,
          'clubCharter': clubCharter,
        });
      },
      child: Container(
        height: 45,
        width: MediaQuery.of(context).size.width / 2.4,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(49),
          color: AppConstants.textGreenColor,
        ),
        child: Center(
          child: Text(
            "Club Charter",
            textAlign: TextAlign.center,
            style: lbBold.copyWith(
              fontSize: 18,
              color: AppConstants.primaryColor,
            ),
          ),
        ),
      ),
    );
  }

  /// Build join/no openings button
  Widget _buildJoinButton(
    BuildContext context,
    ClubDetailsProvider provider,
    bool hasVacancies,
  ) {
    if (hasVacancies) {
      return NetworkAwareTap(
        onTap: () {
          _showJoinClubPopup(
            context,
            bookClubId,
            bookClubName,
            provider.bookClubModel?.memberReqPrompt ?? '',
          );
        },
        child: Container(
          height: 45,
          width: MediaQuery.of(context).size.width / 2.4,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(49),
            color: AppConstants.textGreenColor,
          ),
          child: Center(
            child: Text(
              "Join",
              textAlign: TextAlign.center,
              style: lbBold.copyWith(
                fontSize: 18,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
        ),
      );
    } else {
      return Container(
        height: 45,
        width: MediaQuery.of(context).size.width / 2.5,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(49),
          color: Colors.transparent,
          border: Border.all(
            color: const Color.fromRGBO(45, 45, 45, 1),
          ),
        ),
        child: Center(
          child: Text(
            "No openings",
            textAlign: TextAlign.center,
            style: lbBold.copyWith(
              fontSize: 18,
              color: AppConstants.primaryColor,
            ),
          ),
        ),
      );
    }
  }

  /// Show join club popup dialog
  void _showJoinClubPopup(
    BuildContext context,
    int bookClubId,
    String bookClubName,
    String requestPrompt,
  ) {
    final provider = Provider.of<ClubDetailsProvider>(context, listen: false);
    provider.clearValidationMessage();

    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return ClubJoinPopup(
              bookClubId: bookClubId,
              bookClubName: bookClubName,
              memberRequestPrompt: requestPrompt,
            );
          },
        );
      },
    );
  }
}
