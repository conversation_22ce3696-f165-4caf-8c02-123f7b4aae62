import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../models/home_model/home_screen2_model/previous_meeting_model.dart';
import '../../models/home_model/home_screen2_model/upcoming_meeting_model.dart';
import '../../models/home_model/home_screen6_model/member_model.dart';

class HomeScreen6 extends StatefulWidget {
  final String? clubName;
  const HomeScreen6({
    super.key,
    this.clubName,
  });

  @override
  State<HomeScreen6> createState() => _HomeScreen6State();
}

class _HomeScreen6State extends State<HomeScreen6> {
  List<MemberDetailsModel> memberDetailsList = [
    MemberDetailsModel(
        leaderLogo: 'assets/icons/Leader_Transparent_Background.png',
        name: "<PERSON>",
        profile: 'assets/icons/Profile_2.png'),
    MemberDetailsModel(
      leaderLogo: 'assets/icons/Leader_Transparent_Background.png',
      name: "<PERSON>",
      profile: 'assets/icons/Profile_2.png',
    ),
    MemberDetailsModel(
      leaderLogo: 'assets/icons/Leader_Transparent_Background.png',
      name: "Karen O’Connor",
      profile: 'assets/icons/Profile_2.png',
    ),
  ];

  List<UpComingMeetingModel> upComingList = [
    // UpComingMeetingModel(
    //   schedule: "Middlemarch",
    //   name: "George Elliot",
    //   books: "Books 1-4",
    //   date: "Mon Nov 13, 2023",
    //   time: "8PM-10PM",
    // ),
    // UpComingMeetingModel(
    //   schedule: "Middlemarch",
    //   name: "George Elliot",
    //   books: "Books 1-4",
    //   date: "Mon Nov 13, 2023",
    //   time: "8PM-10PM",
    // ),
  ];

  List<PreviousMeetingModel> previousList = [
    PreviousMeetingModel(
      bookName: "Moby Dick",
      author: "Herman Melville",
      book: "Chapters 1-70",
      date: "July 2023",
    ),
    PreviousMeetingModel(
      bookName: "Moby Dick",
      author: "Herman Melville",
      book: "Chapters 71-135",
      date: "May 2023",
    ),
    // PreviousMeetingModel(
    //   bookName: "Personal Memoirs of US Grant",
    //   author: "US Grant",
    //   book: "Whole Book",
    //   date: "July 2022",
    // ),
    // PreviousMeetingModel(
    //   bookName: "Of Time and the River",
    //   author: "Thomas Wolfe",
    //   book: "",
    //   date: "Apr 2022",
    // ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: Color.fromRGBO(37, 57, 67, 1),
              ),
            ),
          ),
          child: PreviousScreenAppBar(
            bookName: widget.clubName,
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        // context.pushNamed('HomeScreen3',
                        //     extra: widget.bookName ?? '');
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 2.5,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(49),
                          color: AppConstants.textGreenColor,
                        ),
                        child: Center(
                          child: Text(
                            "Club charter",
                            textAlign: TextAlign.center,
                            style: GoogleFonts.libreBaskerville(
                              fontSize: 18,
                              fontWeight: FontWeight.w700,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                    NetworkAwareTap(
                      onTap: () {
                        // joinrequestFunction();
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 2.5,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(49),
                          color: Colors.transparent,
                          border: Border.all(
                            color: const Color.fromRGBO(45, 45, 45, 1),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            "No openings",
                            textAlign: TextAlign.center,
                            style: GoogleFonts.libreBaskerville(
                              fontSize: 18,
                              fontWeight: FontWeight.w700,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Text(
                  "Members",
                  textAlign: TextAlign.center,
                  style: GoogleFonts.libreBaskerville(
                    fontSize: 20,
                    fontWeight: FontWeight.w400,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              SizedBox(
                height: 107,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.only(left: 10, right: 20),
                  itemCount: memberDetailsList.length,
                  itemBuilder: (context, index) {
                    return NetworkAwareTap(
                      onTap: () {},
                      child: Container(
                        margin: const EdgeInsets.only(left: 10),
                        width: 200,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: AppConstants.primaryColor,
                            width: 1.5,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(
                              top: 9.0, bottom: 9, left: 14),
                          child: Column(
                            children: [
                              Stack(
                                // mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Align(
                                    alignment: Alignment.centerLeft,
                                    child: Visibility(
                                      visible: index == 0,
                                      child: Image.asset(
                                        memberDetailsList[index].leaderLogo ??
                                            '',
                                        height: 40,
                                        width: 40,
                                        fit: BoxFit.cover,
                                        filterQuality: FilterQuality.high,
                                      ),
                                    ),
                                  ),
                                  // const SizedBox(
                                  //   width: 21,
                                  // ),
                                  Align(
                                    alignment: Alignment.center,
                                    child: Image.asset(
                                      memberDetailsList[index].profile ?? '',
                                      height: 50,
                                      width: 50,
                                      fit: BoxFit.cover,
                                      filterQuality: FilterQuality.high,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Text(
                                memberDetailsList[index].name ?? '',
                                textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                                style: GoogleFonts.libreBaskerville(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w700,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              const Divider(
                thickness: 2,
                color: Color.fromRGBO(37, 57, 67, 1),
              ),
              const SizedBox(
                height: 25,
              ),
              upComingList.isNotEmpty
                  ? Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 25.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            "Upcoming Meetings",
                            textAlign: TextAlign.center,
                            style: GoogleFonts.libreBaskerville(
                              fontSize: 20,
                              fontWeight: FontWeight.w400,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    )
                  : const SizedBox.shrink(),
              upComingList.isNotEmpty
                  ? const SizedBox(
                      height: 10,
                    )
                  : const SizedBox.shrink(),
              upComingList.isNotEmpty
                  ? SizedBox(
                      height: 167,
                      child: ListView.builder(
                        padding: const EdgeInsets.only(left: 10, right: 20),
                        scrollDirection: Axis.horizontal,
                        itemCount: upComingList.length,
                        itemBuilder: (context, index) {
                          return NetworkAwareTap(
                            onTap: () {},
                            child: Container(
                              margin: const EdgeInsets.only(left: 10),
                              width: 250,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: AppConstants.primaryColor,
                                  width: 1.5,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(14.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      upComingList[index].schedule ?? '',
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.libreBaskerville(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w700,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 5,
                                    ),
                                    Text(
                                      upComingList[index].name ?? '',
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.libreBaskerville(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 5,
                                    ),
                                    Text(
                                      upComingList[index].books ?? '',
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.libreBaskerville(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 21,
                                    ),
                                    Text(
                                      upComingList[index].date ?? '',
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.libreBaskerville(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w400,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 5,
                                    ),
                                    Text(
                                      upComingList[index].time ?? '',
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.libreBaskerville(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w400,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    )
                  : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20.0),
                      child: Container(
                        height: 50,
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: AppConstants.primaryColor,
                            width: 1.5,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            "No Upcoming  Meetings",
                            textAlign: TextAlign.center,
                            style: GoogleFonts.libreBaskerville(
                              fontSize: 18,
                              fontWeight: FontWeight.w700,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ),
                      ),
                    ),
              const SizedBox(
                height: 25,
              ),
              const Divider(
                thickness: 2,
                color: Color.fromRGBO(37, 57, 67, 1),
              ),
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Text(
                  "Previous Meetings",
                  textAlign: TextAlign.center,
                  style: GoogleFonts.libreBaskerville(
                    fontSize: 20,
                    fontWeight: FontWeight.w400,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              SizedBox(
                height: 145,
                child: ListView.builder(
                  padding: const EdgeInsets.only(left: 10, right: 20),
                  scrollDirection: Axis.horizontal,
                  itemCount: previousList.length,
                  itemBuilder: (context, index) {
                    return NetworkAwareTap(
                      onTap: () {
                        // context.goNamed('HomeScreen2',
                        //     extra: clubList[index].bookName ?? '');
                      },
                      child: Container(
                        margin: const EdgeInsets.only(left: 10),
                        width: 250,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: AppConstants.primaryColor,
                            width: 1.5,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(14.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                previousList[index].bookName ?? '',
                                textAlign: TextAlign.start,
                                overflow: TextOverflow.ellipsis,
                                style: GoogleFonts.libreBaskerville(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w700,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              const SizedBox(
                                height: 5,
                              ),
                              Text(
                                previousList[index].author ?? '',
                                textAlign: TextAlign.center,
                                style: GoogleFonts.libreBaskerville(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              const SizedBox(
                                height: 5,
                              ),
                              Text(
                                previousList[index].book ?? '',
                                textAlign: TextAlign.center,
                                style: GoogleFonts.libreBaskerville(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              const SizedBox(
                                height: 21,
                              ),
                              Text(
                                previousList[index].date ?? '',
                                textAlign: TextAlign.center,
                                style: GoogleFonts.libreBaskerville(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              // const SizedBox(
              //   height: 25,
              // ),
            ],
          ),
        ),
      ),
    );
  }
}
