import 'dart:developer';

import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:eljunto/reusableWidgets/contact_us.dart';
import 'package:eljunto/reusableWidgets/version_display.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../reusableWidgets/custom_text_widget.dart';
import '../../app/core/constants.dart';
import '../../app/core/utils/text_style.dart';
import '../../reusableWidgets/custom_button.dart';
import 'provider/auth_provider.dart';

class NameAndHandlePage extends StatefulWidget {
  final String email;
  final String token;
  final String otp;

  const NameAndHandlePage({
    super.key,
    required this.email,
    required this.token,
    required this.otp,
  });

  @override
  State createState() => _NameAndHandlePageState();
}

class _NameAndHandlePageState extends State<NameAndHandlePage> {
  final _nameFormKey = GlobalKey<FormState>();
  final _handleFormKey = GlobalKey<FormState>();

  final TextEditingController nameController = TextEditingController();
  final TextEditingController locationController = TextEditingController();
  final TextEditingController bioController = TextEditingController();
  final TextEditingController handleController = TextEditingController();
  String? appName, appVersion;
  late AuthProvider provider;

  @override
  void initState() {
    provider = Provider.of<AuthProvider>(context, listen: false);
    provider.initialize(context);
    super.initState();
  }

  @override
  void dispose() {
    nameController.dispose();
    locationController.dispose();
    bioController.dispose();
    handleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: PopScope(
          canPop: false,
          child: Stack(
            children: [
              Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  toolbarHeight: 140,
                  shape: const Border(
                    bottom: BorderSide(
                      color: AppConstants.primaryColor,
                      width: 1.5,
                    ),
                  ),
                  automaticallyImplyLeading: false,
                  backgroundColor: AppConstants.textGreenColor,
                  centerTitle: true,
                  title: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // const SizedBox(
                      //   height: 20,
                      // ),
                      Image.asset(
                        AppConstants.elJuntoLogo,
                        height: 100,
                        width: 80,
                        filterQuality: FilterQuality.high,
                        fit: BoxFit.contain,
                      ),
                      // SizedBox(height: 8),
                      const VersionDisplay(),
                      const SizedBox(height: 25),
                    ],
                  ),
                ),
                body: Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                  return ListView(
                    //crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      const SizedBox(
                        height: 25,
                      ),
                      Text(
                        'Create New Account',
                        style: lbRegular.copyWith(
                          fontSize: 24,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      if (!authProvider.isNameSet) ...[
                        Form(
                          key: _nameFormKey,
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 19.0),
                            child: Column(
                              children: [
                                const SizedBox(
                                  height: 25,
                                ),

                                textWidget("Name"),
                                const SizedBox(
                                  height: 10,
                                ),
                                Stack(
                                  clipBehavior: Clip.none,
                                  children: [
                                    TextFormField(
                                      controller: nameController,
                                      keyboardType: TextInputType.text,
                                      textCapitalization:
                                          TextCapitalization.sentences,
                                      style: lbRegular.copyWith(
                                        fontSize: 18,
                                      ),
                                      maxLength: 50,
                                      decoration: InputDecoration(
                                        counterStyle: lbRegular.copyWith(
                                          fontSize: 14,
                                        ),
                                        contentPadding:
                                            const EdgeInsets.all(10),
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(5.0),
                                          borderSide: const BorderSide(
                                              color: AppConstants.primaryColor,
                                              width: 2.5),
                                        ),
                                        filled: true,
                                        fillColor:
                                            Colors.white.withValues(alpha: 0.8),
                                        errorStyle: errorMsg,
                                      ),
                                      onChanged: (value) {
                                        if (value.trim().isEmpty) {
                                          authProvider.setNameFlag = true;
                                        } else {
                                          authProvider.setNameFlag = false;
                                        }
                                      },
                                      validator: (value) {
                                        if (value == null ||
                                            value.trim().isEmpty) {
                                          authProvider.setNameFlag = true;
                                          return null;
                                        } else {
                                          authProvider.setNameFlag = false;
                                          return null;
                                        }
                                      },
                                    ),
                                    Positioned(
                                      top: 55,
                                      left: 0,
                                      right: 0,
                                      child: authProvider.setNameFlag
                                          ? Text(
                                              "*Enter name",
                                              style: errorMsg,
                                            )
                                          : const SizedBox.shrink(),
                                    ),
                                  ],
                                ),

                                authProvider.setNameFlag
                                    ? const SizedBox(
                                        height: 25,
                                      )
                                    : const SizedBox.shrink(),
                                textWidget(
                                  "Location",
                                ),

                                const SizedBox(
                                  height: 10,
                                ),

                                /// TEXTFORMFIELD FOR LOCATION

                                Stack(
                                  clipBehavior: Clip.none,
                                  children: [
                                    TextFormField(
                                      controller: locationController,
                                      keyboardType: TextInputType.text,
                                      textCapitalization:
                                          TextCapitalization.sentences,
                                      style: lbRegular.copyWith(
                                        fontSize: 18,
                                      ),
                                      decoration: InputDecoration(
                                        suffixIcon: NetworkAwareTap(
                                          onTap: () {},
                                          child: Image.asset(
                                            'assets/icons/Edit.png',
                                            filterQuality: FilterQuality.high,
                                            fit: BoxFit.cover,
                                            height: 38,
                                            width: 38,
                                          ),
                                        ),
                                        counterStyle: lbRegular.copyWith(
                                          fontSize: 14,
                                        ),
                                        contentPadding:
                                            const EdgeInsets.all(10),
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(5.0),
                                          borderSide: const BorderSide(
                                              color: AppConstants.primaryColor,
                                              width: 2.5),
                                        ),
                                        filled: true,
                                        fillColor:
                                            Colors.white.withValues(alpha: 0.8),
                                        errorStyle: errorMsg,
                                      ),
                                      onChanged: (value) {
                                        if (value.trim().isEmpty) {
                                          authProvider.setLocationFlag = true;
                                        } else {
                                          authProvider.setLocationFlag = false;
                                        }
                                      },
                                      validator: (value) {
                                        if (value == null ||
                                            value.trim().isEmpty) {
                                          authProvider.setLocationFlag = true;
                                          return null;
                                        } else {
                                          authProvider.setLocationFlag = false;

                                          return null;
                                        }
                                      },
                                    ),
                                    Positioned(
                                      top: 55,
                                      left: 0,
                                      right: 0,
                                      child: authProvider.setLocationFlag
                                          ? Text(
                                              "*Enter location",
                                              style: errorMsg,
                                            )
                                          : const SizedBox.shrink(),
                                    ),
                                  ],
                                ),

                                authProvider.setLocationFlag
                                    ? const SizedBox(
                                        height: 50,
                                      )
                                    : const SizedBox(
                                        height: 25,
                                      ),
                                textWidget(
                                  "Bio",
                                ),
                                const SizedBox(
                                  height: 10,
                                ),

                                /// TEXTFORMFIELD FOR BIO

                                Stack(
                                  clipBehavior: Clip.none,
                                  children: [
                                    TextFormField(
                                      controller: bioController,
                                      keyboardType: TextInputType.text,
                                      textCapitalization:
                                          TextCapitalization.sentences,
                                      style: lbRegular.copyWith(
                                        fontSize: 18,
                                      ),
                                      maxLines: null,
                                      maxLength: 150,
                                      decoration: InputDecoration(
                                        suffixIcon: NetworkAwareTap(
                                          onTap: () {},
                                          child: Image.asset(
                                            'assets/icons/Edit.png',
                                            filterQuality: FilterQuality.high,
                                            fit: BoxFit.cover,
                                            height: 38,
                                            width: 38,
                                          ),
                                        ),
                                        counterStyle: lbRegular.copyWith(
                                          fontSize: 14,
                                        ),
                                        contentPadding:
                                            const EdgeInsets.all(10),
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(5.0),
                                          borderSide: const BorderSide(
                                              color: AppConstants.primaryColor,
                                              width: 2.5),
                                        ),
                                        filled: true,
                                        fillColor:
                                            Colors.white.withValues(alpha: 0.8),
                                        errorStyle: errorMsg,
                                      ),
                                      onChanged: (value) {
                                        if (value.trim().isEmpty) {
                                          authProvider.setBioFlag = true;
                                        } else {
                                          authProvider.setBioFlag = false;
                                        }
                                      },
                                      validator: (value) {
                                        if (value == null ||
                                            value.trim().isEmpty) {
                                          authProvider.setBioFlag = true;
                                          return null;
                                        } else {
                                          authProvider.setBioFlag = false;
                                          return null;
                                        }
                                      },
                                    ),
                                    Positioned(
                                      top: 55,
                                      left: 0,
                                      right: 0,
                                      child: authProvider.setBioFlag
                                          ? Text(
                                              "*Enter bio",
                                              style: errorMsg,
                                            )
                                          : const SizedBox.shrink(),
                                    ),
                                  ],
                                ),
                                authProvider.setBioFlag
                                    ? const SizedBox(
                                        height: 50,
                                      )
                                    : const SizedBox(
                                        height: 25,
                                      ),
                                CustomLoaderButton(
                                  // loginText: 'Login',
                                  buttonWidth: authProvider.nameLoading
                                      ? 45.0
                                      : MediaQuery.of(context).size.width,
                                  buttonRadius: 30.0,
                                  buttonChild: authProvider.nameLoading
                                      ? const CircularProgressIndicator(
                                          valueColor: AlwaysStoppedAnimation(
                                              Colors.white),
                                          strokeWidth: 3.0,
                                        )
                                      : Text(
                                          'Next',
                                          style: lbBold.copyWith(
                                            fontSize: 18,
                                            color: AppConstants.primaryColor,
                                          ),
                                        ),
                                  buttonPressed: () {
                                    authProvider.submitName(
                                      nameController.text.trim(),
                                      locationController.text.trim(),
                                      bioController.text.trim(),
                                      _nameFormKey,
                                    );
                                  },
                                ),
                                const SizedBox(height: 25),
                                const ContactUs(),
                              ],
                            ),
                          ),
                        ),
                      ],
                      if (authProvider.isNameSet) ...[
                        Form(
                          key: _handleFormKey,
                          child: Padding(
                            padding: const EdgeInsets.all(19.0),
                            child: Column(
                              children: [
                                const SizedBox(
                                  height: 25,
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: Text(
                                        "What is your @handle?: \n\nThis is a unique identifier meant to distinguish you from others with the  same name.\n\n Your @ handle is permanent so choose wisely!",
                                        style: lbRegular.copyWith(
                                          fontSize: 18,
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                                const SizedBox(
                                  height: 25,
                                ),
                                Stack(
                                  clipBehavior: Clip.none,
                                  children: [
                                    TextFormField(
                                      controller: handleController,
                                      style: lbRegular.copyWith(
                                        fontSize: 18,
                                      ),
                                      onChanged: (value) {
                                        log("Value HandleController : $value");
                                        if (value.trim().isNotEmpty) {
                                          authProvider.checkHandler(
                                              widget.email,
                                              value.trim(),
                                              context);
                                        } else {
                                          authProvider.isHandlerAvailable =
                                              false;
                                        }
                                      },
                                      keyboardType: TextInputType.text,
                                      textCapitalization:
                                          TextCapitalization.sentences,
                                      maxLength: 15,
                                      decoration: InputDecoration(
                                        prefix: Text(
                                          "@",
                                          style: lbRegular.copyWith(
                                            fontSize: 20,
                                            fontWeight: FontWeight.w400,
                                            color: const Color.fromRGBO(
                                                0, 0, 0, 1),
                                          ),
                                        ),
                                        counterStyle: lbRegular.copyWith(
                                          fontSize: 14,
                                          color: AppConstants.primaryColor,
                                        ),
                                        contentPadding:
                                            const EdgeInsets.all(10),
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(5.0),
                                          borderSide: const BorderSide(
                                            color: AppConstants.primaryColor,
                                            width: 2.5,
                                          ),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          borderSide: const BorderSide(
                                            color: AppConstants.primaryColor,
                                            width: 1.5,
                                          ),
                                        ),
                                        disabledBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          borderSide: const BorderSide(
                                            color: Colors.black38,
                                            width: 1.5,
                                          ),
                                        ),
                                        enabled: authProvider.isFieldEnabled,

                                        filled: true,
                                        fillColor:
                                            Colors.white.withValues(alpha: 0.8),
                                        errorStyle: errorMsg,
                                        // errorText: ,
                                      ),
                                      validator: (value) {
                                        if (value == null ||
                                            value.trim().isEmpty) {
                                          authProvider.handlerFlag = true;

                                          return null;
                                        }
                                        return null;
                                      },
                                    ),
                                    if (handleController.text.trim().isEmpty)
                                      Positioned(
                                        left: 0,
                                        right: 0,
                                        top: 55,
                                        child: authProvider.handlerFlag
                                            ? Text(
                                                authProvider.errorMessage,
                                                overflow: TextOverflow.clip,
                                                style: lbBold.copyWith(
                                                  fontSize: 14,
                                                  color: AppConstants.redColor,
                                                ),
                                              )
                                            : const SizedBox.shrink(),
                                      ),
                                    Positioned(
                                      left: 0,
                                      right: 0,
                                      top: 55,
                                      child: handleController.text
                                                  .trim()
                                                  .isNotEmpty &&
                                              !_isOnlySpaces()
                                          ? _buildCheckHandlerMessage()
                                          : const SizedBox.shrink(),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 25),
                                CustomLoaderButton(
                                  buttonWidth: authProvider.handleLoading
                                      ? 45.0
                                      : MediaQuery.of(context).size.width,
                                  buttonRadius: 30.0,
                                  buttonChild: authProvider.handleLoading
                                      ? const CircularProgressIndicator(
                                          valueColor: AlwaysStoppedAnimation(
                                              Colors.white),
                                          strokeWidth: 3.0,
                                        )
                                      : Text(
                                          'Next',
                                          style: lbBold.copyWith(
                                            fontSize: 18,
                                            color: AppConstants.primaryColor,
                                          ),
                                        ),
                                  buttonPressed: () {
                                    authProvider.submitHandle(
                                      nameController.text.trim(),
                                      locationController.text.trim(),
                                      bioController.text.trim(),
                                      handleController.text.trim(),
                                      widget.email,
                                      widget.token,
                                      widget.otp,
                                      _handleFormKey,
                                      context,
                                    );
                                  },
                                ),
                                const SizedBox(height: 25),
                                const ContactUs(),
                              ],
                            ),
                          ),
                        )
                      ]
                    ],
                  );
                }),
              ),
              NoConnectionTag(bottomPosition: 70),
            ],
          ),
        ),
      ),
    );
  }

  Widget textWidget(String title) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          "$title:",
          style: lbRegular.copyWith(
            fontSize: 18,
          ),
        ),
      ],
    );
  }

  Widget _buildCheckHandlerMessage() {
    return Consumer<AuthProvider>(builder: (context, authProvider, child) {
      return Column(
        children: authProvider.isHandlerAvailable
            ? [
                _buildHandlerMessage(
                    'Avaliable',
                    authProvider.isHandlerAvailable,
                    handleController.text.isNotEmpty)
              ]
            : [
                _buildHandlerMessage(
                    'Not available',
                    authProvider.isHandlerAvailable,
                    handleController.text.isNotEmpty)
              ],
      );
    });
  }

  Widget _buildHandlerMessage(String message, bool isValid, bool shouldShow) {
    if (!shouldShow) {
      return const SizedBox.shrink();
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          isValid ? Icons.check_circle : Icons.error,
          size: 20,
          color: isValid ? Colors.green : Colors.red,
        ),
        const SizedBox(width: 8.0),
        Expanded(
          child: Text(message, style: lbItalic),
        ),
      ],
    );
  }

  bool _isOnlySpaces() {
    return handleController.text.trim().isEmpty;
  }
}
