import 'dart:developer';

import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:eljunto/reusableWidgets/contact_us.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/reusableWidgets/version_display.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:pinput/pinput.dart';
import 'package:provider/provider.dart';

import '../../../reusableWidgets/custom_text_widget.dart';
import '../../app/core/constants.dart';
import '../../reusableWidgets/custom_button.dart';
import 'provider/auth_provider.dart';

class OTPPage extends StatefulWidget {
  final String email;
  final String token;
  final bool? isForgotPassword;

  const OTPPage({
    super.key,
    required this.email,
    required this.token,
    this.isForgotPassword,
  });

  @override
  State createState() => _OTPPageState();
}

class _OTPPageState extends State<OTPPage> {
  final otpController = TextEditingController();

  late AuthProvider provider;

  @override
  void initState() {
    provider = Provider.of<AuthProvider>(context, listen: false);
    provider.initialize(context);
    super.initState();
    provider.startTimer();
  }

  @override
  void dispose() {
    provider.disposeTimer();
    otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final defaultPinTheme = PinTheme(
      height: 45,
      width: 48.03,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        color: Colors.white,
        border: Border.all(
          color: Colors.black38,
          width: 1,
        ),
      ),
    );

    final focusedPinTheme = PinTheme(
      height: 45,
      width: 48.03,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        color: Colors.white,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1,
        ),
      ),
    );

    final submittedPinTheme = PinTheme(
      height: 45,
      width: 48.03,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        color: Colors.white,
        border: Border.all(
          color: Colors.black38,
          width: 1.5,
        ),
      ),
    );

    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            PopScope(
              onPopInvokedWithResult: (didPop, _) {
                if (didPop) {
                  provider.errorMessage = '';
                }
              },
              child: Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  toolbarHeight: 140,
                  shape: const Border(
                    bottom: BorderSide(
                      color: AppConstants.primaryColor,
                      width: 1.5,
                    ),
                  ),
                  automaticallyImplyLeading: false,
                  backgroundColor: AppConstants.textGreenColor,
                  centerTitle: true,
                  title: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        AppConstants.elJuntoLogo,
                        height: 100,
                        width: 80,
                        filterQuality: FilterQuality.high,
                        fit: BoxFit.contain,
                      ),
                      const VersionDisplay(),
                      const SizedBox(
                        height: 25,
                      ),
                    ],
                  ),
                ),
                body: Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                  return ListView(
                    children: <Widget>[
                      const SizedBox(
                        height: 25,
                      ),
                      Text(
                        widget.isForgotPassword ?? false
                            ? 'Forgot Password'
                            : 'Create New Account',
                        style: lbRegular.copyWith(
                          fontSize: 24,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 25),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Enter verification code sent to email:',
                            style: lbRegular.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),

                      /// OTP BOX

                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Pinput(
                          autofocus: true,
                          length: 6,
                          keyboardType: TextInputType.number,
                          showCursor: true,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          controller: otpController,
                          defaultPinTheme: defaultPinTheme,
                          focusedPinTheme: focusedPinTheme,
                          submittedPinTheme: submittedPinTheme,
                          enabled: authProvider.isFieldEnabled,
                        ),
                      ),
                      const SizedBox(height: 10),
                      // if (_showOTPValidation)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            authProvider.errorMessage.isNotEmpty
                                ? Expanded(
                                    child: MarqueeList(
                                      children: [
                                        Text(
                                          authProvider.errorMessage,
                                          overflow: TextOverflow.ellipsis,
                                          style: errorMsg,
                                        ),
                                      ],
                                    ),
                                  )
                                : const SizedBox.shrink(),
                            const SizedBox(width: 10),

                            // const Spacer(),
                            authProvider.isResendOtp
                                ? NetworkAwareTap(
                                    onTap: () async {
                                      await authProvider
                                          .reSendOtp(widget.email, context)
                                          .then((_) {
                                        resendOTP();
                                      });
                                    },
                                    child: Text(
                                      "Resend code",
                                      style: lbBold.copyWith(
                                        fontSize: 14,
                                        color: AppConstants.primaryColor,
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  )
                                : Row(
                                    children: [
                                      Text(
                                        "Resend code in ",
                                        style: libreBaskervilleRegular,
                                      ),
                                      ValueListenableBuilder(
                                          valueListenable:
                                              authProvider.timerCountdown,
                                          builder:
                                              (context, currentStart, child) {
                                            return Text(
                                              '${(currentStart ~/ 60).toString().padLeft(2, '0')}:${(currentStart % 60).toString().padLeft(2, '0')}',
                                              style: lbRegular.copyWith(
                                                fontSize: 14,
                                                color: AppConstants.redColor,
                                              ),
                                            );
                                          }),
                                    ],
                                  ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 25),

                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: CustomLoaderButton(
                          // loginText: 'Login',
                          buttonWidth: authProvider.otpLoading
                              ? 45.0
                              : MediaQuery.of(context).size.width,
                          buttonRadius: 30.0,
                          buttonChild: authProvider.otpLoading
                              ? const CircularProgressIndicator(
                                  valueColor:
                                      AlwaysStoppedAnimation(Colors.white),
                                  strokeWidth: 3.0,
                                )
                              : Text(
                                  'Next',
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                          buttonPressed: () {
                            log('current token: ${widget.token}');
                            authProvider
                                .verifyOtp(
                              widget.email,
                              otpController.text,
                              widget.isForgotPassword ?? false,
                              widget.token,
                              context,
                            )
                                .then((value) {
                              if (value) {
                                context.pushNamed(
                                  'set-password',
                                  extra: {
                                    'email': widget.email,
                                    'token': widget.token,
                                    'otp': otpController.text,
                                    'isForgotPassword': widget.isForgotPassword,
                                  },
                                );
                              }
                              otpController.clear();
                            });
                          },
                        ),
                      ),
                      const SizedBox(height: 25),
                      const ContactUs(),
                    ],
                  );
                }),
              ),
            ),
            NoConnectionTag(bottomPosition: 70),
          ],
        ),
      ),
    );
  }

  void resendOTP() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30.0),
                  child: Text(
                    provider.reSendOtpMessage,
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 18,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        setState(() {
                          provider.startTimer();
                          context.pop();
                        });
                      },
                      child: Container(
                        height: 45,
                        width: 120,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor),
                        child: Center(
                          child: Text(
                            "Ok",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
              ],
            )
          ],
        );
      },
    );
  }
}
