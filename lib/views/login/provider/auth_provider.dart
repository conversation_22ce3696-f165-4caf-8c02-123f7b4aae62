import 'dart:async';
import 'dart:developer';

import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../app/core/constants.dart';
import '../../../controller/login_controller.dart';

class AuthProvider extends ChangeNotifier {
  // Terms and Condition
  bool _agreeToPolicy = false;
  bool _is18YearsOld = false;
  bool _showPolicyValidation = true;
  bool _showAgeValidation = true;

  String _errorMessage = '';
  bool signUpLoading = false;
  bool otpLoading = false;
  bool _isFieldEnabled = true;
  bool _isResendOtp = false;
  String _currentToken = '';
  final String _resendOtpMessage = '';
  bool _setPasswordFlag = false;
  bool _confirmPasswordFlag = false;
  bool _isPasswordValid = false;
  bool _isPasswordComplex = false;
  bool _isPasswordMatch = false;
  bool passwordLoading = false;
  int _start = 0;
  Timer? _timer;

  // NAME SCREEN
  bool _setNameFlag = false;
  bool _setLocationFlag = false;
  bool _setBioFlag = false;
  bool _nameLoading = false;
  bool _isNameSet = false;
  bool _handleLoading = false;
  bool _isHandlerAvailable = false;
  bool _handlerFlag = false;
  String _formattedHandler = '';

  late LoginController loginController;

  final timerCountdown = ValueNotifier(0);

  bool get agreeToPolicy => _agreeToPolicy;

  bool get is18YearsOld => _is18YearsOld;

  bool get showPolicyValidation => _showPolicyValidation;

  bool get showAgeValidation => _showAgeValidation;

  String get errorMessage => _errorMessage;

  bool get isFieldEnabled => _isFieldEnabled;

  int get start => _start;

  bool get isResendOtp => _isResendOtp;

  String get currentToken => _currentToken;

  String get reSendOtpMessage => _resendOtpMessage;

  bool get setPasswordFlag => _setPasswordFlag;

  bool get confirmPasswordFlag => _confirmPasswordFlag;

  bool get isPasswordValid => _isPasswordValid;

  bool get isPasswordComplex => _isPasswordComplex;

  bool get isPasswordMatch => _isPasswordMatch;

  // NAME GETTER
  bool get setNameFlag => _setNameFlag;

  bool get setLocationFlag => _setLocationFlag;

  bool get setBioFlag => _setBioFlag;

  bool get nameLoading => _nameLoading;

  bool get isNameSet => _isNameSet;

  bool get handleLoading => _handleLoading;

  bool get isHandlerAvailable => _isHandlerAvailable;

  bool get handlerFlag => _handlerFlag;

  String get formattedHandler => _formattedHandler;

  set is18YearsOld(bool value) {
    if (_is18YearsOld != value) {
      _is18YearsOld = value;

      _showAgeValidation = !_is18YearsOld;
      notifyListeners();
    }
  }

  set agreeToPolicy(bool value) {
    if (_agreeToPolicy != value) {
      _agreeToPolicy = value;
      _showPolicyValidation = !_agreeToPolicy;
      notifyListeners();
    }
  }

  set errorMessage(String message) {
    if (_errorMessage != message) {
      _errorMessage = message;
      notifyListeners();
    }
  }

  set currentToken(String token) {
    if (_currentToken != token) {
      _currentToken = token;
      notifyListeners();
    }
  }

  set start(int value) {
    if (_start != value) {
      _start = value;
      notifyListeners();
    }
  }

  set isPasswordValid(bool value) {
    if (_isPasswordValid != value) {
      _isPasswordValid = value;
      notifyListeners();
    }
  }

  set isPasswordComplex(bool value) {
    if (_isPasswordComplex != value) {
      _isPasswordComplex = value;
      notifyListeners();
    }
  }

  set isPasswordMatch(bool value) {
    if (_isPasswordMatch != value) {
      _isPasswordMatch = value;
      notifyListeners();
    }
  }

  set setNameFlag(bool value) {
    if (_setNameFlag != value) {
      _setNameFlag = value;
      notifyListeners();
    }
  }

  set setLocationFlag(bool value) {
    if (_setLocationFlag != value) {
      _setLocationFlag = value;
      notifyListeners();
    }
  }

  set setBioFlag(bool value) {
    if (_setBioFlag != value) {
      _setBioFlag = value;
      notifyListeners();
    }
  }

  set isHandlerAvailable(bool value) {
    if (_isHandlerAvailable != value) {
      _isHandlerAvailable = value;
      notifyListeners();
    }
  }

  set handlerFlag(bool value) {
    if (_handlerFlag != value) {
      _handlerFlag = value;
      notifyListeners();
    }
  }

  void initialize(BuildContext context) {
    loginController = Provider.of<LoginController>(context, listen: false);
    _agreeToPolicy = false;
    _is18YearsOld = false;
    _showPolicyValidation = true;
    _showAgeValidation = true;

    _errorMessage = '';
    signUpLoading = false;
    _isFieldEnabled = true;
  }

  void submitForm(
      String userEmail, GlobalKey<FormState> formKey, BuildContext context) {
    bool validation = formKey.currentState!.validate();

    _showPolicyValidation = !_agreeToPolicy;
    _showAgeValidation = !_is18YearsOld;
    notifyListeners();
    if (userEmail.isEmpty && validation) {
      _errorMessage = '*Enter email';
      notifyListeners();
    } else if (!EmailValidator.validate(userEmail) && validation) {
      _errorMessage = '*Email incorrect';
      notifyListeners();
    } else {
      if (validation &&
          _agreeToPolicy &&
          _is18YearsOld &&
          userEmail.isNotEmpty) {
        String emailId = userEmail.toLowerCase();
        signUpLoading = true;
        _isFieldEnabled = false;
        _errorMessage = '';
        Provider.of<LoginController>(context, listen: false)
            .verifyEmail(emailId, context)
            .then((responseMap) async {
          log("Response Message: $responseMap");
          await saveLocally(userEmail);
          if (responseMap["statusCode"] == 200) {
            final token = responseMap['data']['token'];
            if (context.mounted) {
              context.pushNamed(
                'otp',
                extra: {
                  'email': emailId,
                  'token': token,
                  "isForgotPassword": false,
                },
              );
            }
            _isFieldEnabled = true;
            _agreeToPolicy = false;
            _is18YearsOld = false;
            _showAgeValidation = true;
            _showPolicyValidation = true;
            signUpLoading = false;
          } else if (responseMap["statusCode"] == 400) {
            _errorMessage = responseMap['message'];
            signUpLoading = false;
            _isFieldEnabled = true;
          } else {
            _errorMessage = responseMap['message'];
            _isFieldEnabled = true;
            signUpLoading = false;
          }
          notifyListeners();
        });
      } else {
        _errorMessage = '';
      }
    }
  }

  void forgotPassword(
      String userEmail, GlobalKey<FormState> formKey, BuildContext context) {
    bool validation = formKey.currentState!.validate();
    if (userEmail.isEmpty && validation) {
      _errorMessage = '*Enter email';
      notifyListeners();
    } else if (!EmailValidator.validate(userEmail) && validation) {
      _errorMessage = '*Email incorrect';
      notifyListeners();
    } else {
      if (validation && userEmail.isNotEmpty) {
        signUpLoading = true;
        _isFieldEnabled = false;
        _errorMessage = '';
        notifyListeners();
        Provider.of<LoginController>(context, listen: false)
            .forgotPassword(userEmail, context)
            .then((responseMap) {
          log('Response : $responseMap');
          if (responseMap["statusCode"] == 200) {
            final token = responseMap['data']['token'];
            if (context.mounted) {
              context.pushNamed(
                'otp',
                extra: {
                  'email': userEmail,
                  'token': token,
                  'isForgotPassword': true,
                },
              );
            }
            _isFieldEnabled = true;
            // _emailController.clear();
            signUpLoading = false;
            notifyListeners();
          } else {
            _isFieldEnabled = true;
            signUpLoading = false;
            _errorMessage = responseMap['message'];
            log("Email not exists : $_errorMessage");
            notifyListeners();
          }
        });
      }
    }
  }

  Future<bool> verifyOtp(String userEmailId, String otp, bool isForgotPassword,
      String currentToken, BuildContext context) async {
    _errorMessage = ''; // Clear previous error
    otpLoading = true;
    _isFieldEnabled = false;
    notifyListeners();

    try {
      log("currnet token : $currentToken");
      bool isResetPassword = isForgotPassword ? true : false;
      final responseMap =
          await Provider.of<LoginController>(context, listen: false).verifyOTP(
        userEmailId,
        currentToken,
        otp,
        isResetPassword,
        context,
      );

      log('Response : $responseMap');

      if (responseMap["statusCode"] == 200) {
        _errorMessage = '';
        return true;
        // Handle successful verification
      } else {
        _errorMessage =
            responseMap['message'] ?? 'Invalid OTP. Please try again.';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = 'An error occurred. Please try again.';
      notifyListeners();
      return false;
    } finally {
      otpLoading = false;
      _isFieldEnabled = true;
      notifyListeners();
    }
  }

  Future<void> reSendOtp(String userEmail, BuildContext context) async {
    _errorMessage = ''; // Clear any previous error
    _isResendOtp = false;
    notifyListeners();

    try {
      final responseMap =
          await Provider.of<LoginController>(context, listen: false)
              .verifyEmail(userEmail, context);

      if (responseMap["statusCode"] == 200) {
        _currentToken = responseMap['data']['token'];
        startTimer(); // Restart the timer
      } else {
        _errorMessage = responseMap['message'] ?? 'Failed to resend OTP';
        _isResendOtp = true;
      }
    } catch (e) {
      _errorMessage = 'An error occurred. Please try again.';
      _isResendOtp = true;
    }

    notifyListeners();
  }

  Future<void> setPassword(
      String password,
      String confirmPassword,
      String userEmail,
      String otp,
      String token,
      String deviceId,
      GlobalKey<FormState> passwordFormKey,
      BuildContext context) async {
    await _handlePasswordSetReset(
      password: password,
      confirmPassword: confirmPassword,
      userEmail: userEmail,
      otp: otp,
      token: token,
      deviceId: deviceId,
      passwordFormKey: passwordFormKey,
      context: context,
      isReset: false,
    );
  }

  void changePassword(
      String password,
      String confirmPassword,
      String userEmail,
      String otp,
      String token,
      String deviceId,
      GlobalKey<FormState> passwordFormKey,
      BuildContext context) {
    _handlePasswordSetReset(
      password: password,
      confirmPassword: confirmPassword,
      userEmail: userEmail,
      otp: otp,
      token: token,
      deviceId: deviceId,
      passwordFormKey: passwordFormKey,
      context: context,
      isReset: true,
    );
  }

  Future<void> _handlePasswordSetReset({
    required String password,
    required String confirmPassword,
    required String userEmail,
    required String otp,
    required String token,
    required String deviceId,
    required GlobalKey<FormState> passwordFormKey,
    required BuildContext context,
    required bool isReset,
  }) async {
    bool validation = passwordFormKey.currentState!.validate();
    if (validation) {
      if (password.isEmpty && confirmPassword.isEmpty) {
        _setPasswordFlag = true;
        _confirmPasswordFlag = true;
      } else if (password.isEmpty) {
        _setPasswordFlag = true;
      } else if (confirmPassword.isEmpty) {
        _confirmPasswordFlag = true;
      }
      notifyListeners();
    }

    if (validation &&
        _isPasswordValid &&
        _isPasswordComplex &&
        _isPasswordMatch) {
      passwordLoading = true;
      _isFieldEnabled = false;
      errorMessage = ''; // Clear previous error messages
      notifyListeners();

      try {
        if (isReset) {
          final loginController =
              Provider.of<LoginController>(context, listen: false);
          final success = await loginController.reSetPassword(
              userEmail, token, otp, password, context);
          if (success) {
            if (context.mounted) {
              context.goNamed('login');
            }
          } else {
            errorMessage = loginController.errorMessage;
            log('Error Message : $errorMessage');
          }
        } else {
          final fcmToken = await getFcmToken();
          log('Getting FCM Token : $fcmToken');
          final responseMap = await Provider.of<LoginController>(context,
                  listen: false)
              .setPassword(
                  userEmail, token, otp, password, fcmToken, deviceId, context);
          if (responseMap["statusCode"] == 200) {
            if (context.mounted) {
              context.pushReplacementNamed(
                'set-name-handle',
                extra: {'email': userEmail, 'token': token, 'otp': otp},
              );
            }
          } else {
            errorMessage = responseMap['error'];
          }
        }
      } catch (e) {
        errorMessage = 'An unexpected error occurred.';
        log('Password set/reset error: $e');
      } finally {
        _isFieldEnabled = true;
        passwordLoading = false;
        notifyListeners();
      }
    } else {
      passwordLoading = false;
      notifyListeners();
    }
  }

  void submitName(
    String name,
    String location,
    String bio,
    GlobalKey<FormState> nameFormKey,
  ) {
    bool validation = nameFormKey.currentState!.validate();

    _nameLoading = true;
    if (validation &&
        name.isNotEmpty &&
        location.trim().isNotEmpty &&
        bio.trim().isNotEmpty) {
      _isNameSet = true;
      _setNameFlag = false;
      _setLocationFlag = false;
      _setBioFlag = false;
      _isFieldEnabled = true;
      _nameLoading = false;
    } else {
      _nameLoading = false;
    }
    notifyListeners();
  }

  void submitHandle(
    String name,
    String location,
    String bio,
    String handle,
    String userEmail,
    String token,
    String otp,
    GlobalKey<FormState> handleFormKey,
    BuildContext context,
  ) {
    log("Handle 1: $handle");
    log("Available : $_isHandlerAvailable");
    if (handleFormKey.currentState!.validate()) {
      if (handle.isEmpty) {
        _errorMessage = '*Enter handle';
        _handlerFlag = true;
        notifyListeners();
        return;
      } else if (!_isHandlerAvailable) {
        _handlerFlag = true;
        notifyListeners();
        return;
      }
    }

    log('Name : $name');
    log('Handler : $handle');
    _handleLoading = true;
    _isFieldEnabled = false;
    notifyListeners();
    Provider.of<LoginController>(context, listen: false)
        .setNameAndHandle(
      userEmail,
      token,
      otp,
      name,
      '@$handle',
      location,
      bio,
      context,
    )
        .then((responseMap) async {
      log("Handle : $responseMap");
      await saveLocally(userEmail, name, handle);
      if (responseMap["statusCode"] == 200) {
        if (context.mounted) {
          context.goNamed('subscription');
        }
        _isFieldEnabled = true;
        _handleLoading = false;
        notifyListeners();
      } else {
        _isFieldEnabled = true;
        _handleLoading = false;
        _errorMessage = responseMap['message'];
        notifyListeners();
      }
    });
  }

  void checkHandler(String userEmail, String handler, BuildContext context) {
    if (handler.trim().isNotEmpty) {
      _formattedHandler = '@${handler.trim()}';
      _handlerFlag = false;
      notifyListeners();
      Provider.of<LoginController>(context, listen: false)
          .checkAavailabilityOfHandle(userEmail, _formattedHandler, context)
          .then((responseMap) {
        if (responseMap["statusCode"] == 200) {
          _isHandlerAvailable = true;
        } else {
          errorMessage = responseMap['error'];
          _handlerFlag = false;
          _isHandlerAvailable = false;
        }
        log("Handle Available $_isHandlerAvailable");
        notifyListeners();
      });
    } else {
      _handlerFlag = true;
      notifyListeners();
    }
  }

  bool isComplexPassword(String password) {
    final RegExp hasUppercase = RegExp(r'[A-Z]');
    final RegExp hasLowercase = RegExp(r'[a-z]');
    final RegExp hasDigit = RegExp(r'[0-9]');
    final RegExp hasSymbol = RegExp(r'[\W_]');

    int criteriaMet = 0;

    if (hasUppercase.hasMatch(password)) criteriaMet++;
    if (hasLowercase.hasMatch(password)) criteriaMet++;
    if (hasDigit.hasMatch(password)) criteriaMet++;
    if (hasSymbol.hasMatch(password)) criteriaMet++;

    return criteriaMet >= 3;
  }

  void validatePassword(String password, String confirmPassword) {
    _setPasswordFlag = false;
    _isPasswordValid = password.length >= 8;
    _isPasswordComplex = isComplexPassword(password);
    _isPasswordMatch = password == confirmPassword;
    notifyListeners();
  }

  void validateConfirmPassword(String confirmPassword, String password) {
    _confirmPasswordFlag = false;
    _isPasswordMatch = confirmPassword == password;
    notifyListeners();
  }

  Future<String> getFcmToken() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    final fcmToken = pref.getString('fcmToken');
    log("Local Database Token : $fcmToken");
    return fcmToken ?? '';
  }

  void startTimer() {
    _start = 600;
    _isResendOtp = false;
    _timer?.cancel();

    timerCountdown.value = _start;

    const oneSec = Duration(seconds: 1);
    _timer = Timer.periodic(oneSec, (timer) {
      if (_start == AppConstants.otpExpiryTime) {
        _isResendOtp = true;
        _timer!.cancel();
      } else {
        _isResendOtp = false;
        _start--;
      }
      timerCountdown.value = _start;
      notifyListeners();
    });
  }

  void disposeTimer() {
    _timer?.cancel();
    _timer = null;
    _start = 0;
    timerCountdown.value = 0;
    notifyListeners();
  }

  Future<void> saveLocally(
    String userEmail, [
    String? name,
    String? handle,
  ]) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    pref.setString('userEmailId', userEmail);
    pref.setString('userName', name ?? '');
    pref.setString('userHandle', handle ?? '');
    pref.setBool('isFirstTimeUser', true);
    pref.setBool("isUserBioAndLocationAvailable", true);
  }
}
