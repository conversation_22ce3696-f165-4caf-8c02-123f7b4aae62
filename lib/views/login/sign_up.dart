import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:eljunto/reusableWidgets/contact_us.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../reusableWidgets/custom_text_widget.dart';
import '../../app/core/constants.dart';
import '../../reusableWidgets/custom_button.dart';
import '../../reusableWidgets/version_display.dart';
import 'provider/auth_provider.dart';

class SignUpPage extends StatefulWidget {
  final bool? isForgotPassword;

  const SignUpPage({
    super.key,
    this.isForgotPassword,
  });

  @override
  State createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  TextEditingController otpController = TextEditingController();
  late AuthProvider provider;

  @override
  void initState() {
    provider = Provider.of<AuthProvider>(context, listen: false);
    provider.initialize(context);
    super.initState();
  }

  String? appName, appVersion;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            Scaffold(
              backgroundColor: Colors.transparent,
              appBar: AppBar(
                toolbarHeight: 140,
                shape: const Border(
                  bottom: BorderSide(
                    color: AppConstants.primaryColor,
                    width: 1.5,
                  ),
                ),
                automaticallyImplyLeading: false,
                backgroundColor: AppConstants.textGreenColor,
                centerTitle: true,
                title: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      AppConstants.elJuntoLogo,
                      height: 100,
                      width: 80,
                      filterQuality: FilterQuality.high,
                      fit: BoxFit.contain,
                    ),
                    const VersionDisplay(),
                    const SizedBox(
                      height: 25,
                    ),
                  ],
                ),
              ),
              body: ListView(
                shrinkWrap: true,
                physics: const PageScrollPhysics(),
                children: [
                  const SizedBox(
                    height: 25,
                  ),
                  Text(
                    widget.isForgotPassword ?? false
                        ? 'Forgot Password'
                        : 'Create New Account',
                    style: lbRegular.copyWith(
                      fontSize: 24,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  widget.isForgotPassword ?? false
                      ? Text(
                          'Enter email linked with the account to receive a temporary password. ',
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                          textAlign: TextAlign.center,
                        )
                      : const SizedBox.shrink(),
                  const SizedBox(
                    height: 25,
                  ),
                  Form(
                    key: _formKey,
                    child: Consumer<AuthProvider>(
                        builder: (context, authProvider, child) {
                      return Column(
                        children: [
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  "Email:",
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 10),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 20.0),
                            child: TextFormField(
                              controller: _emailController,
                              style: lbRegular.copyWith(
                                fontSize: 18,
                              ),
                              keyboardType: TextInputType.emailAddress,
                              decoration: InputDecoration(
                                contentPadding: const EdgeInsets.all(10),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(5.0),
                                  borderSide: const BorderSide(
                                      color: AppConstants.primaryColor,
                                      width: 2.5),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: const BorderSide(
                                    color: AppConstants.primaryColor,
                                    width: 1.5,
                                  ),
                                ),
                                disabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: const BorderSide(
                                    color: Colors.black38,
                                    width: 1.5,
                                  ),
                                ),
                                filled: true,
                                fillColor: Colors.white.withOpacity(0.8),
                                errorStyle: errorMsg,
                              ),
                              enabled: authProvider.isFieldEnabled,
                              onChanged: (value) {
                                setState(() {
                                  authProvider.errorMessage = '';
                                });
                              },
                            ),
                          ),
                          (authProvider.errorMessage.isNotEmpty)
                              ? const SizedBox(
                                  height: 10,
                                )
                              : const SizedBox.shrink(),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Row(
                              children: [
                                const Spacer(),
                                authProvider.errorMessage.isNotEmpty
                                    ? Text(
                                        authProvider.errorMessage,
                                        style: errorMsg,
                                      )
                                    : const SizedBox.shrink(),
                              ],
                            ),
                          ),
                          widget.isForgotPassword ?? false
                              ? const SizedBox.shrink()
                              : const SizedBox(
                                  height: 15,
                                ),
                          widget.isForgotPassword ?? false
                              ? const SizedBox.shrink()
                              : Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Checkbox(
                                        value: authProvider.agreeToPolicy,
                                        activeColor: AppConstants.primaryColor,
                                        // Set the color when checkbox is checked
                                        checkColor: Colors.white,
                                        // Color of the checkmark
                                        onChanged: (bool? value) {
                                          setState(() {
                                            authProvider.agreeToPolicy =
                                                value ?? false;
                                            // authProvider.showPolicyValidation =
                                            //     !authProvider.agreeToPolicy;
                                          });
                                        },
                                      ),
                                      Expanded(
                                        child: RichText(
                                          text: TextSpan(
                                            children: [
                                              TextSpan(
                                                text: 'I agree to El Junto ',
                                                style: lbRegular.copyWith(
                                                  height: 2.3,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              TextSpan(
                                                recognizer: TapGestureRecognizer()
                                                  ..onTap = () async =>
                                                      await launchUrl(
                                                          AppConstants
                                                              .privacyPolicyUrl),
                                                text: 'Privacy Policy',
                                                style: lbRegular.copyWith(
                                                  color: AppConstants.blueColor,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              TextSpan(
                                                text: ' and ',
                                                style: lbRegular.copyWith(
                                                  fontSize: 16,
                                                ),
                                              ),
                                              TextSpan(
                                                text: ' Terms of Service ',
                                                recognizer: TapGestureRecognizer()
                                                  ..onTap = () async =>
                                                      await launchUrl(AppConstants
                                                          .termsAndConditionUrl),
                                                style: lbRegular.copyWith(
                                                  color: AppConstants.blueColor,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              authProvider.showPolicyValidation
                                                  ? TextSpan(
                                                      text: '*',
                                                      style: lbBold.copyWith(
                                                        fontSize: 14,
                                                        color: AppConstants
                                                            .redColor,
                                                      ),
                                                    )
                                                  : const TextSpan(),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                          widget.isForgotPassword ?? false
                              ? const SizedBox.shrink()
                              : Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Checkbox(
                                        value: authProvider.is18YearsOld,
                                        activeColor: AppConstants.primaryColor,
                                        checkColor: Colors.white,
                                        onChanged: (bool? value) {
                                          setState(() {
                                            authProvider.is18YearsOld =
                                                value ?? false;
                                            // authProvider.showAgeValidation =
                                            //     !authProvider.is18YearsOld;
                                          });
                                        },
                                      ),
                                      Flexible(
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'I am 18 or older ',
                                              style: lbRegular.copyWith(
                                                fontSize: 16,
                                                height: 3,
                                              ),
                                            ),
                                            authProvider.showAgeValidation
                                                ? Text(
                                                    "*",
                                                    style: lbBold.copyWith(
                                                      fontSize: 14,
                                                      height: 3.4,
                                                      color:
                                                          AppConstants.redColor,
                                                    ),
                                                  )
                                                : const SizedBox.shrink(),
                                            // if (_showAgeValidation)
                                            //   Text(
                                            //     'Please confirm you are 18 years or older',
                                            //     style: errorMsg,
                                            //   ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                          const SizedBox(
                            height: 25,
                          ),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 20.0),
                            child: CustomLoaderButton(
                              // loginText: 'Login',
                              buttonWidth: authProvider.signUpLoading
                                  ? 45.0
                                  : MediaQuery.of(context).size.width,
                              buttonRadius: 30.0,
                              buttonChild: authProvider.signUpLoading
                                  ? const CircularProgressIndicator(
                                      valueColor:
                                          AlwaysStoppedAnimation(Colors.white),
                                      strokeWidth: 3.0,
                                    )
                                  : Text(
                                      widget.isForgotPassword ?? false
                                          ? 'Submit'
                                          : 'Next',
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                              buttonPressed: () {
                                widget.isForgotPassword ?? false
                                    ? authProvider.forgotPassword(
                                        _emailController.text.trim(),
                                        _formKey,
                                        context)
                                    : authProvider.submitForm(
                                        _emailController.text.trim(),
                                        _formKey,
                                        context);
                              },
                            ),
                          ),
                          widget.isForgotPassword ?? false
                              ? NetworkAwareTap(
                                  onTap: () {
                                    context.pop();
                                  },
                                  child: Container(
                                    margin: const EdgeInsets.only(
                                        left: 20, right: 20, top: 25),
                                    height: 45,
                                    width: MediaQuery.of(context).size.width,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(49),
                                      border: Border.all(
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                    child: Center(
                                      child: Text(
                                        "Cancel",
                                        style: lbBold.copyWith(
                                          fontSize: 18,
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              : const SizedBox.shrink(),
                          const SizedBox(height: 25),
                          ContactUs(),
                        ],
                      );
                    }),
                  ),
                  const SizedBox(height: 10),
                ],
              ),
            ),
            NoConnectionTag(bottomPosition: 70),
          ],
        ),
      ),
    );
  }
}
