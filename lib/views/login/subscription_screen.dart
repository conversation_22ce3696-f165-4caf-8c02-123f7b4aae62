import 'dart:developer';
import 'dart:io';

import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/controller/session_manager.dart';
import 'package:eljunto/controller/subscription_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:eljunto/reusableWidgets/contact_us.dart';
import 'package:eljunto/reusableWidgets/version_display.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../app/core/constants.dart';
import '../../controller/inapp_purchase_controller.dart';
import '../../controller/login_controller.dart';
import '../../models/subscription_model/subscription_model.dart';
import '../../reusableWidgets/custom_button.dart';
import '../../reusableWidgets/subscription/subscription_list_ui.dart';

class SubscriptionPage extends StatefulWidget {
  const SubscriptionPage({super.key});

  @override
  State createState() => _SubscriptionPageState();
}

class _SubscriptionPageState extends State<SubscriptionPage> {
  SubscriptionController? subscriptionController;
  SubscriptionModel? subscriptionDetails;
  InAppPurchaseController? inAppPurchaseController;
  bool isLoading = false;
  bool isLogout = false;
  bool isFreeTrial = false;
  bool isPuchasePlan = false;
  bool isSubscribedClicked = false;
  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    setState(() {
      isLoading = true;
    });
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await getUSerSubscriptionDetails();

      if (mounted) {
        subscriptionController =
            Provider.of<SubscriptionController>(context, listen: false);
        isFreeTrial =
            Provider.of<SubscriptionController>(context, listen: false)
                .isFreeTraial;
        log("Subscription Scrren $isFreeTrial");
        inAppPurchaseController =
            Provider.of<InAppPurchaseController>(context, listen: false);
        await Provider.of<InAppPurchaseController>(context, listen: false)
            .fetchProducts(context);
      }
      if (mounted) {
        final purchaseUpdated =
            Provider.of<InAppPurchaseController>(context, listen: false)
                .inAppPurchase
                .purchaseStream;
        Provider.of<InAppPurchaseController>(context, listen: false)
            .streamSubscription = purchaseUpdated.listen((purchaseDetailsList) {
          log("In Purchase Stream: $purchaseDetailsList");
          if (mounted) {
            Provider.of<InAppPurchaseController>(context, listen: false)
                .handlePurchaseUpdates(purchaseDetailsList, context);
          }
        }, onDone: () {
          log("In Purchase Stream Done");
          if (mounted) {
            Provider.of<InAppPurchaseController>(context, listen: false)
                .streamSubscription
                .cancel();
          }
        }, onError: (error) {
          log(error.toString());
        });
      }
      await localData();
      setState(() {
        isLoading = false;
      });
    });
    super.initState();
  }

  Future<void> getUSerSubscriptionDetails() async {
    // ADD API CALL TO GET SUBSCRIPTION DETAILS 17 FEB 2025
    if (mounted) {
      await Provider.of<SubscriptionController>(context, listen: false)
          .getSubscriptionDetails(context)
          .then((_) async {
        if (mounted) {
          log("Subscription Data : ${Provider.of<SubscriptionController>(context, listen: false).subscriptionDetails?.data?.subscriptionDetails}");
        }
      });
    }
  }

  String? userMailId;

  Future<void> localData() async {
    userMailId = _sessionManager.userEmail;
    if (mounted) {
      subscriptionDetails =
          Provider.of<SubscriptionController>(context, listen: false)
              .subscriptionDetails;
    }
    // print("Mail : $userMailId");
  }

  Future<bool> logOutFunction() async {
    bool value = await Provider.of<LoginController>(context, listen: false)
        .logOutFunction(userMailId ?? '');
    return value;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 140,
        shape: const Border(
          bottom: BorderSide(
            color: AppConstants.primaryColor,
            width: 1.5,
          ),
        ),
        automaticallyImplyLeading: false,
        backgroundColor: AppConstants.textGreenColor,
        centerTitle: true,
        title: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              AppConstants.elJuntoLogo,
              height: 100,
              width: 80,
              filterQuality: FilterQuality.high,
              fit: BoxFit.contain,
            ),
            const VersionDisplay(),
            const SizedBox(height: 25),
          ],
        ),
      ),
      body: Skeletonizer(
        effect: const SoldColorEffect(
          color: AppConstants.skeletonforgroundColor,
          lowerBound: 0.1,
          upperBound: 0.5,
        ),
        containersColor: AppConstants.skeletonBackgroundColor,
        enabled: isLoading,
        child: Container(
          padding: EdgeInsets.zero,
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                AppConstants.bgImagePath,
              ),
              filterQuality: FilterQuality.high,
              fit: BoxFit.cover,
            ),
          ),
          child: Stack(
            children: [
              NoConnectionTag(bottomPosition: 70),
              Padding(
                padding: const EdgeInsets.only(
                  left: 19.0,
                  right: 19,
                  top: 25,
                  // bottom: 35,
                ),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Text(
                        isFreeTrial
                            ? 'This is the final step!'
                            : 'Free trial ended',
                        style: lbRegular.copyWith(
                          fontSize: 24,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 25),
                      Skeleton.replace(
                        replacement: SubscriptionListUI(
                          subProducts: inAppPurchaseController?.products,
                        ),
                        child: SubscriptionListUI(
                          subProducts: inAppPurchaseController?.products,
                        ),
                      ),
                      if (isSubscribedClicked &&
                          inAppPurchaseController?.purchaseProduct == null)
                        Text(
                          'Please select a subscription to continue.',
                          style: lbRegular.copyWith(
                            fontSize: 12,
                            fontWeight: FontWeight.w300,
                            color: AppConstants.redColor,
                          ),
                        )
                      else
                        const SizedBox.shrink(),
                      const SizedBox(height: 20),
                      CustomLoaderButton(
                        // loginText: 'Login',
                        buttonWidth: isPuchasePlan
                            ? 45.0
                            : MediaQuery.of(context).size.width,
                        buttonRadius: 30.0,
                        buttonChild: isPuchasePlan
                            ? const CircularProgressIndicator(
                                valueColor:
                                    AlwaysStoppedAnimation(Colors.white),
                                strokeWidth: 3.0,
                              )
                            : Text(
                                'Subscribe',
                                style: lbBold.copyWith(
                                  fontSize: 18,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                        buttonPressed: () async {
                          // If no product is selected
                          if (inAppPurchaseController?.purchaseProduct ==
                              null) {
                            setState(() {
                              isSubscribedClicked = true;
                            });
                            return;
                          }

                          // If a purchase is in progress, prevent further actions
                          if (inAppPurchaseController?.isPurchaseLoading ??
                              false) {
                            return; // Don't do anything while loading
                          }

                          // Proceed with the purchase
                          final purchaseProduct =
                              Provider.of<InAppPurchaseController>(
                            context,
                            listen: false,
                          ).purchaseProduct;

                          if (purchaseProduct != null) {
                            await Provider.of<InAppPurchaseController>(context,
                                    listen: false)
                                .buyProduct(purchaseProduct);
                          }

                          setState(() {
                            isSubscribedClicked = false;
                          });
                        },
                      ),
                      const SizedBox(height: 25),
                      _buildSubscriptionPoints(
                        isFreeTrial
                            ? 'Start your 30-Day Free Trial, full access for 30 days.'
                            : Platform.isAndroid
                                ? 'To continue enjoying full access, please click above to subscribe through the play store.'
                                : 'To continue enjoying full access, please click above to subscribe through the app store.',
                      ),
                      const SizedBox(height: 10),
                      _buildSubscriptionPoints(
                          'Only subscribers have unlimited access to all features.'),
                      const SizedBox(height: 10),
                      _buildSubscriptionPoints(
                          'Start or join a book club with integrated videoconferencing and messaging.'),
                      const SizedBox(height: 10),
                      _buildSubscriptionPoints(
                          'Search to see who is into your favorite books or author.'),
                      const SizedBox(height: 10),
                      _buildSubscriptionPoints(
                          'Customer support for any app feedback or technical issues.'),
                      const SizedBox(height: 10),
                      _buildSubscriptionPoints(
                          'Customer support to add books to the database.'),
                      const SizedBox(height: 10),
                      _buildPP(),
                      const SizedBox(height: 20),
                      Text(
                        "${inAppPurchaseController?.planPrice} per month after 30-day free trial. Cancel anytime. Subscription auto-renews unless canceled at least 24 hours before the end of the current period.",
                        style: lbRegular.copyWith(fontSize: 18),
                      ),
                      // ],
                      const SizedBox(height: 25),
                      CustomLoaderLogoutButton(
                        // loginText: 'Login',
                        buttonWidth:
                            isLogout ? 45.0 : MediaQuery.of(context).size.width,
                        buttonRadius: 30.0,
                        buttonChild: isLogout
                            ? const CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation(
                                  AppConstants.primaryColor,
                                ),
                                strokeWidth: 3.0,
                              )
                            : Text(
                                'Logout',
                                style: lbBold.copyWith(
                                  fontSize: 18,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                        buttonPressed: () async {
                          if (inAppPurchaseController?.isPurchaseLoading ??
                              false) {
                            const SizedBox.shrink();
                          } else {
                            setState(() {
                              isLogout = true;
                            });
                            Map<String, dynamic> payload = {};
                            if (context.mounted) {
                              await _sessionManager.userLogout(
                                context,
                                payload,
                              );
                            }
                            setState(() {
                              isLogout = false;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 25),
                      NetworkAwareTap(
                        onTap: () {
                          (inAppPurchaseController?.isPurchaseLoading ?? false)
                              ? const SizedBox.shrink()
                              : context.pushNamed('trial-delete-account');
                        },
                        child: Text(
                          'Delete Account',
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 14,
                            decoration: TextDecoration.underline,
                            decorationColor: AppConstants.redColor,
                            color: AppConstants.redColor,
                          ),
                        ),
                      ),
                      const SizedBox(height: 25),
                      const ContactUs(),
                      const SizedBox(height: 25),
                    ],
                  ),
                ),
              ),
              Consumer<InAppPurchaseController>(
                builder: (context, inAppPurchaseController, child) {
                  log("Is Purchase Loading : ${inAppPurchaseController.isPurchaseLoading}");
                  return Align(
                    alignment: Alignment.center,
                    child: inAppPurchaseController.isPurchaseLoading
                        ? CircularProgressIndicator(
                            color: AppConstants.primaryColor,
                            strokeWidth: 4,
                            strokeAlign: 2,
                          )
                        : const SizedBox.shrink(),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPP() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Padding(
          padding: EdgeInsets.only(top: 12.0),
          child: Icon(Icons.circle, color: Colors.black, size: 8),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 3.0),
            child: Text.rich(
              TextSpan(
                text: 'View our ',
                style: lbRegular.copyWith(fontSize: 18),
                children: [
                  TextSpan(
                    text: 'privacy policy',
                    style: lbRegular.copyWith(
                      fontSize: 18,
                      color: AppConstants.blueColor,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () => launchUrl(
                            AppConstants.privacyPolicyUrl,
                          ),
                  ),
                  TextSpan(
                    text: ' and ',
                    style: lbRegular.copyWith(fontSize: 18),
                  ),
                  TextSpan(
                    text: 'terms of service',
                    style: lbRegular.copyWith(
                      fontSize: 18,
                      color: AppConstants.blueColor,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () => launchUrl(
                            AppConstants.termsAndConditionUrl,
                          ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubscriptionPoints(String pointText) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Padding(
          padding: EdgeInsets.only(top: 12.0),
          child: Icon(Icons.circle, color: Colors.black, size: 8),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 3.0),
            child: Text(
              pointText,
              style: lbRegular.copyWith(fontSize: 18),
            ),
          ),
        ),
      ],
    );
  }
}
