import 'package:eljunto/views/meeting/meeting_screen.dart';
import 'package:permission_handler/permission_handler.dart';

import 'base_video_call_mixin.dart';
import 'permission_handler_mixin.dart';

mixin CallInitializationMixin on BaseVideoCallMixin<MeetingScreen> {
  Future<void> initializeCallAndPermissions(
      MeetingScreenStateAccessor stateAccessor) async {
    setStateIfMounted(() {
      stateAccessor.isLoading = true;
      stateAccessor.callJoinedSuccessfully = false;
    });

    final micStatus = await Permission.microphone.request();
    final camStatus = await Permission.camera.request();
    bool initialMicMute = !(micStatus.isGranted || micStatus.isLimited);
    bool initialCamMute = !(camStatus.isGranted || camStatus.isLimited);

    if (!mounted) return;

    if (micStatus.isPermanentlyDenied || camStatus.isPermanentlyDenied) {
      if (!mounted) return;
      PermissionHandlerMixin.showPermissionInfoDialog(
        this,
        isPermanentlyDeniedMic: micStatus.isPermanentlyDenied,
        isPermanentlyDeniedCam: camStatus.isPermanentlyDenied,
      );
    }

    // Check if call is already active - similar to working implementation
    if (videoCallManager.isCallActive && videoCallManager.engine != null) {
      stateAccessor.engine = videoCallManager.engine;
      stateAccessor.updateStateFromManager();
      stateAccessor.callJoinedSuccessfully = true;
      stateAccessor.addNetworkQualityListener();
      if (!mounted) return;
      setStateIfMounted(() {
        stateAccessor.isLoading = false;
      });
    } else {
      final widgetState = widget;
      if (widgetState.channelName == null ||
          widgetState.token == null ||
          widgetState.userHandle == null) {
        showErrorAndPop("Missing call information.");
        if (!mounted) return;
        setStateIfMounted(() {
          stateAccessor.isLoading = false;
        });
        return;
      }

      // Use the approach from the working implementation
      bool success = await videoCallManager.startCall(
        channelName: widgetState.channelName!,
        token: widgetState.token!,
        preferredLocalUid: widgetState.userId ?? 0,
        userHandle: widgetState.userHandle!,
        profilePictureUrl: widgetState.profilePictureUrl,
        discussionQue: widgetState.discussionQue,
        bookName: widgetState.bookName,
        bookClubId: widgetState.bookClubId,
        initialAudioMute: initialMicMute,
        initialVideoMute: initialCamMute,
      );

      if (!mounted) return;

      if (success) {
        // Directly access the engine from videoCallManager like in working implementation
        stateAccessor.engine = videoCallManager.engine;
        if (stateAccessor.engine != null) {
          stateAccessor.addNetworkQualityListener();
          setStateIfMounted(() {
            stateAccessor.callJoinedSuccessfully = true;
            stateAccessor.isLoading = false;
          });
        } else {
          showErrorAndPop("Failed to obtain video engine.");
          setStateIfMounted(() {
            stateAccessor.isLoading = false;
          });
        }
      } else {
        showErrorAndPop("Failed to start video call.");
        setStateIfMounted(() {
          stateAccessor.isLoading = false;
        });
      }
    }
  }
}
