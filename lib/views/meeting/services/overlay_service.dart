import 'dart:async';
import 'dart:developer';

import 'package:eljunto/app/core/routes/app_navigation_keys.dart';
import 'package:eljunto/views/meeting/services/video_call_manager.dart';
import 'package:eljunto/views/meeting/widgets/picture_in_picture/pip_widget.dart';
import 'package:flutter/material.dart';

enum TransitionState { none, toFullScreen, toPip }

class OverlayService {
  static final OverlayService _instance = OverlayService._internal();

  factory OverlayService() => _instance;

  OverlayService._internal();

  // --- Dependencies & Navigation ---
  final GlobalKey<NavigatorState> navigatorKey =
      AppNavigationKeys.rootNavigatorKey;
  final VideoCallManager _videoCallManager = VideoCallManager();

  // --- Overlay Management ---
  OverlayEntry? _overlayEntry;
  Offset _offset = Offset(20, 40);

  // --- Listener Callbacks ---
  VoidCallback? _callStateListenerCallback;
  VoidCallback? _remoteUsersListenerCallback;

  // --- PiP Sizing Constants ---
  static const double pipBaseWidth = 130.0;
  static const double pipBaseHeight = 180.0;
  static const double pipExpandedWidth = 150.0;
  static const double pipExpandedHeight = 220.0;
  static const double pipHeaderHeight = 40.0;
  static const double pipMediaControlsHeight = 40.0;

  bool get isActive => _overlayEntry != null;

  TransitionState _transitionState = TransitionState.none;
  Timer? _transitionDebounceTimer;

  // --- Public Methods ---
  void showVideoCallOverlay() {
    if (_transitionState != TransitionState.none) {
      _transitionDebounceTimer?.cancel();
      _transitionDebounceTimer = Timer(
        const Duration(milliseconds: 300),
        () => _transitionState = TransitionState.none,
      );
      return;
    }

    _transitionState = TransitionState.toPip;

    if (!_videoCallManager.isCallActive || _videoCallManager.engine == null) {
      return;
    }

    hideOverlay();

    Future.delayed(const Duration(milliseconds: 50), () {
      final context = navigatorKey.currentContext;
      if (context == null) return;

      // Create the overlay entry
      _createOverlayEntry();

      try {
        Overlay.of(context).insert(_overlayEntry!);
      } catch (e) {
        _removeListeners();
        log('Error inserting overlay: $e');
      }
    });

    Future.delayed(
      const Duration(milliseconds: 500),
      () => _transitionState = TransitionState.none,
    );
  }

  void hideOverlay() {
    _removeListeners();

    if (_overlayEntry != null) {
      final entry = _overlayEntry;
      _overlayEntry = null;

      Future.microtask(() {
        try {
          entry?.remove();
        } catch (e) {
          log('Error removing overlay: $e');
        }
      });
    }
  }

  // --- Listener Setup and Removal ---
  void _setupListeners({
    required bool pipControlsVisible,
    Timer? pipAutoHideTimer,
    required int currentRemoteUserIndex,
    required List<int> activeRemoteUids,
  }) {
    _callStateListenerCallback = () {
      if (!_videoCallManager.callStateNotifier.value && isActive) {
        hideOverlay();
      }
    };

    _remoteUsersListenerCallback = () {
      if (isActive) {
        _overlayEntry?.markNeedsBuild();
      }
    };

    _videoCallManager.callStateNotifier
        .addListener(_callStateListenerCallback!);
    _videoCallManager.remoteUsersNotifier
        .addListener(_remoteUsersListenerCallback!);
  }

  void _removeListeners() {
    if (_callStateListenerCallback != null) {
      _videoCallManager.callStateNotifier
          .removeListener(_callStateListenerCallback!);
      _callStateListenerCallback = null;
    }

    if (_remoteUsersListenerCallback != null) {
      _videoCallManager.remoteUsersNotifier
          .removeListener(_remoteUsersListenerCallback!);
      _remoteUsersListenerCallback = null;
    }
  }

  void _createOverlayEntry() {
    if (_overlayEntry != null) return;

    bool pipControlsVisible = false;
    int currentRemoteUserIndex = 0;
    List<int> activeRemoteUids =
        _videoCallManager.remoteUsersNotifier.value.toList();
    if (activeRemoteUids.isNotEmpty &&
        currentRemoteUserIndex >= activeRemoteUids.length) {
      currentRemoteUserIndex = 0;
    }

    _overlayEntry = OverlayEntry(
      builder: (ctx) => PipWidget(
        initialOffset: _offset,
        initialControlsVisible: pipControlsVisible,
        initialRemoteUserIndex: currentRemoteUserIndex,
        initialActiveRemoteUids: activeRemoteUids,
        videoCallManager: _videoCallManager,
        navigatorKey: navigatorKey,
        onPanUpdate: (details) {
          _offset += details.delta;
          _overlayEntry?.markNeedsBuild();
        },
        onHideOverlay: hideOverlay,
      ),
    );
  }

  void prepareVideoCallOverlay() {
    if (_overlayEntry != null) return;

    if (!_videoCallManager.isCallActive || _videoCallManager.engine == null) {
      return;
    }

    _createOverlayEntry();

    _setupListeners(
      pipControlsVisible: false,
      pipAutoHideTimer: null,
      currentRemoteUserIndex: 0,
      activeRemoteUids: _videoCallManager.remoteUsersNotifier.value.toList(),
    );
  }

  bool get isOverlayActive => _overlayEntry != null;
}
