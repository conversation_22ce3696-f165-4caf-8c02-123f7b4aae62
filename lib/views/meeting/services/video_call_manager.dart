import 'dart:developer';

import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:flutter/foundation.dart';

class VideoCallManager {
  static final VideoCallManager _instance = VideoCallManager._internal();
  factory VideoCallManager() => _instance;
  VideoCallManager._internal();

  RtcEngine? _engine;
  bool _isInitialized = false;
  bool _isInCall = false;

  // --- State Notifiers ---
  final callStateNotifier = ValueNotifier<bool>(false);
  final remoteUsersNotifier = ValueNotifier<Set<int>>({});
  final remoteVideoMutedNotifier = ValueNotifier<Map<int, bool>>({});
  final remoteAudioMutedNotifier = ValueNotifier<Map<int, bool>>({});
  final localAudioMutedNotifier = ValueNotifier<bool>(false);
  final localVideoMutedNotifier = ValueNotifier<bool>(false);

  // Add a value notifier to track PiP mode
  final ValueNotifier<bool> pipModeActiveNotifier = ValueNotifier<bool>(false);

  // --- Internal State ---
  int? _localUid;
  final Map<int, bool> _internalRemoteVideoStates = {};
  final Map<int, bool> _internalRemoteAudioStates = {};
  bool _internalLocalAudioMuted = false;
  bool _internalLocalVideoMuted = false;

  // --- Call Metadata ---
  String? _channelName;
  String? _token;
  String? _userHandle;
  String? _profilePictureUrl;
  String? _discussionQue;
  String? _bookName;
  int? _bookClubId;

  // --- Getters ---
  RtcEngine? get engine => _engine;
  bool get isCallActive => _engine != null && _isInCall;
  int? get localUid => _localUid;

  // Add controller caching
  final Map<int, VideoViewController> _controllerCache = {};

  // --- Public Methods ---

  VideoViewController getOrCreateController(
    int uid, {
    bool isRemote = true,
    String? channelId,
  }) {
    if (_controllerCache.containsKey(uid) && _engine != null) {
      return _controllerCache[uid]!;
    }

    VideoViewController controller;
    if (isRemote && channelId != null) {
      controller = VideoViewController.remote(
        rtcEngine: _engine!,
        canvas: VideoCanvas(uid: uid),
        connection: RtcConnection(channelId: channelId),
        useFlutterTexture: false,
        useAndroidSurfaceView: false,
      );
    } else {
      controller = VideoViewController(
        rtcEngine: _engine!,
        canvas: VideoCanvas(uid: uid),
        useFlutterTexture: false,
        useAndroidSurfaceView: false,
      );
    }

    _controllerCache[uid] = controller;
    return controller;
  }

  Future<bool> startCall({
    required String channelName,
    required String token,
    required int preferredLocalUid,
    required String userHandle,
    required String? profilePictureUrl,
    required String? discussionQue,
    required String? bookName,
    required int bookClubId,
    bool initialAudioMute = false,
    bool initialVideoMute = false,
  }) async {
    if (isCallActive) {
      return true;
    }

    _channelName = channelName;
    _token = token;
    _userHandle = userHandle;
    _profilePictureUrl = profilePictureUrl;
    _discussionQue = discussionQue;
    _bookName = bookName;
    _bookClubId = bookClubId;

    try {
      await _initializeEngine();
      _setEventHandlers();

      await _engine!.enableVideo();
      await _engine!.enableAudio();

      _internalLocalAudioMuted = initialAudioMute;
      await _engine!.muteLocalAudioStream(_internalLocalAudioMuted);
      localAudioMutedNotifier.value = _internalLocalAudioMuted;

      _internalLocalVideoMuted = initialVideoMute;
      await _engine!.muteLocalVideoStream(_internalLocalVideoMuted);
      localVideoMutedNotifier.value = _internalLocalVideoMuted;

      if (!_internalLocalVideoMuted) {
        await _engine!.startPreview();
      }

      await _engine!.joinChannel(
        token: token,
        channelId: channelName,
        uid: preferredLocalUid,
        options: const ChannelMediaOptions(
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
          clientRoleType: ClientRoleType.clientRoleBroadcaster,
          publishCameraTrack: true,
          publishMicrophoneTrack: true,
          autoSubscribeAudio: true,
          autoSubscribeVideo: true,
        ),
      );
      return true;
    } catch (e) {
      await endCall();
      return false;
    }
  }

  Future<void> endCall() async {
    for (final controller in _controllerCache.values) {
      try {
        controller.dispose();
      } catch (e) {
        log('Error disposing controller: $e');
      }
    }
    _controllerCache.clear();

    if (_engine == null) return;

    try {
      if (_isInCall) {
        await _engine?.leaveChannel();
      }
      await _engine?.stopPreview();
      await _engine?.release();
    } catch (e) {
      log('Error ending call: $e');
    } finally {
      _engine = null;
      _isInitialized = false;
      _isInCall = false;
      _clearCallState();
      callStateNotifier.value = false;
      localAudioMutedNotifier.value = false;
      localVideoMutedNotifier.value = false;
    }
  }

  void toggleAudio() {
    if (!isCallActive) return;
    _internalLocalAudioMuted = !_internalLocalAudioMuted;
    _engine?.muteLocalAudioStream(_internalLocalAudioMuted);
    localAudioMutedNotifier.value = _internalLocalAudioMuted;
  }

  void toggleVideo() {
    if (!isCallActive) return;
    _internalLocalVideoMuted = !_internalLocalVideoMuted;
    _engine?.muteLocalVideoStream(_internalLocalVideoMuted);
    if (_internalLocalVideoMuted) {
      _engine?.stopPreview();
    } else {
      _engine?.startPreview();
    }
    localVideoMutedNotifier.value = _internalLocalVideoMuted;
  }

  void switchCamera() {
    if (!isCallActive) return;
    _engine?.switchCamera();
  }

  Map<String, dynamic>? getCallMetadata() {
    if ((!isCallActive && _channelName == null)) return null;
    return {
      'channelName': _channelName,
      'token': _token,
      'userId': _localUid,
      'userHandle': _userHandle,
      'profilePictureUrl': _profilePictureUrl,
      'discussionQue': _discussionQue,
      'bookName': _bookName,
      'bookClubId': _bookClubId,
      'isRejoin': true,
    };
  }

  Future<void> setVideoEncoderConfiguration(
      VideoEncoderConfiguration config) async {
    if (!isCallActive) return;
    try {
      await _engine?.setVideoEncoderConfiguration(config);
    } catch (e) {
      log('Error setting video encoder configuration: $e');
    }
  }

  void setPipModeActive(bool active) {
    pipModeActiveNotifier.value = active;
  }

  bool get isPipModeActive => pipModeActiveNotifier.value;

  // --- Private Helpers ---

  Future<void> _initializeEngine() async {
    if (_isInitialized) return;
    _engine = createAgoraRtcEngine();
    await _engine!.initialize(
      RtcEngineContext(
        appId: 'a2f415f7ddd94a409f63d84ef2977445',
        channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
      ),
    );
    _isInitialized = true;
  }

  void _setEventHandlers() {
    if (_engine == null) return;
    _engine!.registerEventHandler(
      RtcEngineEventHandler(
        onJoinChannelSuccess: (connection, elapsed) {
          _localUid = connection.localUid;
          _isInCall = true;
          callStateNotifier.value = true;
        },
        onLeaveChannel: (connection, stats) {
          _isInCall = false;
          callStateNotifier.value = false;
          _clearCallState();
        },
        onError: (err, msg) {
          callStateNotifier.value = false;
          endCall();
        },
        onUserJoined: (connection, remoteUid, elapsed) {
          _internalRemoteVideoStates[remoteUid] = false;
          _internalRemoteAudioStates[remoteUid] = false;

          final currentUsers = remoteUsersNotifier.value.toSet();
          currentUsers.add(remoteUid);
          remoteUsersNotifier.value = currentUsers;

          remoteVideoMutedNotifier.value = Map.from(_internalRemoteVideoStates);
          remoteAudioMutedNotifier.value = Map.from(_internalRemoteAudioStates);
        },
        onUserOffline: (connection, remoteUid, reason) {
          _internalRemoteVideoStates.remove(remoteUid);
          _internalRemoteAudioStates.remove(remoteUid);

          final currentUsers = remoteUsersNotifier.value.toSet();
          currentUsers.remove(remoteUid);
          remoteUsersNotifier.value = currentUsers;

          remoteVideoMutedNotifier.value = Map.from(_internalRemoteVideoStates);
          remoteAudioMutedNotifier.value = Map.from(_internalRemoteAudioStates);
        },
        onRemoteVideoStateChanged:
            (connection, remoteUid, state, reason, elapsed) {
          bool isMuted = state == RemoteVideoState.remoteVideoStateStopped ||
              state == RemoteVideoState.remoteVideoStateFrozen;
          _internalRemoteVideoStates[remoteUid] = isMuted;
          remoteVideoMutedNotifier.value = Map.from(_internalRemoteVideoStates);
        },
        onRemoteAudioStateChanged:
            (connection, remoteUid, state, reason, elapsed) {
          bool isMuted = state == RemoteAudioState.remoteAudioStateStopped ||
              state == RemoteAudioState.remoteAudioStateFrozen ||
              state == RemoteAudioState.remoteAudioStateFailed;
          _internalRemoteAudioStates[remoteUid] = isMuted;
          remoteAudioMutedNotifier.value = Map.from(_internalRemoteAudioStates);
        },
      ),
    );
  }

  void _clearCallState() {
    _internalRemoteVideoStates.clear();
    _internalRemoteAudioStates.clear();
    _localUid = null;
    _internalLocalAudioMuted = false;
    _internalLocalVideoMuted = false;
    remoteUsersNotifier.value = {};
    remoteVideoMutedNotifier.value = {};
    remoteAudioMutedNotifier.value = {};
  }
}
