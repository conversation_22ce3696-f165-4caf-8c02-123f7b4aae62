import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class LeaveCallDialog {
  static Future<bool?> showConfirmationDialog({
    required BuildContext context,
    required bool isCallActive,
  }) async {
    if (!isCallActive) return true;
    return await showDialog<bool?>(
      context: context,
      builder: (ctx) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
          side: const BorderSide(color: AppConstants.primaryColor),
        ),
        backgroundColor: AppConstants.backgroundColor,
        buttonPadding: EdgeInsets.zero,
        title: Text(
          'Leave Call?',
          style: lbBold.copyWith(
            fontSize: 18,
            color: AppConstants.primaryColor,
          ),
        ),
        content: Text(
          'End the call or keep it running in the background (PiP)?',
          style: lbRegular.copyWith(
            fontSize: 14,
            color: AppConstants.primaryColor,
          ),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => ctx.pop(false),
            style: TextButton.styleFrom(
                splashFactory: NoSplash.splashFactory,
                shadowColor: Colors.transparent,
                surfaceTintColor: Colors.transparent),
            child: Text(
              'Cancel',
              style: lbBold.copyWith(
                fontSize: 14,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          TextButton(
            onPressed: () => ctx.pop(null),
            child: Text(
              'Background',
              style: lbBold.copyWith(
                fontSize: 14,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          TextButton(
            onPressed: () => ctx.pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(
              'End Call',
              style: lbBold.copyWith(
                fontSize: 14,
                color: AppConstants.redColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
