import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:flutter/material.dart';

class VideoCallAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String bookName;
  final VoidCallback onBackPressed;

  const VideoCallAppBar({
    super.key,
    required this.bookName,
    required this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppConstants.videoConferenceAppBarColor,
      centerTitle: true,
      leading: IconButton(
        icon: const Icon(
          Icons.arrow_back,
          color: AppConstants.backgroundColor,
        ),
        onPressed: onBackPressed,
      ),
      title: <PERSON><PERSON>e<PERSON><PERSON>(
        children: [
          Text(
            bookName,
            style: lbBold.copyWith(
              fontSize: 18,
              color: AppConstants.backgroundColor,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
