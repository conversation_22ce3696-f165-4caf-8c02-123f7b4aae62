// REMOVED: import 'package:dash_chat_2/dash_chat_2.dart';
import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/chat_controller.dart';
import 'package:eljunto/controller/connectivity_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/models/messages/custom_chat_message.dart';
import 'package:eljunto/reusableWidgets/message_app_bar.dart';
import 'package:eljunto/views/messages/widgets/chat_screen_view.dart';
import 'package:eljunto/views/messages/widgets/message_app_bar_title.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:provider/provider.dart';

class FireBaseChat extends StatelessWidget {
  final int bookClubId;
  final String? bookClubName;

  const FireBaseChat({
    super.key,
    required this.bookClubId,
    this.bookClubName,
  });

  @override
  Widget build(BuildContext context) {
    try {
      return ChangeNotifierProvider(
        create: (context) => ChatController(
          bookClubId: bookClubId,
          bookClubController: context.read<BookClubController>(),
          messageController: context.read<MessageController>(),
          initialBookClubName: bookClubName,
        )..initialize(),
        child: const _ChatScreenView(),
      );
    } catch (e) {
      return Scaffold(
        body: Center(
          child: Text('Failed to initialize chat: ${e.toString()}'),
        ),
      );
    }
  }
}

class _ChatScreenView extends StatefulWidget {
  const _ChatScreenView();

  @override
  State<_ChatScreenView> createState() => _ChatScreenViewState();
}

class _ChatScreenViewState extends State<_ChatScreenView> {
  final _textController = TextEditingController();
  final _connectivityProvider = locator<ConnectivityProvider>();
  bool _isInternetConnected = true;

  @override
  void initState() {
    super.initState();
    _connectivityProvider.statusStream.listen((status) {
      if (mounted) {
        setState(
          () => _isInternetConnected = status == InternetStatus.connected,
        );
      }
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  void _onSend(ChatController controller) {
    if (_textController.text.trim().isEmpty) return;
    if (controller.currentUser == null) return;

    final message = CustomChatMessage(
      user: controller.currentUser!,
      createdAt: DateTime.now(),
      text: _textController.text.trim(),
    );
    controller.sendMessage(message);
    _textController.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ChatController>(
      builder: (context, controller, child) {
        return Scaffold(
          appBar: _buildAppBar(context, controller),
          body: _buildBody(context, controller),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(
    BuildContext context,
    ChatController controller,
  ) {
    if (controller.isLoading &&
        (controller.initialBookClubName?.isEmpty ?? false)) {
      return PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(width: 1.5, color: AppConstants.primaryColor),
            ),
          ),
          child: const MessageAppBarTitle(isInternetFlag: false),
        ),
      );
    } else {
      final clubName = controller.initialBookClubName?.isNotEmpty ?? false
          ? controller.initialBookClubName
          : (controller.members.isNotEmpty
              ? controller.members.first.bookClubName ?? ''
              : '');
      return PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: _isInternetConnected
              ? MessageScreenAppBar(
                  bookName: clubName,
                  isSetProfile: true,
                  onTap: () => context.goNamed(
                    "message-user-club-details",
                    queryParameters: {
                      'bookClubId': controller.bookClubId.toString(),
                      'userId': controller.loggedInUserId.toString(),
                    },
                  ),
                  clubMembers: controller.members,
                )
              : const MessageAppBarTitle(isInternetFlag: true),
        ),
      );
    }
  }

  Widget _buildBody(BuildContext context, ChatController controller) {
    if (controller.isLoading) {
      return Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AppConstants.bgImagePath),
            fit: BoxFit.cover,
            filterQuality: FilterQuality.high,
          ),
        ),
        child: const Center(
          child: CircularProgressIndicator(color: AppConstants.primaryColor),
        ),
      );
    }

    return Container(
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AppConstants.bgImagePath),
          fit: BoxFit.cover,
          filterQuality: FilterQuality.high,
        ),
      ),
      child: GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: CustomChatView(
          controller: controller,
          textController: _textController,
          isInternetConnected: _isInternetConnected,
          onSend: () => _onSend(controller),
        ),
      ),
    );
  }
}
