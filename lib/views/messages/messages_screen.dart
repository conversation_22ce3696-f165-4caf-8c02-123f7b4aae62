import 'dart:async';

import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/controller/session_manager.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../app/core/constants.dart';
import '../../app/core/utils/text_style.dart';
import '../../reusableWidgets/appbar.dart';

class MessagesScreen extends StatefulWidget {
  const MessagesScreen({super.key});

  @override
  State<MessagesScreen> createState() => _MessagesScreenState();
}

class _MessagesScreenState extends State<MessagesScreen> {
  int? loggedInUserId;
  bool _isInitialized = false;
  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    super.initState();

    _initializeAndFetchData();
  }

  Future<void> _initializeAndFetchData() async {
    if (_isInitialized) return;
    _isInitialized = true;

    loggedInUserId = _sessionManager.userId;
    if (loggedInUserId != null && mounted) {
      // The controller now handles the entire data fetching pipeline.
      context.read<MessageController>().initializeMessagesData(loggedInUserId!);
    }
  }

  Widget _buildContent(MessageController controller) {
    if (controller.isFirstLoading) {
      return _buildSkeletonLoader();
    }

    final clubs = controller.documentList
        .map((doc) => doc.data() as Map<String, dynamic>)
        .toList();

    if (clubs.isEmpty) {
      return _buildEmptyState();
    }

    return _buildClubList(clubs, controller.notificationStatus);
  }

  @override
  void dispose() {
    // The controller manages its own streams. If the controller is scoped
    // only to this screen, you would dispose it here. If it's a global
    // provider, it should persist.
    super.dispose();
  }

  void _handleChatTap(String clubId, int userClubId, String clubName) {
    if (loggedInUserId == null) return;

    // Mark as seen first (optimistic update via controller)
    context.read<MessageController>().markMessageAsSeen(
          clubId,
          userClubId,
          loggedInUserId!,
        );

    // Then navigate
    context.pushNamed(
      'chat-screen',
      queryParameters: {
        "userId": loggedInUserId.toString(),
        'bookClubId': clubId,
        'bookClubName': clubName,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const AppBarWidget(
            appBarText: 'Messages',
          ),
        ),
      ),
      body: Stack(
        children: [
          Positioned.fill(
            child: Image.asset(
              AppConstants.bgImagePath,
              fit: BoxFit.cover,
              filterQuality: FilterQuality.high,
            ),
          ),
          Consumer<MessageController>(
            builder: (context, controller, child) => _buildContent(controller),
          ),
        ],
      ),
    );
  }

  // BEST PRACTICE: Extract complex widgets into their own methods for readability.

  Widget _buildSkeletonLoader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Skeletonizer(
        enabled: true,
        child: ListView.builder(
          padding: const EdgeInsets.only(bottom: 25),
          itemCount: 15, // Number of skeleton items
          itemBuilder: (context, index) {
            // Using a "bone" widget for Skeletonizer to draw over
            return Container(
              margin: const EdgeInsets.only(top: 25),
              padding: const EdgeInsets.symmetric(horizontal: 15),
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: AppConstants.primaryColor,
                  width: 1.5,
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Loading club name please wait...',
                      style: lbBold.copyWith(fontSize: 18),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 10),
                  Image.asset(
                    "assets/icons/Messages.png",
                    height: 30,
                    width: 48,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.only(top: 25, left: 20, right: 20),
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(10)),
            border: Border.all(
              color: AppConstants.primaryColor,
              width: 1.5,
            ),
          ),
          child: ListTile(
            titleTextStyle: lbBold.copyWith(fontSize: 14, height: 1.3),
            title: const Text(
              "When you join or start a club, you’ll be able to message fellow club members here to coordinate meeting times, chat, etc.\n\nGet into a club & start chatting with your book people!",
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildClubList(
    List<Map<String, dynamic>> clubs,
    Map<int, bool> notificationStatus,
  ) {
    return RepaintBoundary(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: ListView.builder(
          padding: const EdgeInsets.only(bottom: 25),
          itemCount: clubs.length,
          itemBuilder: (context, index) {
            final club = clubs[index];
            final int bookClubId = club['bookClubId'] ?? 0;
            final String clubName = club['bookClubName'] ?? 'Unnamed Club';
            final bool isSeen =
                notificationStatus[bookClubId] ?? true; // Default to seen

            // This widget is now much cleaner and has no code duplication.
            return RepaintBoundary(
              child: NetworkAwareTap(
                key: ValueKey(bookClubId),
                onTap: () => _handleChatTap(
                  bookClubId.toString(),
                  bookClubId,
                  clubName,
                ),
                child: Stack(
                  clipBehavior:
                      Clip.none, // Allow positioned items to draw outside
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 25),
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      height: 50,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: AppConstants.primaryColor,
                          width: 1.5,
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              clubName,
                              overflow: TextOverflow.ellipsis,
                              style: lbBold.copyWith(fontSize: 18),
                            ),
                          ),
                          const SizedBox(width: 10),
                          Image.asset(
                            "assets/icons/Messages.png",
                            height: 30,
                            width: 48,
                            fit: BoxFit.contain,
                            filterQuality: FilterQuality.high,
                          ),
                        ],
                      ),
                    ),
                    // Show notification dot if message is NOT seen.
                    Visibility(
                      visible: !isSeen,
                      child: Positioned(
                        top: 15, // Adjusted for better visual placement
                        right: 2,
                        child: Image.asset(
                          AppConstants.notificationImagePath,
                          height: 18,
                          width: 18,
                          fit: BoxFit.cover,
                          filterQuality: FilterQuality.high,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
