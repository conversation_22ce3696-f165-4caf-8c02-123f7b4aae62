import 'dart:convert';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:eljunto/app/core/utils/app_config.dart';
import 'package:flutter/services.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:http/http.dart' as http;

class ChatService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  CollectionReference _getMessagesCollection(int bookClubId) {
    return _firestore
        .collection('Club_Collection')
        .doc(bookClubId.toString())
        .collection('messages');
  }

  // Gets a real-time stream of the latest messages
  Stream<QuerySnapshot> getMessagesStream(int bookClubId, int limit) {
    return _getMessagesCollection(bookClubId)
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots();
  }

  // Fetches a paginated list of older messages
  Future<QuerySnapshot> getMoreMessages(
    int bookClubId,
    DocumentSnapshot lastDoc,
    int limit,
  ) {
    return _getMessagesCollection(bookClubId)
        .orderBy('createdAt', descending: true)
        .startAfterDocument(lastDoc)
        .limit(limit)
        .get();
  }

  // Sends a new message
  Future<void> sendMessage(int bookClubId, Map<String, dynamic> messageData) {
    return _getMessagesCollection(bookClubId).add(messageData);
  }

  // Updates the parent club document's timestamp for ordering
  Future<void> updateClubMetadata(int bookClubId, String bookClubName) {
    return _firestore
        .collection('Club_Collection')
        .doc(bookClubId.toString())
        .set(
      {
        'createdAt': FieldValue.serverTimestamp(),
        'bookClubId': bookClubId,
        'bookClubName': bookClubName,
      },
      SetOptions(merge: true),
    );
  }

  // Adds or updates an emoji reaction
  Future<void> addEmojiReaction(
    int bookClubId,
    String messageId,
    String userId,
    String emoji,
  ) {
    return _getMessagesCollection(bookClubId).doc(messageId).update({
      'reactions.$userId': emoji,
    });
  }

  // Removes an emoji reaction
  Future<void> removeEmojiReaction(
    int bookClubId,
    String messageId,
    String userId,
  ) {
    return _getMessagesCollection(bookClubId).doc(messageId).update({
      'reactions.$userId': FieldValue.delete(),
    });
  }

  Future<void> sendNotification({
    required List<String>? fcmTokens,
    required String title,
    required String body,
    required String bookClubId,
    required String receiverId,
  }) async {
    if (fcmTokens == null || fcmTokens.isEmpty) return;

    final isDev = AppConfig.shared.flavor == Flavor.dev;
    final serverUrl = isDev
        ? 'https://fcm.googleapis.com/v1/projects/el-junto-development-server/messages:send'
        : 'https://fcm.googleapis.com/v1/projects/el-junto-4fb80/messages:send';
    final serviceAccountPath = isDev
        ? "assets/el-junto-development-server-firebase-adminsdk-vmec3-eb3a739632.json"
        : "assets/el-junto-4fb80-firebase-adminsdk-gwtik-f421cef23d.json";

    try {
      final bearerToken = await _getBearerToken(serviceAccountPath);
      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $bearerToken',
      };
      final deepLink =
          'https://eljunto.com/chat-screen?userId=$receiverId&bookClubId=$bookClubId';

      for (final token in fcmTokens) {
        final payload = _buildFcmPayload(token, title, body, deepLink);
        final response = await http.post(
          Uri.parse(serverUrl),
          headers: headers,
          body: json.encode(payload),
        );
        if (response.statusCode != 200) {
          log('Failed to send notification to $token: ${response.body}');
        }
      }
    } catch (e) {
      log('Error sending notification: $e');
    }
  }

  Map<String, dynamic> _buildFcmPayload(
    String token,
    String title,
    String body,
    String deepLink,
  ) {
    // ... FCM payload logic is the same as before ...
    return {
      'message': {
        'token': token,
        'notification': {'title': title, 'body': body},
        'data': {'sound': 'default', 'deepLink': deepLink},
        "apns": {
          "headers": {"apns-priority": "10"},
          "payload": {
            "aps": {"sound": "default"},
          },
        },
        "android": {
          "priority": "high",
          "notification": {"channel_id": "high_importance_channel"},
        },
      },
    };
  }

  Future<String> _getBearerToken(String serviceAccountKeyPath) async {
    // ... getBearerToken logic is the same ...
    final serviceAccountKey =
        await rootBundle.loadString(serviceAccountKeyPath);
    final credentials = ServiceAccountCredentials.fromJson(serviceAccountKey);
    const scopes = ['https://www.googleapis.com/auth/firebase.messaging'];
    final authClient = await clientViaServiceAccount(credentials, scopes);
    final accessToken = authClient.credentials.accessToken.data;
    authClient.close();
    return accessToken;
  }
}
