import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/controller/session_manager.dart';

/// Service for handling Firebase Firestore user club data streams
/// Replaces API-based club data fetching with real-time Firebase streams
class FirebaseUserClubService {
  static final FirebaseUserClubService _instance =
      FirebaseUserClubService._internal();
  factory FirebaseUserClubService() => _instance;
  FirebaseUserClubService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  StreamSubscription<DocumentSnapshot>? _userClubsSubscription;
  final _sessionManager = locator<SessionManager>();

  /// Stream of user's club data from Firebase
  /// Structure: users/{userId} document with individual club fields
  /// Based on screenshot: bookClubId, bookClubName, bookAuthor, createdAt, userId fields
  Stream<List<Map<String, dynamic>>> getUserClubsStream(int userId) {
    return _firestore
        .collection('users')
        .doc(userId.toString())
        .snapshots()
        .map((docSnapshot) {
      if (!docSnapshot.exists || docSnapshot.data() == null) {
        log('User document does not exist or is empty for userId: $userId');
        return <Map<String, dynamic>>[];
      }

      final data = docSnapshot.data()!;
      final List<Map<String, dynamic>> clubs = [];

      // This logic assumes any Map value in the document is a club.
      // This is robust as long as the document structure is consistent.
      data.forEach((key, value) {
        if (value is Map<String, dynamic>) {
          // Add a check for a required field to make parsing more robust.
          if (value.containsKey('bookClubId')) {
            clubs.add(value);
          }
        }
      });

      log('Extracted ${clubs.length} clubs for userId: $userId');
      return clubs;
    }).handleError((error) {
      log('Error in user clubs stream for userId $userId: $error');
      // Return an empty list on stream error to prevent app crashes.
      return <Map<String, dynamic>>[];
    });
  }

  /// Get current user's club data as a one-time fetch
  Future<List<Map<String, dynamic>>> getCurrentUserClubs() async {
    try {
      final userId = _sessionManager.userId;
      if (userId == null) {
        log('No logged in user found');
        return [];
      }

      final docSnapshot =
          await _firestore.collection('users').doc(userId.toString()).get();

      if (!docSnapshot.exists || docSnapshot.data() == null) {
        log('User document does not exist or is empty for userId: $userId');
        return [];
      }

      final data = docSnapshot.data()!;
      final List<Map<String, dynamic>> clubs = [];

      data.forEach((key, value) {
        if (value is Map<String, dynamic> && value.containsKey('bookClubId')) {
          clubs.add(value);
        }
      });

      log('getCurrentUserClubs - Extracted ${clubs.length} clubs');
      return clubs;
    } catch (e) {
      log('Error fetching current user clubs: $e');
      return [];
    }
  }

  /// Dispose of any active subscriptions
  void dispose() {
    _userClubsSubscription?.cancel();
    _userClubsSubscription = null;
  }

  /// Check if user has any clubs
  Future<bool> hasUserClubs() async {
    final clubs = await getCurrentUserClubs();
    return clubs.isNotEmpty;
  }

  /// Get club IDs from user's clubs
  Future<List<int>> getUserClubIds() async {
    final clubs = await getCurrentUserClubs();
    return clubs
        .map((club) => club['bookClubId'] as int?)
        .where((id) => id != null)
        .cast<int>()
        .toList();
  }
}
