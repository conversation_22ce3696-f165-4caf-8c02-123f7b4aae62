import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/question_feedback_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class MessageAppBarTitle extends StatelessWidget {
  final bool isInternetFlag;

  const MessageAppBarTitle({super.key, required this.isInternetFlag});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppConstants.textGreenColor,
      centerTitle: true,
      title: Padding(
        padding: const EdgeInsets.only(top: 15.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CupertinoActivityIndicator(
              animating: true,
              color: AppConstants.primaryColor,
            ),
            const SizedBox(width: 10),
            Text(
              isInternetFlag ? 'Waiting for network' : 'Loading club details',
              style: lbBold.copyWith(
                fontSize: 15,
                color: AppConstants.primaryColor,
              ),
            ),
          ],
        ),
      ),
      leading: Padding(
        padding: const EdgeInsets.only(left: 20.0, top: 10),
        child: NetworkAwareTap(
          onTap: () => context.goNamed('Message'),
          child: Image.asset(
            width: 73,
            height: 65,
            "assets/icons/Back.png",
            fit: BoxFit.contain,
            filterQuality: FilterQuality.high,
          ),
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 20.0, top: 10),
          child: NetworkAwareTap(
            onTap: () {
              showDialog(
                context: context,
                barrierColor: Colors.white60,
                builder: (BuildContext context) {
                  return const QuestionFeedbackDialog();
                },
              );
            },
            child: Image.asset(
              AppConstants.questionLogoImagePath,
              height: 34,
              width: 34,
            ),
          ),
        ),
      ],
    );
  }
}
