import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/controller/chat_controller.dart';
import 'package:eljunto/models/messages/custom_chat_message.dart';
import 'package:eljunto/reusableWidgets/cached_network_image.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/emoji/custom_emoji_picker.dart';
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart' as emoji;
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import 'reactions_bottom_sheet.dart';

class MessageRow extends StatelessWidget {
  final CustomChatMessage message;
  final bool showUsername;
  final bool showAvatar;
  final bool isOutgoing;
  final ChatController controller;

  const MessageRow({
    super.key,
    required this.message,
    required this.showUsername,
    required this.showAvatar,
    required this.isOutgoing,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 10,
        right: 10,
        top: showUsername ? 4 : 2,
        bottom: 4,
      ),
      child: Row(
        mainAxisAlignment:
            isOutgoing ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isOutgoing) _buildAvatarColumn(context),
          _buildMessageContent(context),
        ],
      ),
    );
  }

  Widget _buildAvatarColumn(BuildContext context) {
    if (showAvatar) {
      return Padding(
        padding: const EdgeInsets.only(right: 8.0),
        child: ClipRRect(
          borderRadius: BorderRadiusGeometry.circular(50),
          child: NetworkAwareTap(
            onTap: () => context.goNamed(
              'message-club-member-profile',
              extra: {
                'userId': int.parse(message.user.id),
                'bookClubId': controller.bookClubId.toString(),
              },
            ),
            child: CustomCachedNetworkImage(
              imageUrl: message.user.profileImage,
              width: 40,
              height: 40,
              errorImage: AppConstants.profileLogoImagePath,
            ),
          ),
        ),
      );
    }
    return const SizedBox(width: 48);
  }

  Widget _buildMessageContent(BuildContext context) {
    return GestureDetector(
      onLongPress: () => _showEmojiPicker(context, message, controller),
      child: Column(
        crossAxisAlignment:
            isOutgoing ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          if (showUsername) ...[
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: Text(
                message.user.firstName ?? '',
                style: lbRegular.copyWith(
                  fontSize: 14,
                  color: AppConstants.primaryColor,
                ),
              ),
            ),
            const SizedBox(height: 5),
          ],
          Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.7,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 5),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: isOutgoing ? AppConstants.textGreenColor : Colors.white,
              border: Border.all(color: AppConstants.primaryColor),
            ),
            child: Column(
              crossAxisAlignment: isOutgoing
                  ? CrossAxisAlignment.end
                  : CrossAxisAlignment.start,
              children: [
                Theme(
                  data: Theme.of(context).copyWith(
                    textSelectionTheme: TextSelectionThemeData(
                      cursorColor: AppConstants.textGreenColor,
                      selectionColor: isOutgoing
                          ? AppConstants.primaryColor
                          : AppConstants.textGreenColor,
                      selectionHandleColor: isOutgoing
                          ? AppConstants.primaryColor
                          : AppConstants.textGreenColor,
                    ),
                  ),
                  child: SelectableText(
                    message.text,
                    textAlign: TextAlign.start,
                    style: lbRegular.copyWith(
                      fontSize: 16,
                      color:
                          isOutgoing ? Colors.white : AppConstants.primaryColor,
                    ),
                    enableInteractiveSelection: true,
                    selectionControls: MaterialTextSelectionControls(),
                  ),
                ),
                const SizedBox(height: 4),
                _buildTimeAndSeenStatus(),
              ],
            ),
          ),
          _buildMessageBottom(context),
        ],
      ),
    );
  }

  Widget _buildTimeAndSeenStatus() {
    final date = DateFormat('hh:mm a').format(message.createdAt);
    if (!isOutgoing) {
      return Text(
        date,
        textAlign: TextAlign.start,
        style: lbRegular.copyWith(
          fontSize: 12,
          color: AppConstants.primaryColor,
        ),
      );
    }
    final seenByList = message.customMessageProperties?['seenBy'];
    bool isSeenByOther = seenByList is List && controller.loggedInUserId != null
        ? seenByList.any(
            (e) =>
                e['user_id'] != controller.loggedInUserId &&
                e['isSeen'] == true,
          )
        : false;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          date,
          style: lbRegular.copyWith(fontSize: 12, color: Colors.white),
        ),
        const SizedBox(width: 15),
        if (isSeenByOther)
          const Icon(
            Icons.done_all,
            color: AppConstants.primaryColor,
            size: 14,
          ),
      ],
    );
  }

  Widget _buildMessageBottom(BuildContext context) {
    final reactionsMap =
        message.customMessageProperties?["reactions"] as Map<String, dynamic>?;
    if (reactionsMap == null || reactionsMap.isEmpty) {
      return const SizedBox.shrink();
    }

    final Map<String, int> emojiCounts = {};
    reactionsMap.forEach((_, emoji) {
      if (emoji != null) {
        emojiCounts[emoji.toString()] =
            (emojiCounts[emoji.toString()] ?? 0) + 1;
      }
    });

    return NetworkAwareTap(
      onTap: () => showReactionsBottomSheet(
        context: context,
        reactionsMap: reactionsMap,
        message: message,
        controller: controller,
      ),
      child: Container(
        margin: const EdgeInsets.only(top: 3),
        padding: const EdgeInsets.symmetric(horizontal: 5),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: AppConstants.primaryColor),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: emojiCounts.entries
              .map(
                (entry) => Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: Row(
                    children: [
                      Text(entry.key, style: const TextStyle(fontSize: 16)),
                      if (entry.value > 1)
                        Padding(
                          padding: const EdgeInsets.only(left: 2),
                          child: Text(
                            '${entry.value}',
                            style: lbRegular.copyWith(
                              fontSize: 12,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              )
              .toList(),
        ),
      ),
    );
  }

  void _showEmojiPicker(
    BuildContext context,
    CustomChatMessage message,
    ChatController controller,
  ) async {
    final result = await showModalBottomSheet<emoji.Emoji>(
      context: context,
      isScrollControlled: true,
      backgroundColor: AppConstants.primaryColor,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      builder: (_) => CustomEmojiPicker(message: message),
    );
    if (result != null) {
      final messageId = message.customMessageProperties?['message_id'];
      if (messageId != null) {
        controller.addEmojiReaction(
          messageId,
          result.emoji,
        );
      }
    }
  }
}
