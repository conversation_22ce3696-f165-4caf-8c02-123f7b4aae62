import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:eljunto/app/core/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

/// Creates a circular version of the given image.
Future<File> convertSquareImageToCircle(File imageFile) async {
  final image = await decodeImageFromList(await imageFile.readAsBytes());
  final output = await _createCircleImage(image);
  await imageFile.writeAsBytes(output);
  return imageFile;
}

/// Helper function to perform the canvas drawing for the circular image.
Future<Uint8List> _createCircleImage(ui.Image image) async {
  final pictureRecorder = ui.PictureRecorder();
  final canvas = Canvas(pictureRecorder);
  final size = image.width.toDouble();
  final radius = size / 2;

  final paint = Paint()..isAntiAlias = true;

  // Clip the canvas to a circle
  canvas.clipPath(
    Path()
      ..addOval(
        Rect.fromCircle(center: Offset(radius, radius), radius: radius),
      ),
  );

  // Draw the image onto the clipped canvas
  canvas.drawImage(image, Offset.zero, paint);

  final picture = pictureRecorder.endRecording();
  final img = await picture.toImage(size.toInt(), size.toInt());
  final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
  return byteData!.buffer.asUint8List();
}

/// Opens the image cropper UI for the user to crop the picked image.
Future<CroppedFile?> cropUserImage(XFile pickedFile) async {
  final croppedImage = await ImageCropper().cropImage(
    sourcePath: pickedFile.path,
    aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
    compressFormat: ImageCompressFormat.png,
    compressQuality: 100,
    uiSettings: [
      AndroidUiSettings(
        toolbarTitle: 'Crop your image',
        toolbarColor: AppConstants.primaryColor,
        cropFrameColor: AppConstants.primaryColor,
        toolbarWidgetColor: Colors.white,
        cropStyle: CropStyle.circle,
        hideBottomControls: true,
        lockAspectRatio: true,
        showCropGrid: true,
      ),
      IOSUiSettings(
        minimumAspectRatio: 1,
        title: 'Crop your image',
        doneButtonTitle: 'Save',
        cancelButtonTitle: 'Cancel',
        cropStyle: CropStyle.circle,
        showCancelConfirmationDialog: true,
        hidesNavigationBar: false,
        rotateButtonsHidden: true,
        rotateClockwiseButtonHidden: true,
        aspectRatioPickerButtonHidden: true,
        aspectRatioLockEnabled: true,
      ),
    ],
  );
  return croppedImage;
}

Future<XFile> compressImageUntilBelow5MB(XFile imageFile,
    {int minQuality = 10}) async {
  final tempDir = await getTemporaryDirectory();
  var currentFile = imageFile;
  final maxSize = 5 * 1024 * 1024;

  final resizedPath =
      path.join(tempDir.path, 'resized_${path.basename(currentFile.path)}');
  final resizedFile = await FlutterImageCompress.compressAndGetFile(
    currentFile.path,
    resizedPath,
    minWidth: 1080,
    minHeight: 1080,
    format: CompressFormat.png,
    quality: 90,
  );

  if (resizedFile != null) {
    currentFile = resizedFile;
  }

  int quality = 80;
  int attempt = 0;
  while (await currentFile.length() > maxSize && quality >= minQuality) {
    attempt++;
    final destinationPath = path.join(
        tempDir.path, 'compressed_${attempt}_${path.basename(imageFile.path)}');

    final compressedResult = await FlutterImageCompress.compressAndGetFile(
      currentFile.path,
      destinationPath,
      format: CompressFormat.png,
      quality: quality,
    );

    if (compressedResult == null) {
      break;
    }

    currentFile = compressedResult;
    quality -= 15;
  }

  return currentFile;
}
