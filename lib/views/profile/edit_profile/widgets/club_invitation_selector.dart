// lib/features/profile/view/widgets/club_invitation_selector.dart

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:flutter/material.dart';

class ClubInvitationSelector extends StatelessWidget {
  final String? groupValue;
  final ValueChanged<String?> onChanged;

  const ClubInvitationSelector({
    super.key,
    required this.groupValue,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Open to club invitations ?',
          overflow: TextOverflow.ellipsis,
          style: lbRegular.copyWith(fontSize: 18),
        ),
        const SizedBox(height: 3),
        Text(
          '(Must be set to “Yes” to receive new club invitations)',
          overflow: TextOverflow.ellipsis,
          style: lbItalic.copyWith(fontSize: 14),
        ),
        const SizedBox(height: 10),
        Row(
          children: [
            _buildRadioOption('Yes'),
            const SizedBox(width: 25),
            _buildRadioOption('No'),
          ],
        ),
      ],
    );
  }

  Widget _buildRadioOption(String title) {
    return Row(
      children: [
        SizedBox(
          height: 24,
          width: 24,
          child: Radio<String>(
            fillColor: const WidgetStatePropertyAll(AppConstants.primaryColor),
            value: title,
            groupValue: groupValue,
            onChanged: onChanged,
          ),
        ),
        const SizedBox(width: 8),
        GestureDetector(
          onTap: () => onChanged(title),
          child: Text(
            title,
            style: lbRegular.copyWith(fontSize: 18),
          ),
        ),
      ],
    );
  }
}
