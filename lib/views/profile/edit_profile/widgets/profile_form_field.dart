// lib/features/profile/view/widgets/profile_form_field.dart

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:flutter/material.dart';

class ProfileFormField extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final bool isEnabled;
  final bool isReadOnly;
  final int? maxLength;
  final String? validationMessage;
  final ValueChanged<String>? onChanged;
  final String? Function(String?)? validator;
  final TextCapitalization textCapitalization;

  const ProfileFormField({
    super.key,
    required this.label,
    required this.controller,
    this.isEnabled = true,
    this.isReadOnly = false,
    this.maxLength,
    this.validationMessage,
    this.onChanged,
    this.validator,
    this.textCapitalization = TextCapitalization.none,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          overflow: TextOverflow.ellipsis,
          style: lbRegular.copyWith(fontSize: 18),
        ),
        const SizedBox(height: 10),
        TextFormField(
          controller: controller,
          enabled: isEnabled,
          readOnly: isReadOnly,
          maxLength: maxLength,
          maxLines: null,
          textCapitalization: textCapitalization,
          style: lbRegular.copyWith(fontSize: 18),
          decoration: InputDecoration(
            counterStyle: lbRegular.copyWith(
              fontSize: 14,
              color: AppConstants.primaryColor,
            ),
            suffixIcon: isReadOnly
                ? null
                : Image.asset(
                    'assets/icons/Edit.png',
                    height: 38,
                    width: 38,
                  ),
            filled: true,
            fillColor: const Color.fromRGBO(255, 255, 255, 1),
            contentPadding: const EdgeInsets.all(10),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
              borderSide: const BorderSide(
                color: AppConstants.primaryColor,
                width: 1.5,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
              borderSide: const BorderSide(
                color: AppConstants.primaryColor,
                width: 1.5,
              ),
            ),
          ),
          onChanged: onChanged,
          validator: validator,
        ),
        if (validationMessage != null && validationMessage!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              validationMessage!,
              style: lbBold.copyWith(
                fontSize: 14,
                color: AppConstants.redColor,
              ),
            ),
          ),
      ],
    );
  }
}
