// lib/features/profile/view/widgets/profile_image_editor.dart

import 'dart:io';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/reusableWidgets/cached_network_image.dart';
import 'package:flutter/material.dart';

class ProfileImageEditor extends StatelessWidget {
  final String imageUrl;
  final String? validationMessage;
  final bool? isPreview;
  final VoidCallback onTap;

  const ProfileImageEditor({
    super.key,
    required this.imageUrl,
    this.validationMessage,
    required this.onTap,
    this.isPreview = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListTile(
          contentPadding: const EdgeInsets.symmetric(
            vertical: 1.5,
            horizontal: 16,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.primaryColor,
              width: 1.5,
            ),
          ),
          dense: true,
          tileColor: Colors.transparent,
          title: Text(
            'Edit Profile Photo',
            overflow: TextOverflow.ellipsis,
            style: lbRegular.copyWith(fontSize: 18),
          ),
          leading: ClipRRect(
            // clipBehavior: ui.Clip.none,
            borderRadius: BorderRadius.circular(50),
            child: (isPreview ?? false)
                ? Image.file(
                    File(imageUrl),
                    height: 45,
                    width: 45,
                  )
                : CustomCachedNetworkImage(
                    errorImage: AppConstants.profileLogoImagePath,
                    height: 45,
                    width: 45,
                    imageUrl: imageUrl,
                  ),
          ),
          onTap: onTap,
        ),
        if (validationMessage != null && validationMessage!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(left: 0.0, top: 8.0),
            child: Text(
              validationMessage!,
              style: lbBold.copyWith(
                fontSize: 14,
                color: AppConstants.redColor,
              ),
            ),
          ),
      ],
    );
  }
}
