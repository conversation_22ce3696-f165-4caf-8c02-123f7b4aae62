import 'package:flutter/material.dart';

import '../../../../models/book_case_model.dart';
import '../../../../models/book_club_model.dart';
import '../../../../models/user_model.dart';

/// Centralized state management for ProfileScreen
/// Handles all data states, loading states, and pagination
class ProfileScreenState {
  // User data state
  int? loggedInUserId;
  String? userName;
  String? userLocation;
  String? userHandler;
  String? userBio;
  UserModel userModel = UserModel();
  bool isUserLoading = false;

  // Bookcase data state
  List<BookCaseModel>? currentBookCaseList = [];
  List<BookCaseModel>? topShelfList = [];
  List<BookCaseModel>? completedBooks = [];
  List<BookCaseModel>? toBeReadList = [];
  List<BookCaseModel>? bookCase;

  // Club data state
  List<BookClubModel>? standingBookClubList = [];
  List<BookClubModel>? impromptuBookClubList = [];

  // Loading states
  bool isLoading = false;
  bool currentReadLoading = false;
  bool topShelfLoading = false;
  bool toBeReadLoading = false;
  bool standingClubLoading = false;

  // Pagination state
  int offset = 0;
  int currentReadLimit = 10;
  int topShelfLimit = 10;
  int toBeReadLimit = 10;
  int currentReadCount = 0;
  int topShelfCount = 0;
  int toBeReadCount = 0;
  int standingClubCount = 0;

  // Scroll controllers
  final ScrollController currentReadScrollController = ScrollController();
  final ScrollController topShelfScrollController = ScrollController();
  final ScrollController standingClubScrollController = ScrollController();
  final ScrollController impromptuClubScrollController = ScrollController();
  final ScrollController toBeReadScrollController = ScrollController();

  ProfileScreenState();

  /// Reset all data to initial state
  void reset() {
    // Reset user data
    loggedInUserId = null;
    userName = null;
    userLocation = null;
    userHandler = null;
    userBio = null;
    userModel = UserModel();
    isUserLoading = false;

    // Reset bookcase data
    currentBookCaseList?.clear();
    topShelfList?.clear();
    completedBooks?.clear();
    toBeReadList?.clear();
    bookCase = null;

    // Reset club data
    standingBookClubList?.clear();
    impromptuBookClubList?.clear();

    // Reset loading states
    isLoading = false;
    currentReadLoading = false;
    topShelfLoading = false;
    toBeReadLoading = false;
    standingClubLoading = false;

    // Reset pagination
    offset = 0;
    currentReadLimit = 10;
    topShelfLimit = 10;
    toBeReadLimit = 10;
    currentReadCount = 0;
    topShelfCount = 0;
    toBeReadCount = 0;
    standingClubCount = 0;
  }

  /// Update user data
  void updateUserData({
    int? userId,
    String? name,
    String? location,
    String? handler,
    String? bio,
    UserModel? model,
  }) {
    if (userId != null) loggedInUserId = userId;
    if (name != null) userName = name;
    if (location != null) userLocation = location;
    if (handler != null) userHandler = handler;
    if (bio != null) userBio = bio;
    if (model != null) userModel = model;
  }

  /// Update bookcase data
  void updateBookcaseData({
    List<BookCaseModel>? currentReading,
    List<BookCaseModel>? topShelf,
    List<BookCaseModel>? completed,
    List<BookCaseModel>? toBeRead,
    List<BookCaseModel>? allBooks,
  }) {
    if (currentReading != null) currentBookCaseList = currentReading;
    if (topShelf != null) topShelfList = topShelf;
    if (completed != null) completedBooks = completed;
    if (toBeRead != null) toBeReadList = toBeRead;
    if (allBooks != null) bookCase = allBooks;
  }

  /// Update club data
  void updateClubData({
    List<BookClubModel>? standing,
    List<BookClubModel>? impromptu,
  }) {
    if (standing != null) standingBookClubList = standing;
    if (impromptu != null) impromptuBookClubList = impromptu;
  }

  /// Update loading states
  void updateLoadingStates({
    bool? general,
    bool? currentRead,
    bool? topShelf,
    bool? toBeRead,
    bool? standingClub,
    bool? user,
  }) {
    if (general != null) isLoading = general;
    if (currentRead != null) currentReadLoading = currentRead;
    if (topShelf != null) topShelfLoading = topShelf;
    if (toBeRead != null) toBeReadLoading = toBeRead;
    if (standingClub != null) standingClubLoading = standingClub;
    if (user != null) isUserLoading = user;
  }

  /// Update pagination counts
  void updateCounts({
    int? currentRead,
    int? topShelf,
    int? toBeRead,
    int? standingClub,
  }) {
    if (currentRead != null) currentReadCount = currentRead;
    if (topShelf != null) topShelfCount = topShelf;
    if (toBeRead != null) toBeReadCount = toBeRead;
    if (standingClub != null) standingClubCount = standingClub;
  }

  /// Check if more data can be loaded for current reading
  bool canLoadMoreCurrentReading() {
    return !currentReadLoading &&
        (currentBookCaseList?.length ?? 0) < currentReadCount;
  }

  /// Check if more data can be loaded for top shelf
  bool canLoadMoreTopShelf() {
    return !topShelfLoading && (topShelfList?.length ?? 0) < topShelfCount;
  }

  /// Check if more data can be loaded for to-be-read
  bool canLoadMoreToBeRead() {
    return !toBeReadLoading && (toBeReadList?.length ?? 0) < toBeReadCount;
  }

  /// Dispose all scroll controllers
  void dispose() {
    currentReadScrollController.dispose();
    topShelfScrollController.dispose();
    standingClubScrollController.dispose();
    impromptuClubScrollController.dispose();
    toBeReadScrollController.dispose();
  }
}

/// Loading state types for different sections
enum ProfileLoadingType {
  initial,
  user,
  currentReading,
  topShelf,
  toBeRead,
  standingClubs,
  impromptuClubs,
  pagination,
}

/// Data refresh types
enum ProfileRefreshType {
  all,
  user,
  bookcase,
  clubs,
  currentReading,
  topShelf,
  toBeRead,
  standingClubs,
  impromptuClubs,
}
