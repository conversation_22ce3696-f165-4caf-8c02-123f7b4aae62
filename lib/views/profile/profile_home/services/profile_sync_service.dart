import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';

/// Global service for synchronizing profile data across screens
/// Handles cross-screen state management and data consistency
class ProfileSyncService {
  static final ProfileSyncService _instance = ProfileSyncService._internal();
  factory ProfileSyncService() => _instance;
  ProfileSyncService._internal();

  // Stream controllers for cross-screen communication
  final StreamController<ProfileSyncEvent> _syncController =
      StreamController<ProfileSyncEvent>.broadcast();
  final StreamController<bool> _refreshController =
      StreamController<bool>.broadcast();

  // Getters for streams
  Stream<ProfileSyncEvent> get syncStream => _syncController.stream;
  Stream<bool> get refreshStream => _refreshController.stream;

  // Cache for profile data to avoid redundant API calls
  final Map<String, dynamic> _profileCache = {};
  DateTime? _lastCacheUpdate;
  static const Duration _cacheValidDuration = Duration(minutes: 5);

  /// Notify all listening screens about profile data changes
  void notifyProfileDataChanged(ProfileSyncEventType type,
      {Map<String, dynamic>? data}) {
    log('ProfileSyncService: Notifying profile data changed - $type');
    final event =
        ProfileSyncEvent(type: type, data: data, timestamp: DateTime.now());
    _syncController.add(event);
  }

  /// Request refresh of all profile screens
  void requestRefresh() {
    log('ProfileSyncService: Requesting refresh of all profile screens');
    _refreshController.add(true);
  }

  /// Cache profile data
  void cacheProfileData(String key, dynamic data) {
    _profileCache[key] = data;
    _lastCacheUpdate = DateTime.now();
    log('ProfileSyncService: Cached data for key: $key');
  }

  /// Get cached profile data
  T? getCachedData<T>(String key) {
    if (_lastCacheUpdate != null &&
        DateTime.now().difference(_lastCacheUpdate!) < _cacheValidDuration) {
      return _profileCache[key] as T?;
    }
    return null;
  }

  /// Clear cache
  void clearCache() {
    _profileCache.clear();
    _lastCacheUpdate = null;
    log('ProfileSyncService: Cache cleared');
  }

  /// Check if cache is valid
  bool get isCacheValid {
    return _lastCacheUpdate != null &&
        DateTime.now().difference(_lastCacheUpdate!) < _cacheValidDuration;
  }

  /// Handle profile update events
  void onProfileUpdated(Map<String, dynamic> profileData) {
    cacheProfileData('userProfile', profileData);
    notifyProfileDataChanged(
      ProfileSyncEventType.profileUpdated,
      data: profileData,
    );
  }

  /// Handle bookcase changes
  void onBookcaseChanged(String bookcaseType, List<dynamic> books) {
    cacheProfileData('bookcase_$bookcaseType', books);
    notifyProfileDataChanged(
      ProfileSyncEventType.bookcaseChanged,
      data: {
        'bookcaseType': bookcaseType,
        'books': books,
      },
    );
  }

  /// Handle currently reading updates
  void onCurrentlyReadingUpdated(List<dynamic> books) {
    cacheProfileData('currentlyReading', books);
    notifyProfileDataChanged(
      ProfileSyncEventType.currentReadingUpdated,
      data: {'books': books},
    );
  }

  /// Handle to-be-read updates
  void onToBeReadUpdated(List<dynamic> books) {
    cacheProfileData('toBeRead', books);
    notifyProfileDataChanged(
      ProfileSyncEventType.toBeReadUpdated,
      data: {'books': books},
    );
  }

  /// Handle completed books updates
  void onCompletedBooksUpdated(List<dynamic> books) {
    cacheProfileData('completedBooks', books);
    notifyProfileDataChanged(
      ProfileSyncEventType.completedBooksUpdated,
      data: {'books': books},
    );
  }

  /// Handle club membership changes
  void onClubMembershipChanged(String clubType, List<dynamic> clubs) {
    cacheProfileData('clubs_$clubType', clubs);
    notifyProfileDataChanged(
      ProfileSyncEventType.clubMembershipChanged,
      data: {
        'clubType': clubType,
        'clubs': clubs,
      },
    );
  }

  /// Dispose resources
  void dispose() {
    _syncController.close();
    _refreshController.close();
    _profileCache.clear();
  }
}

/// Event types for cross-screen synchronization
enum ProfileSyncEventType {
  profileUpdated,
  bookcaseChanged,
  currentReadingUpdated,
  toBeReadUpdated,
  completedBooksUpdated,
  clubMembershipChanged,
  generalRefresh,
}

/// Profile sync event model
class ProfileSyncEvent {
  final ProfileSyncEventType type;
  final Map<String, dynamic>? data;
  final DateTime timestamp;

  ProfileSyncEvent({
    required this.type,
    this.data,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'ProfileSyncEvent{type: $type, data: $data, timestamp: $timestamp}';
  }
}

/// Mixin for screens that need to listen to profile sync events
mixin ProfileSyncListener<T extends StatefulWidget> on State<T> {
  StreamSubscription<ProfileSyncEvent>? _syncSubscription;
  StreamSubscription<bool>? _refreshSubscription;

  /// Initialize sync listeners
  void initializeProfileSyncListeners() {
    _syncSubscription = ProfileSyncService().syncStream.listen(onProfileSyncEvent);
    _refreshSubscription =
        ProfileSyncService().refreshStream.listen(onRefreshRequested);
  }

  /// Handle profile sync events - override in implementing classes
  void onProfileSyncEvent(ProfileSyncEvent event) {
    log('ProfileSyncListener: Received sync event - ${event.type}');
  }

  /// Handle refresh requests - override in implementing classes
  void onRefreshRequested(bool shouldRefresh) {
    log('ProfileSyncListener: Received refresh request - $shouldRefresh');
  }

  /// Dispose sync listeners
  void disposeProfileSyncListeners() {
    _syncSubscription?.cancel();
    _refreshSubscription?.cancel();
  }
}
