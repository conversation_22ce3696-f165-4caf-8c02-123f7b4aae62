import 'dart:developer';

import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../../../app/core/constants.dart';
import '../../../../app/core/utils/text_style.dart';
import '../../../../controller/book_case_controller.dart';
import '../../../../controller/profile_controller.dart';
import '../../../../models/profile_model/edit_bookcase/listof_book_model.dart';
import '../../../../reusableWidgets/connection_error/network_aware_tap.dart';
import '../../../../reusableWidgets/customDialouge_with_message.dart';
import '../../../../reusableWidgets/paginated_book_typeahead.dart';
import '../../../../reusableWidgets/question_feedback_dialog.dart';
import '../../../../reusable_api_function/books_api_functions.dart';

class AddBookPopup extends StatefulWidget {
  final String title;
  final bool isCurrentlyReading;
  final int? loggedInUserId;
  final Future<bool> Function(bool isMove) onAddBook;
  final VoidCallback? onBookAdded;
  final Function(int?, int?, String?, String?)? onBookSelected;
  final Future<void> Function()? onMoveBook;

  const AddBookPopup({
    super.key,
    required this.title,
    required this.isCurrentlyReading,
    required this.loggedInUserId,
    required this.onAddBook,
    this.onBookAdded,
    this.onBookSelected,
    this.onMoveBook,
  });

  @override
  State<AddBookPopup> createState() => _AddBookPopupState();
}

class _AddBookPopupState extends State<AddBookPopup> {
  final TextEditingController bookController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  SuggestionsController<Books>? suggestionsController;
  BookCaseController? bookCaseController;

  int? bookId = 0;
  int? bookCaseId = 0;
  String? bookName;
  String? bookAuthor;
  bool isBookIdNotEmpty = false;
  bool isLoading = false;
  bool isMovableConflict = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    bookController.clear();
    isBookIdNotEmpty = false;
    bookId = 0;
    bookCaseId = 0;
    bookName = null;
    bookAuthor = null;

    suggestionsController?.dispose();
    suggestionsController = SuggestionsController();
  }

  @override
  void dispose() {
    suggestionsController?.dispose();
    bookController.dispose();
    bookCaseController?.updateTypeAheadFlag(false);
    super.dispose();
  }

  void _handleBookSelection(Books book) {
    setState(() {
      bookController.text = book.bookName.toString();
      bookId = book.bookId;
      bookCaseId = book.bookCaseId;
      bookName = book.bookName ?? '';
      bookAuthor = book.bookAuthor ?? '';
      isBookIdNotEmpty = true;
    });
    log('book id: ${book.bookId}');
    log('book case: ${book.bookCaseId}');
    log('book name: ${book.bookName}');
    log('book author: ${book.bookAuthor}');
  }

  void _showQuestionFeedBox() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const QuestionFeedbackDialog(isBookQuery: true);
      },
    );
  }

  Future<void> _handleAddBook() async {
    bool validation = _formKey.currentState!.validate();

    if (bookController.text.isEmpty) {
      setState(() {
        bookCaseController?.updateTypeAheadFlag(true);
      });
      return;
    }

    await bookCaseController?.updateTypeAheadFlag(false);

    if (isBookIdNotEmpty && validation) {
      try {
        widget.onBookSelected?.call(bookId, bookCaseId, bookName, bookAuthor);
        final alreadyExists = await widget.onAddBook(false);

        if (mounted) {
          context.pop();
        }

        if (alreadyExists) {
          // Check if the conflict is movable
          final errorMsg = bookCaseController?.addBookErrorMessage ?? '';
          final isMovable = (errorMsg.toLowerCase().contains('currently') ||
                  errorMsg.toLowerCase().contains('to-be-read') ||
                  errorMsg.toLowerCase().contains('all-books-read')) &&
              errorMsg.toLowerCase().contains('move it to');
          if (isMovable) {
            // Show Move dialog
            await _showMoveDialog(errorMsg);
          } else {
            await _showExistsDialog();
          }
        }

        widget.onBookAdded?.call();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error adding book: $e')),
          );
        }
      }
    } else {
      setState(() {
        isBookIdNotEmpty = true;
      });
    }
  }

  Future<void> _showExistsDialog() async {
    final responseMessage = bookCaseController?.addBookErrorMessage ?? '';
    await showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return CustomDialog(
          title: widget.title,
          message: responseMessage,
          showDoneImage: false,
          incomingClubFont: true,
        );
      },
    );
  }

  Future<void> _showMoveDialog(String message) async {
    await showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10, bottom: 20),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                _buildCloseButton(),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    children: [
                      Text(
                        widget.title,
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(fontSize: 18),
                      ),
                      const SizedBox(height: 25),
                      Text(
                        message,
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(fontSize: 12),
                      ),
                      const SizedBox(height: 25),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          NetworkAwareTap(
                            onTap: () async {
                              if (widget.onMoveBook != null) {
                                await widget.onMoveBook!();
                              }
                              context.pop();
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.textGreenColor,
                              ),
                              child: Center(
                                child: Text("Move",
                                    style: lbBold.copyWith(fontSize: 18)),
                              ),
                            ),
                          ),
                          NetworkAwareTap(
                            onTap: () => Navigator.of(context).pop(),
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.backgroundColor,
                                border: Border.all(
                                    color: AppConstants.primaryColor),
                              ),
                              child: Center(
                                child: Text(
                                  "Cancel",
                                  style: lbBold.copyWith(fontSize: 18),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                    ],
                  ),
                )
              ],
            )
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: AlertDialog(
        contentPadding: const EdgeInsets.only(right: 10),
        insetPadding: const EdgeInsets.all(20),
        actionsPadding: EdgeInsets.zero,
        backgroundColor: AppConstants.backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
          side: const BorderSide(
            color: AppConstants.popUpBorderColor,
            width: 1.5,
          ),
        ),
        surfaceTintColor: Colors.white,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCloseButton(),
            _buildTitle(),
            const SizedBox(height: 25),
            _buildTypeAheadField(),
            _buildValidationMessages(),
            const SizedBox(height: 25),
          ],
        ),
        actions: [
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildActionButtons(),
              const SizedBox(height: 30),
            ],
          )
        ],
      ),
    );
  }

  Widget _buildCloseButton() {
    return NetworkAwareTap(
      onTap: () {
        bookController.clear();
        suggestionsController?.dispose();
        bookCaseController?.updateTypeAheadFlag(false);
        context.pop();
      },
      child: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(top: 10),
        child: Image.asset(
          AppConstants.closePopupImagePath,
          height: 30,
          width: 30,
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Padding(
      padding: const EdgeInsets.only(left: 30.0, right: 20),
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        child: Text(
          widget.title,
          textAlign: TextAlign.center,
          style: lbRegular.copyWith(fontSize: 18),
        ),
      ),
    );
  }

  Widget _buildTypeAheadField() {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 20.0, left: 30),
          child: Consumer<ProfileController>(
            builder: (context, profileController, child) {
              return PaginatedBookTypeahead(
                suggestionsController: suggestionsController,
                controller: bookController,
                fetchBooksCallback: (query, offset, limit) =>
                    BooksApiFunctions.fetchBooks(
                  query,
                  offset,
                  limit,
                  context,
                  widget.loggedInUserId,
                ),
                onSelected: (book) => _handleBookSelection(book),
                onCantFindBook: () => _showQuestionFeedBox(),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildValidationMessages() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Consumer<BookCaseController>(
          builder: (context, bookCaseController, child) {
            return bookCaseController.isTypeAheadEmpty
                ? Padding(
                    padding: const EdgeInsets.only(left: 30.0),
                    child: Text(
                      "*Select book",
                      style: lbRegular.copyWith(
                        fontSize: 14,
                        color: AppConstants.redColor,
                      ),
                    ),
                  )
                : const SizedBox.shrink();
          },
        ),
        if (isBookIdNotEmpty && bookId == 0)
          Padding(
            padding: const EdgeInsets.only(left: 30.0),
            child: Text(
              "*Invalid book",
              style: lbRegular.copyWith(
                fontSize: 14,
                color: AppConstants.redColor,
              ),
            ),
          ),
        Consumer<BookCaseController>(
          builder: (context, bookCaseController, child) {
            return bookCaseController.isTypeAheadEmpty
                ? const SizedBox(height: 15)
                : const SizedBox.shrink();
          },
        ),
        if (isBookIdNotEmpty) const SizedBox(height: 15),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.only(left: 30.0, right: 20),
      child: Row(
        mainAxisAlignment: isLoading
            ? MainAxisAlignment.center
            : MainAxisAlignment.spaceBetween,
        children: [
          CustomLoaderButton(
            buttonWidth:
                isLoading ? 45.0 : MediaQuery.of(context).size.width / 3.2,
            buttonRadius: 30.0,
            buttonChild: isLoading
                ? const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation(Colors.white),
                    strokeWidth: 3.0,
                  )
                : Text(
                    'Add',
                    style: lbBold.copyWith(
                      fontSize: 18,
                      color: AppConstants.primaryColor,
                    ),
                  ),
            buttonPressed: () async {
              setState(() => isLoading = true);
              await _handleAddBook();
              setState(() => isLoading = false);
            },
          ),
          isLoading
              ? const SizedBox.shrink()
              : NetworkAwareTap(
                  onTap: () async {
                    await bookCaseController?.updateTypeAheadFlag(false);
                    if (mounted) context.pop();
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.backgroundColor,
                      border: Border.all(color: AppConstants.primaryColor),
                    ),
                    child: Center(
                      child: Text(
                        "Cancel",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(fontSize: 18),
                      ),
                    ),
                  ),
                ),
        ],
      ),
    );
  }
}
