import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/helpers/datetime_helper.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/models/book_case_model.dart';
import 'package:eljunto/models/profile_model/edit_bookcase/listof_book_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/paginated_book_typeahead.dart';
import 'package:eljunto/reusableWidgets/question_feedback_dialog.dart';
import 'package:eljunto/reusable_api_function/books_api_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:roundcheckbox/roundcheckbox.dart';

import '../constants/bookcase_constants.dart' as constants;

/// A dialog for adding a new book or editing an existing one in the user's bookcase.
class AddEditBookDialog extends StatefulWidget {
  final bool isEditMode;
  final BookCaseModel? book;
  final int loggedInUserId;
  final Future<String?> Function(BookCaseModel) onConfirmAdd;
  final Future<void> Function(BookCaseModel) onConfirmUpdate;
  final VoidCallback onDelete;

  const AddEditBookDialog({
    super.key,
    required this.isEditMode,
    this.book,
    required this.loggedInUserId,
    required this.onConfirmAdd,
    required this.onConfirmUpdate,
    required this.onDelete,
  });

  @override
  State<AddEditBookDialog> createState() => _AddEditBookDialogState();
}

class _AddEditBookDialogState extends State<AddEditBookDialog> {
  // Controllers
  final reviewController = TextEditingController();
  final monthyearController = TextEditingController();
  final bookController = TextEditingController();
  SuggestionsController<Books>? suggestionsController;

  // State variables
  final unknownController = ValueNotifier<bool>(true);
  final selectTopShelfController = ValueNotifier<bool>(false);
  bool bookValidation = false;
  bool monthyearValidation = false;
  bool ratingValidation = false;
  double ratingStar = 0;
  DateTime? selectedDate;
  String? bookName;
  String? bookAuthor;
  int? bookId;

  @override
  void initState() {
    super.initState();
    if (widget.isEditMode && widget.book != null) {
      _initializeForEditMode();
    }
  }

  void _initializeForEditMode() {
    final book = widget.book!;
    reviewController.text = book.review ?? '';
    ratingStar = book.ratings ?? 0.5;
    selectTopShelfController.value = book.topShelf ?? false;

    if (book.readingCompleteDate != null) {
      selectedDate =
          DateTime.fromMillisecondsSinceEpoch(book.readingCompleteDate!);
      monthyearController.text =
          DateFormat(constants.BookcaseConstants.monthYearFormat)
              .format(selectedDate!);
      unknownController.value = false;
    } else {
      selectedDate = null;
      monthyearController.clear();
      unknownController.value = true;
    }
  }

  @override
  void dispose() {
    reviewController.dispose();
    monthyearController.dispose();
    bookController.dispose();
    unknownController.dispose();
    selectTopShelfController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      actionsPadding: const EdgeInsets.only(right: 10),
      insetPadding: const EdgeInsets.all(20),
      contentPadding: EdgeInsets.symmetric(horizontal: 20),
      backgroundColor: AppConstants.backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
            constants.BookcaseConstants.dialogBorderRadius),
        side: const BorderSide(
          color: AppConstants.popUpBorderColor,
          width: constants.BookcaseConstants.dialogBorderWidth,
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 20),
            _buildSharedCloseButton(),
            _buildSharedTitle(widget.isEditMode ? "Edit Book" : "Add New Book"),
            _buildSharedContent(),
            _buildSharedActions(),
            const SizedBox(height: 30),
          ],
        ),
      ),
    );
  }

  Widget _buildSharedCloseButton() {
    return NetworkAwareTap(
      onTap: () => context.pop(),
      child: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(top: 10, right: 10),
        child: Image.asset(
          AppConstants.closePopupImagePath,
          height: 30,
          width: 30,
        ),
      ),
    );
  }

  Widget _buildSharedTitle(String title) {
    return Column(
      children: [
        SizedBox(
          width: MediaQuery.of(context).size.width,
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: lbRegular.copyWith(fontSize: 18),
          ),
        ),
        const SizedBox(height: 25),
      ],
    );
  }

  Widget _buildSharedContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.isEditMode)
          _buildBookInfoDisplay(widget.book!)
        else
          _buildBookTypeahead(),
        if (bookValidation)
          Text(
            "*Select book",
            style: lbRegular.copyWith(
              fontSize: 14,
              color: AppConstants.redColor,
            ),
          ),
        const SizedBox(height: 25),
        if (widget.isEditMode) ...[
          Text(
            'Completed: ',
            style: lbRegular.copyWith(fontSize: 12),
          ),
          const SizedBox(height: 15),
        ],
        _buildSharedDateSelection(),
        const SizedBox(height: 15),
        _buildSharedRatingSection(),
        const SizedBox(height: 15),
        _buildSharedReviewSection(),
        const SizedBox(height: 25),
      ],
    );
  }

  Widget _buildBookInfoDisplay(BookCaseModel book) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Book: ${book.bookName}, ${book.bookAuthor}",
          textAlign: TextAlign.center,
          style: lbRegular.copyWith(fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildBookTypeahead() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 10),
        PaginatedBookTypeahead(
          suggestionsController: suggestionsController,
          controller: bookController,
          fetchBooksCallback: (query, offset, limit) {
            setState(() => bookValidation = false);
            return BooksApiFunctions.fetchBooks(
              query,
              offset,
              limit,
              context,
              widget.loggedInUserId,
            );
          },
          onSelected: (book) => setState(
            () {
              bookController.text = book.bookName.toString();
              bookId = book.bookId;
              bookName = book.bookName ?? '';
              bookAuthor = book.bookAuthor ?? '';
              FocusManager.instance.primaryFocus?.unfocus();
            },
          ),
          onCantFindBook: () => _showQuestionFeedBox(),
        ),
      ],
    );
  }

  Widget _buildSharedDateSelection() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              constants.BookcaseConstants.selectCompletionDate,
              style: lbRegular.copyWith(fontSize: 12),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width / 2.8,
              child: TextFormField(
                controller: monthyearController,
                style: lbRegular.copyWith(fontSize: 12),
                decoration: InputDecoration(
                  suffixIcon:
                      const Icon(Icons.calendar_month_outlined, size: 20),
                  contentPadding: const EdgeInsets.all(10),
                  fillColor: const Color.fromRGBO(255, 255, 255, 1),
                  filled: true,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                    borderSide: const BorderSide(
                      color: AppConstants.primaryColor,
                      width: 1.5,
                    ),
                  ),
                ),
                onChanged: (value) =>
                    setState(() => monthyearValidation = false),
                onTap: () async {
                  selectedDate = await DateTimeHelper.getMonthYear(context);
                  setState(() {
                    if (selectedDate != null) {
                      monthyearValidation = false;
                      unknownController.value = false;
                      monthyearController.text = DateFormat(
                              constants.BookcaseConstants.monthYearFormat)
                          .format(selectedDate!);
                    } else {
                      monthyearController.clear();
                    }
                  });
                },
                readOnly: true,
              ),
            ),
            const SizedBox(width: 25),
            _buildSharedUnknownCheckbox(),
          ],
        ),
        if (monthyearValidation)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              constants.BookcaseConstants.selectDateValidation,
              style: lbRegular.copyWith(
                fontSize: 14,
                color: AppConstants.redColor,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSharedUnknownCheckbox() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          constants.BookcaseConstants.unknown,
          style: lbRegular.copyWith(fontSize: 12),
        ),
        const SizedBox(width: 10),
        RoundCheckBox(
          isChecked: unknownController.value,
          border: Border.all(color: Colors.transparent),
          onTap: (value) {
            setState(() {
              unknownController.value = value!;
              if (unknownController.value) {
                monthyearValidation = false;
                monthyearController.clear();
                selectedDate = null;
              }
            });
          },
          checkedWidget: const Icon(
            Icons.check_circle_outline_rounded,
            color: AppConstants.primaryColor,
          ),
          checkedColor: AppConstants.backgroundColor,
          uncheckedColor: AppConstants.backgroundColor,
          uncheckedWidget: const Icon(
            Icons.circle_outlined,
            color: AppConstants.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildSharedRatingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              constants.BookcaseConstants.ratings,
              style: lbRegular.copyWith(fontSize: 12),
            ),
            const SizedBox(width: 10),
            RatingBar(
              glow: false,
              itemCount: constants.BookcaseConstants.maxRating,
              itemSize: constants.BookcaseConstants.ratingItemSize.toDouble(),
              allowHalfRating: true,
              initialRating: ratingStar,
              minRating: constants.BookcaseConstants.minRating,
              unratedColor: Colors.red,
              ratingWidget: RatingWidget(
                full: const Icon(
                  Icons.star,
                  color: AppConstants.textGreenColor,
                ),
                half: const Icon(
                  Icons.star_half,
                  color: AppConstants.textGreenColor,
                ),
                empty: const Icon(
                  Icons.star_border_outlined,
                  color: AppConstants.textGreenColor,
                ),
              ),
              onRatingUpdate: (double value) {
                setState(() {
                  ratingValidation = false;
                  ratingStar = value;
                });
              },
            ),
          ],
        ),
        if (ratingValidation)
          Padding(
            padding: const EdgeInsets.only(left: 60),
            child: Text(
              constants.BookcaseConstants.ratingValidation,
              style: lbRegular.copyWith(
                fontSize: 14,
                color: AppConstants.redColor,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSharedReviewSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              constants.BookcaseConstants.reviewLabel,
              style: lbRegular.copyWith(fontSize: 12),
            ),
          ],
        ),
        const SizedBox(height: 10),
        TextFormField(
          controller: reviewController,
          textCapitalization: TextCapitalization.sentences,
          maxLines: constants.BookcaseConstants.reviewMaxLines,
          maxLength: constants.BookcaseConstants.reviewMaxLength,
          style: lbRegular.copyWith(fontSize: 12),
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.all(10),
            fillColor: const Color.fromRGBO(255, 255, 255, 1),
            filled: true,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
              borderSide: const BorderSide(
                color: AppConstants.primaryColor,
                width: 1.5,
              ),
            ),
            counterStyle: lbRegular.copyWith(fontSize: 14),
          ),
        ),
      ],
    );
  }

  Widget _buildSharedActions() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            NetworkAwareTap(
              onTap: _handleConfirm,
              child: Container(
                height: constants.BookcaseConstants.buttonHeight,
                width: MediaQuery.of(context).size.width / 3,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                      constants.BookcaseConstants.buttonBorderRadius),
                  color: AppConstants.textGreenColor,
                ),
                child: Center(
                  child: Text(
                    widget.isEditMode ? "Save" : "Add Book",
                    textAlign: TextAlign.center,
                    style: lbBold.copyWith(fontSize: 18),
                  ),
                ),
              ),
            ),
            NetworkAwareTap(
              onTap: () => context.pop(),
              child: Container(
                height: constants.BookcaseConstants.buttonHeight,
                width: MediaQuery.of(context).size.width / 3,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                      constants.BookcaseConstants.buttonBorderRadius),
                  color: AppConstants.backgroundColor,
                  border: Border.all(color: AppConstants.primaryColor),
                ),
                child: Center(
                  child: Text(
                    constants.BookcaseConstants.cancel,
                    textAlign: TextAlign.center,
                    style: lbBold.copyWith(fontSize: 18),
                  ),
                ),
              ),
            ),
          ],
        ),
        if (widget.isEditMode) ...[
          const SizedBox(height: 25),
          NetworkAwareTap(
            onTap: () {
              context.pop(); // Close this dialog first
              widget.onDelete(); // Then trigger the delete confirmation
            },
            child: Container(
              height: constants.BookcaseConstants.buttonHeight,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(
                    constants.BookcaseConstants.buttonBorderRadius),
                color: AppConstants.textGreenColor,
              ),
              child: Center(
                child: Text(
                  constants.BookcaseConstants.deleteBook,
                  textAlign: TextAlign.center,
                  style: lbBold.copyWith(fontSize: 18),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  bool _validateForm() {
    bool isValid = true;
    setState(() {
      monthyearValidation = false;
      ratingValidation = false;
    });

    if (!widget.isEditMode && bookController.text.isEmpty) {
      bookValidation = true;
      isValid = false;
    }

    if (monthyearController.text.isEmpty && !unknownController.value) {
      setState(() => monthyearValidation = true);
      isValid = false;
    }

    if (ratingStar == 0) {
      setState(() => ratingValidation = true);
      isValid = false;
    }

    return isValid;
  }

  Future<void> _handleConfirm() async {
    if (!_validateForm()) {
      return;
    }

    if (widget.isEditMode) {
      // --- UPDATE LOGIC ---
      final bookToUpdate = BookCaseModel(
        bookId: widget.book!.bookId,
        userId: widget.loggedInUserId,
        ratings: ratingStar,
        bookCaseId: widget.book!.bookCaseId,
        review: reviewController.text,
        topShelf: selectTopShelfController.value,
        reading_complete_date_String: selectedDate != null
            ? DateFormat(constants.BookcaseConstants.completeDateFormat)
                .format(selectedDate!)
            : '',
        reRead: widget.book!.reRead,
      );
      await widget.onConfirmUpdate(bookToUpdate);
      if (mounted) context.pop();
    } else {
      // --- ADD LOGIC ---
      final bookToAdd = BookCaseModel(
        userId: widget.loggedInUserId,
        bookId: bookId,
        bookAuthor: bookAuthor,
        bookName: bookName,
        is_currently_reading: false,
        ratings: ratingStar,
        review: reviewController.text,
        topShelf: selectTopShelfController.value,
        reading_complete_date_String: selectedDate != null
            ? DateFormat(constants.BookcaseConstants.completeDateFormat)
                .format(selectedDate!)
            : null,
        toBeRead: null,
      );

      final result = await widget.onConfirmAdd(bookToAdd);

      // Only pop if the book was added successfully (not if it exists)
      if (result != 'exist' && mounted) {
        final popResult = result;
        Future.delayed(Duration.zero, () => context.pop(popResult));
      } else if (result == 'exist' && mounted) {
        context.pop(result);
      }
    }
  }

  /// Show question feedback box
  void _showQuestionFeedBox() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const QuestionFeedbackDialog(isBookQuery: true);
      },
    );
  }
}
