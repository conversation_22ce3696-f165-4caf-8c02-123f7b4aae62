import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/models/book_case_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/views/profile/user_bookcase/constants/bookcase_constants.dart'
    as constants;
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:intl/intl.dart';

class BookCard extends StatelessWidget {
  final BookCaseModel book;
  final VoidCallback onEdit;
  final VoidCallback onReadReview;

  const BookCard({
    super.key,
    required this.book,
    required this.onEdit,
    required this.onReadReview,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 25),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildBookInfo(),
            _buildBookActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildBookInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          book.bookName ?? '',
          style: lbBold.copyWith(fontSize: 18),
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          book.bookAuthor ?? '',
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          style: lbRegular.copyWith(
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        if (book.ratings != null && book.ratings! > 0) ...[
          RatingBar(
            ignoreGestures: true,
            itemCount: 5,
            itemSize: 22,
            allowHalfRating: true,
            initialRating: book.ratings ?? 0,
            minRating: 0.5,
            unratedColor: Colors.red,
            ratingWidget: RatingWidget(
              full: const Icon(
                Icons.star,
                color: AppConstants.textGreenColor,
              ),
              half: const Icon(
                Icons.star_half,
                color: AppConstants.textGreenColor,
              ),
              empty: const Icon(
                Icons.star_border_outlined,
                color: AppConstants.textGreenColor,
              ),
            ),
            onRatingUpdate: (double value) {},
          ),
        ],
      ],
    );
  }

  Widget _buildBookActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        NetworkAwareTap(
          onTap: onReadReview,
          child: Text(
            'Read Review',
            style: lbItalic.copyWith(
              fontSize: 12,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
        const SizedBox(height: 5),
        NetworkAwareTap(
          onTap: onEdit,
          child: Text(
            'Edit',
            style: lbItalic.copyWith(
              fontSize: 12,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
        const SizedBox(height: 5),
        if (book.readingCompleteDate != null) ...[
          const SizedBox(height: 4),
          Text(
            'Completed: ${DateFormat(constants.BookcaseConstants.monthYearFormat).format(DateTime.fromMillisecondsSinceEpoch(book.readingCompleteDate!))}',
            style: lbRegular.copyWith(fontSize: 12),
          ),
        ] else ...[
          const SizedBox(height: 4),
          Text(
            'Completed: Unknown',
            style: lbRegular.copyWith(fontSize: 12),
          ),
        ],
      ],
    );
  }
}
