import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/views/profile/user_bookcase/constants/bookcase_constants.dart'
    as constants;
import 'package:flutter/material.dart';

class BookCardSkeleton extends StatelessWidget {
  const BookCardSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: constants.BookcaseConstants.cardHeight,
      margin: const EdgeInsets.only(top: 25),
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Loading...',
              style: lbBold.copyWith(fontSize: 14),
            ),
            const SizedBox(height: 4),
            Text(
              'Loading...',
              style: lbRegular.copyWith(fontSize: 12),
            ),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Edit',
                  style: lbItalic.copyWith(fontSize: 12),
                ),
                Text(
                  'Delete',
                  style: lbItalic.copyWith(fontSize: 12),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
