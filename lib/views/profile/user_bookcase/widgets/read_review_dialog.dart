import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// A dialog that displays a book's review.
class ReadReviewDialog extends StatelessWidget {
  final String? bookName;
  final String? bookAuthor;
  final String? review;

  const ReadReviewDialog({
    super.key,
    required this.bookName,
    required this.bookAuthor,
    required this.review,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      actionsPadding: const EdgeInsets.only(right: 10),
      insetPadding: const EdgeInsets.all(25),
      contentPadding: EdgeInsets.zero,
      backgroundColor: AppConstants.backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: const BorderSide(
          color: AppConstants.popUpBorderColor,
          width: 1.5,
        ),
      ),
      surfaceTintColor: Colors.white,
      actions: [
        Column(
          children: [
            _buildCloseButton(context),
            _buildTitle(context),
            _buildBookInfo(context),
            _buildReviewContent(),
            _buildOkButton(context),
            const SizedBox(height: 30),
          ],
        )
      ],
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return NetworkAwareTap(
      onTap: () => context.pop(),
      child: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(top: 10),
        child: Image.asset(
          AppConstants.closePopupImagePath,
          height: 30,
          width: 30,
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 30.0, right: 20),
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        child: Text(
          "Review:",
          textAlign: TextAlign.center,
          style: lbRegular.copyWith(fontSize: 18),
        ),
      ),
    );
  }

  Widget _buildBookInfo(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 25),
        Padding(
          padding: const EdgeInsets.only(left: 30.0, right: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  "$bookName, $bookAuthor",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(fontSize: 12),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 25),
      ],
    );
  }

  Widget _buildReviewContent() {
    return Padding(
      padding: const EdgeInsets.only(left: 30.0, right: 20),
      child: TextFormField(
        initialValue: review ?? '',
        textCapitalization: TextCapitalization.sentences,
        readOnly: true,
        maxLines: 4,
        maxLength: 2000,
        style: lbRegular.copyWith(fontSize: 12),
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.all(10),
          fillColor: AppConstants.backgroundColor,
          filled: true,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
            borderSide: const BorderSide(
              color: AppConstants.primaryColor,
              width: 1.5,
            ),
          ),
          counterStyle: lbRegular.copyWith(fontSize: 14),
          hintText: "No review",
          hintStyle: lbRegular.copyWith(fontSize: 12),
        ),
      ),
    );
  }

  Widget _buildOkButton(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 25),
        NetworkAwareTap(
          onTap: () {
            if (context.mounted) {
              context.pop();
            }
          },
          child: Container(
            height: 45,
            width: MediaQuery.of(context).size.width / 3.5,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(49),
              color: AppConstants.textGreenColor,
            ),
            child: Center(
              child: Text(
                "Ok",
                textAlign: TextAlign.center,
                style: lbBold.copyWith(fontSize: 18),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
