import 'dart:async';
import 'dart:developer';

import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/controller/session_manager.dart';
import 'package:eljunto/models/search_model/search_model.dart';
import 'package:eljunto/reusableWidgets/appbar.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../app/core/constants.dart';
import '../../app/core/services/diacritics_remover.dart';
import '../../app/core/services/setup_locator.dart';
import '../../app/core/utils/text_style.dart';
import '../../controller/search_controller.dart';
import '../../models/search_model/search_bookclub_model.dart';
import '../../models/search_model/who_into_this_author_model.dart';
import '../../models/search_model/who_into_this_book_model.dart';
import '../../reusableWidgets/cached_network_image.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class Data {
  final String? title;
  final String? subtitle;

  Data({
    this.title,
    this.subtitle,
  });
}

class Filter {
  final String? filterName;

  Filter({
    this.filterName,
  });
}

class _SearchScreenState extends State<SearchScreen> {
  TextEditingController searchController = TextEditingController();

  String image = ApiConstants.imageBaseUrl;
  bool isSearcheable = false;

  // final _diacriticsRemover = DiacriticsRemover();
  final _textCleaner = TextCleaner();

  // String? bookName;

  // List searchList = [];

  List<Filter> filterList = [
    // Filter(
    //   filterName: SearchOption.whoIntoThisBook,
    // ),
    Filter(
      filterName: SearchOption.profile.value,
    ),
    Filter(
      filterName: SearchOption.whoIntoThisAuthor.value,
    ),
    // Filter(
    //   filterName: SearchOption.whoReadingThis.value,
    // ),
    Filter(
      filterName: SearchOption.clubs.value,
    ),
    Filter(
      filterName: SearchOption.whoIntoThisBook.value,
    ),
  ];

  int optionSelectedIndex = 0;
  bool isSearching = false;
  int offSet = 0;
  int limit = 10;
  String selectedFilter = 'Profile';

  // SearchData? searchObj;
  List<Profile>? profileList = [];

  List<WhoReadingThisUser>? whoReadingThisUser = [];
  List<BookClub>? bookClubs = [];
  List<WhoReadThisUser>? whoReadThisUser = [];
  final ScrollController _scrollController = ScrollController();
  final ScrollController _clubScrollController = ScrollController();
  final ScrollController _whoAuthorScrollController = ScrollController();
  final ScrollController _whoBookScrollController = ScrollController();

  bool isLoading = false; // Prevent duplicate API calls
  SearchDataController? searchDataController;

  List<SearchClubs>? bookClubList = [];
  List<WhoIntoThisBookList>? whoIntoThisBookList = [];
  List<WhoIntoThisAuthorList>? whoIntoThisAuthorList = [];

  /// LIST OF BOOK AND AUTHOR
  List<Book>? booksList = [];
  List<BookAuthor>? booksAuthor = [];

  /// LOADING
  bool profileLoading = false;
  bool clubLoading = false;
  bool whoBookLoading = false;
  bool whoAuthorLoading = false;

  /// LIMIT
  int clubLimit = 10;
  int whoBookLimit = 10;
  int whoAuthorLimit = 10;

  Timer? _debounceTimer;
  final int _debounceTime = 500;

  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    searchDataController =
        Provider.of<SearchDataController>(context, listen: false);
    _scrollController.addListener(_onScroll);
    _clubScrollController.addListener(_fetchClubsOnScroll);
    _whoAuthorScrollController.addListener(_fetchAuthorsOnScroll);
    _whoBookScrollController.addListener(_fetchBooksOnScroll);
    _initializeUserId();
    super.initState();
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    searchController.dispose();
    _scrollController.dispose();
    _clubScrollController.dispose();
    _whoAuthorScrollController.dispose();
    _whoBookScrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _fetchProfile(searchController.text, true);
    }
  }

  /// FETCH CLUBS NAME
  void _fetchClubsOnScroll() {
    if (_clubScrollController.position.pixels ==
        _clubScrollController.position.maxScrollExtent) {
      _fetchClubs(searchController.text, true);
    }
  }

  /// WHO INTO THIS BOOK
  void _fetchBooksOnScroll() {
    if (_whoBookScrollController.position.pixels ==
        _whoBookScrollController.position.maxScrollExtent) {
      _fetchWhoIntoThisBook(searchController.text, true);
    }
  }

  /// WHO INTO THIS AUTHOR
  void _fetchAuthorsOnScroll() {
    if (_whoAuthorScrollController.position.pixels ==
        _whoAuthorScrollController.position.maxScrollExtent) {
      _fetchWhoIntoThisAuthor(searchController.text, true);
    }
  }

  int? loggedInUserId;

  Future<void> _initializeUserId() async {
    loggedInUserId = _sessionManager.userId;
  }

  Future<List<Profile>?> _fetchProfile(String query, bool isMore) async {
    if (isMore) {
      limit += 10;
    }

    await Provider.of<SearchDataController>(context, listen: false)
        .searchFunction(query, loggedInUserId ?? 0, offSet, limit, context)
        .then((searchData) {
      profileList = searchData?.profiles;
    });
    setState(() {});
    return profileList;
  }

  Future<List<SearchClubs>?> _fetchClubs(String query, bool isMore) async {
    if ((searchDataController?.bookClubList?.length ?? 0) <=
            (searchDataController?.bookClubCount ?? 0) ||
        !isMore) {
      clubLoading = true;
      if (isMore) {
        clubLimit += 10;
      }
    }

    await Provider.of<SearchDataController>(context, listen: false)
        .searchClubsFunction(
      query,
      loggedInUserId ?? 0,
      offSet,
      clubLimit,
      context,
    )
        .then((_) {
      bookClubList = searchDataController?.bookClubList;
      setState(() {});
    }).whenComplete(() {
      clubLoading = false;
    });
    log("ClubList : $bookClubList");
    return bookClubList;
  }

  Future<List<Book>?> _fetchWhoIntoThisBook(String query, bool isMore) async {
    if ((searchDataController?.whoIntoThisBookList?.length ?? 0) <=
            (searchDataController?.whoIntoThisBookCount ?? 0) ||
        !isMore) {
      whoBookLoading = true;

      if (isMore) {
        whoBookLimit += 10;
      }
    }

    log("Hereee In Book");
    final searchObj =
        await Provider.of<SearchDataController>(context, listen: false)
            .whoIntoThisBooks(
      query.trim(),
      loggedInUserId ?? 0,
      offSet,
      whoBookLimit,
      context,
    );
    if (searchObj != null) {
      return searchObj.first.whoIntoThisBooks?.books;
    } else {
      return [];
    }
  }

  Future<List<BookAuthor>?> _fetchWhoIntoThisAuthor(
      String query, bool isMore) async {
    if ((searchDataController?.whoIntoThisAuthorList?.length ?? 0) <=
            (searchDataController?.whoIntoThisAuthorCount ?? 0) ||
        !isMore) {
      whoAuthorLoading = true;

      if (isMore) {
        whoAuthorLimit += 10;
      }
    }
    log("Hereee In Author");
    final searchObj =
        await Provider.of<SearchDataController>(context, listen: false)
            .whoIntoThisAuthor(
      query.trim(),
      loggedInUserId ?? 0,
      offSet,
      limit,
      context,
    );
    if (searchObj != null) {
      return searchObj.first.whoIntoThisAuthor?.books;
    } else {
      return [];
    }
  }

  Future<void> searchFunction(String value) async {
    log("Selected FilterName : $selectedFilter");
    log('search term: $value');
    setState(() => isLoading = true);

    /// FETCH BOOKCLUBS

    try {
      switch (selectedFilter) {
        case "Profile":
          profileList = await _fetchProfile(value, false);
          // profileList = searchObj?.profiles;
          log('search user: ${profileList?[0].userName}');
          break;
        case "Clubs":
          bookClubList = await _fetchClubs(value, false);
          break;
        case "Who’s Into This Book?":
          booksList = await _fetchWhoIntoThisBook(value, false);
          log("Bookname : ${booksList?.length}");
          break;
        case "Who’s Into This Author?":
          booksAuthor = await _fetchWhoIntoThisAuthor(value, false);
          break;
      }
    } catch (e) {
      log("Error Search Screen: $e");
    } finally {
      setState(() => isLoading = false);
    }
    // setState(() => isLoading = false);
  }

  Future<void> selectedIndex(int index) async {
    log("SelectedIndex : $index");
    selectedFilter = filterList[index].filterName ?? '';
    log("Selected Filter : $selectedFilter");

    if (searchController.text.isNotEmpty) {
      final searchQuery = await _handleSuggestions(searchController.text);
      await searchFunction(searchQuery).then(
        (_) => setState(() {
          if ((profileList?.isEmpty ?? false) ||
              (bookClubList?.isEmpty ?? false) ||
              (booksAuthor?.isEmpty ?? false) ||
              (booksList?.isEmpty ?? false)) {
            isSearching = true;
          }
        }),
      );
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const AppBarWidget(
            appBarText: 'Search',
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                AppConstants.bgImagePath,
              ),
              filterQuality: FilterQuality.high,
              fit: BoxFit.cover,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: textFormField(),
              ),
              const SizedBox(
                height: 12,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: SizedBox(
                  height: 65,
                  child: MasonryGridView.builder(
                    gridDelegate:
                        const SliverSimpleGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                    ),
                    // physics: NeverScrollableScrollPhysics(),
                    scrollDirection: Axis.horizontal,
                    itemCount: filterList.length,
                    // crossAxisCount: 2,

                    mainAxisSpacing: 12,
                    crossAxisSpacing: 12,
                    itemBuilder: (context, index) {
                      return NetworkAwareTap(
                        onTap: () {
                          selectedIndex(index);
                        },
                        child: selectedOption(index),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(
                height: 12,
              ),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.0),
                child: Divider(
                  thickness: 1.5,
                  color: AppConstants.primaryColor,
                ),
              ),
              const SizedBox(
                height: 12,
              ),
              Expanded(
                child: Skeletonizer(
                  effect: const SoldColorEffect(
                    color: AppConstants.skeletonforgroundColor,
                    lowerBound: 0.1,
                    upperBound: 0.5,
                  ),
                  containersColor: AppConstants.skeletonBackgroundColor,
                  enabled: isLoading,
                  child: Column(
                    children: [
                      /// WHO'S INTO THIS BOOK LIST (1)
                      if (selectedFilter ==
                          SearchOption.whoIntoThisBook.value) ...[
                        (booksList != null &&
                                booksList!.isNotEmpty &&
                                booksList?.isNotEmpty == true)
                            ? Expanded(
                                child: Consumer<SearchDataController>(builder:
                                    (context, searchDataController, _) {
                                  return ListView.builder(
                                    controller: _whoBookScrollController,
                                    itemCount: whoBookLoading
                                        ? (booksList?.length ?? 0 + 1)
                                        : booksList?.length,
                                    itemBuilder: (context, index) {
                                      return Skeleton.replace(
                                        replacement: _bookSkeleton(index, true),
                                        child: _bookSkeleton(index, false),
                                      );
                                    },
                                  );
                                }),
                              )
                            : (isSearching)
                                ? const NoDataWidget(
                                    message: "No user is into this book",
                                  )
                                : const SizedBox.shrink(),
                      ],

                      /// WHO'S INTO THIS AUTHOR LIST
                      if (selectedFilter ==
                          SearchOption.whoIntoThisAuthor.value) ...[
                        (booksAuthor != null &&
                                booksAuthor!.isNotEmpty &&
                                booksAuthor?.isNotEmpty == true)
                            ? Expanded(
                                child: Consumer<SearchDataController>(
                                  builder: (context, searchDataController, _) {
                                    return ListView.builder(
                                      controller: _whoAuthorScrollController,
                                      itemCount: whoAuthorLoading
                                          ? (booksAuthor?.length ?? 0 + 1)
                                          : booksAuthor?.length,
                                      itemBuilder: (context, index) {
                                        return Skeleton.replace(
                                          replacement:
                                              _authorSkeleton(index, true),
                                          child: _authorSkeleton(index, false),
                                        );
                                      },
                                    );
                                  },
                                ),
                              )
                            : (isSearching)
                                ? const NoDataWidget(
                                    message: "No user is into this author",
                                  )
                                : const SizedBox.shrink(),
                      ],

                      ///PROFILE SEARCH
                      if (selectedFilter == SearchOption.profile.value) ...[
                        (profileList != null && profileList!.isNotEmpty)
                            ? Expanded(
                                child: ListView.builder(
                                  // padding: const EdgeInsets.only(top: 12),
                                  controller: _scrollController,
                                  itemCount: profileList?.length ?? 0,
                                  itemBuilder: (context, index) {
                                    optionSelectedIndex = index;
                                    String? userImage;
                                    if (selectedFilter ==
                                        SearchOption.profile.value) {
                                      userImage = image +
                                          (profileList?[index]
                                                  .userProfilePicture ??
                                              '');
                                    }

                                    return Skeleton.replace(
                                      replacement: _profileSkeleton(
                                          index, true, userImage ?? ''),
                                      child: _profileSkeleton(
                                          index, false, userImage ?? ''),
                                    );
                                  },
                                ),
                              )
                            : (isSearching)
                                ? const NoDataWidget(
                                    message:
                                        "There is no profile with this name or handle",
                                  )
                                : const SizedBox.shrink(),
                      ],

                      ///CLUBS LIST
                      if (selectedFilter == SearchOption.clubs.value) ...[
                        // (searchDataController?.bookClubList?.isNotEmpty ?? false)
                        (bookClubList != null && bookClubList!.isNotEmpty)
                            ? Expanded(
                                child: Consumer<SearchDataController>(builder:
                                    (context, searchDataController, child) {
                                  return ListView.builder(
                                    controller: _clubScrollController,
                                    itemCount: clubLoading
                                        ? (bookClubList?.length ?? 0 + 1)
                                        : bookClubList?.length ?? 0,
                                    itemBuilder: (context, index) {
                                      optionSelectedIndex = index;

                                      String? clubType;
                                      if (bookClubList?[index].bookClubType ==
                                          ClubType.impromptu.value) {
                                        clubType =
                                            bookClubList?[index].clubCount;
                                      } else {
                                        clubType = "Standing book club";
                                      }

                                      return Skeleton.replace(
                                        replacement: _clubSkeleton(
                                          index,
                                          true,
                                          clubType,
                                        ),
                                        child: _clubSkeleton(
                                          index,
                                          false,
                                          clubType,
                                        ),
                                      );
                                    },
                                  );
                                }),
                              )
                            : (isSearching)
                                ? const NoDataWidget(
                                    message: "There is no club with this name",
                                  )
                                : const SizedBox.shrink(),
                      ]
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget selectedOption(int index) {
    return Container(
      padding: const EdgeInsets.only(left: 5, right: 5),
      decoration: BoxDecoration(
        color: (selectedFilter == filterList[index].filterName)
            ? AppConstants.primaryColor
            : Colors.transparent,
        borderRadius: BorderRadius.circular(5),
        border: Border.all(
          color: AppConstants.primaryColor,
        ),
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          filterList[index].filterName ?? '',
          textAlign: TextAlign.left,
          style: lbRegular.copyWith(
            fontSize: 12,
            color: (selectedFilter == filterList[index].filterName)
                ? AppConstants.backgroundColor
                : AppConstants.primaryColor,
          ),
        ),
      ),
    );
  }

  Widget textFormField() {
    return TextFormField(
      controller: searchController,
      textCapitalization: TextCapitalization.sentences,
      style: lbRegular.copyWith(
        fontSize: 18,
      ),
      decoration: InputDecoration(
        suffixIcon: NetworkAwareTap(
          onTap: () {
            searchController.text.isEmpty
                ? const SizedBox.shrink()
                : searchController.clear();
            if (searchController.text.isEmpty) {
              setState(() {
                profileList = [];
                bookClubList = [];

                booksAuthor = [];
                booksList = [];
                isSearching = false;
              });
            }

            setState(() {});
          },
          child: Icon(
            searchController.text.isEmpty
                ? Icons.search_rounded
                : Icons.clear_rounded,
            size: 25,
            color: AppConstants.primaryColor,
          ),
        ),
        filled: true,
        fillColor: const Color.fromRGBO(255, 255, 255, 1),
        contentPadding: const EdgeInsets.all(10),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(
            color: AppConstants.primaryColor,
            width: 1.5,
          ),
        ),
        hintText: "Search",
        hintStyle: lbRegular.copyWith(
          fontSize: 18,
          color: AppConstants.hintTextColor,
        ),
      ),
      onChanged: (value) async {
        final search = await _handleSuggestions(value);
        // Cancel previous timer if it exists
        if (_debounceTimer?.isActive ?? false) {
          _debounceTimer!.cancel();
        }

        // Show skeleton loader immediately when typing starts
        if (value.isNotEmpty) {
          setState(() {
            isLoading = true;
          });
        }

        // Set new timer
        _debounceTimer = Timer(Duration(milliseconds: _debounceTime), () {
          if (value.isNotEmpty) {
            searchFunction(search);
            isSearching = true;
          } else {
            // setState(() {
            isSearching = false;
            isLoading = false;
            // Clear your lists here
            profileList = [];
            bookClubList = [];
            booksAuthor = [];
            booksList = [];
            // });
          }
          setState(() {});
        });
      },
    );
  }

  Future<String> _handleSuggestions(String pattern) async {
    final normalizedPattern = _textCleaner.removeAll(pattern.toLowerCase());
    log("Normalized Pattern: $normalizedPattern");
    return normalizedPattern;
  }

  Widget _profileSkeleton(int index, bool isBorder, String userImage) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12, left: 20, right: 20, top: 12),
      decoration: BoxDecoration(
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: isBorder
              ? AppConstants.skeletonBackgroundColor
              : AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: NetworkAwareTap(
        onTap: () {
          context.pushNamed(
            'club-member-profile',
            extra: {
              'userId': profileList?[index].userId,
              'userName': profileList?[index].userName,
            },
          );
        },
        child: ListTile(
          title: Text(
            profileList?[index].userName ?? '',
            style: lbBold.copyWith(
              fontSize: 18,
            ),
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: 5.0),
            child: Text(
              profileList?[index].userHandle ?? '',
              style: lbRegular.copyWith(
                fontSize: 14,
              ),
            ),
          ),
          trailing: (selectedFilter == SearchOption.profile.value)
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(45),
                  child: CustomCachedNetworkImage(
                    imageUrl: userImage,
                    width: 45,
                    height: 45,
                    errorImage: AppConstants.profileLogoImagePath,
                  ),
                )
              : const SizedBox.shrink(),
        ),
      ),
    );
  }

  Widget _clubSkeleton(int index, bool isBorder, String? clubType) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12, left: 20, right: 20, top: 12),
      decoration: BoxDecoration(
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: isBorder
              ? AppConstants.skeletonBackgroundColor
              : AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: NetworkAwareTap(
        onTap: () {
          context.pushNamed(
            'club-details',
            extra: {
              'bookClubId': bookClubList?[index].bookClubId,
              'bookClubName': bookClubList?[index].bookClubName,
              'impromptuCount': bookClubList?[index].clubCount,
            },
          );
        },
        child: ListTile(
          title: Text(
            bookClubList?[index].bookClubName ?? '',
            style: lbBold.copyWith(
              fontSize: 18,
            ),
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: 5.0),
            child: Text(
              clubType ?? '',
              style: lbRegular.copyWith(
                fontSize: 14,
              ),
            ),
          ),
          trailing: ClipRRect(
            borderRadius: BorderRadius.circular(45),
            child: Image.asset(
              (bookClubList?[index].totalVacancies ?? 0) > 0
                  ? AppConstants.clubOpeningLogoImagePath
                  : AppConstants.clubOpeningZero,
              height: 50,
              width: 50,
              fit: BoxFit.contain,
              filterQuality: FilterQuality.high,
            ),
          ),
          // : const SizedBox.shrink(),
        ),
      ),
    );
  }

  Widget _bookSkeleton(int index, bool isBorder) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12, left: 20, right: 20, top: 12),
      decoration: BoxDecoration(
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: isBorder
              ? AppConstants.skeletonBackgroundColor
              : AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: NetworkAwareTap(
        onTap: () {
          log("Book Id : ${booksList?[index].bookId}");

          /// NAVIGATION HANDLE CODE
          context.pushNamed(
            "SearchResult",
            extra: {
              "filterName": "Who’s into this book?",
              "bookIds": booksList?[index].bookId,
              "bookName": booksList?[index].bookNames,
              "bookAuthor": booksList?[index].bookAuthor,
            },
          );
        },
        child: ListTile(
          title: Text(
            booksList?[index].bookNames ?? '',
            style: lbBold.copyWith(
              fontSize: 18,
              color: AppConstants.primaryColor,
            ),
          ),
          subtitle: Text(
            booksList?[index].bookAuthor ?? '',
            style: lbRegular.copyWith(
              fontSize: 14,
              color: AppConstants.primaryColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _authorSkeleton(int index, bool isBorder) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12, left: 20, right: 20, top: 12),
      decoration: BoxDecoration(
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: isBorder
              ? AppConstants.skeletonBackgroundColor
              : AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: NetworkAwareTap(
        onTap: () {
          /// NAVIGATION HANDLE CODE
          context.pushNamed(
            "SearchResult",
            extra: {
              "filterName": "Who’s into this author?",
              "bookIds": booksAuthor?[index].bookIds,
              "bookAuthor": booksAuthor?[index].bookAuthor,
            },
          );
        },
        child: ListTile(
          title: Text(
            booksAuthor?[index].bookAuthor ?? '',
            style: lbBold.copyWith(
              fontSize: 18,
              color: AppConstants.primaryColor,
            ),
          ),
        ),
      ),
    );
  }
}
