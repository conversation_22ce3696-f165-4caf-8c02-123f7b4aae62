import 'dart:developer';

import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/controller/session_manager.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:eljunto/views/search/search_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../app/core/constants.dart';
import '../../app/core/utils/text_style.dart';
import '../../controller/search_controller.dart';
import '../../models/search_model/interested_model.dart';
import '../../models/search_model/what_clubs_into_this_model.dart';
import '../../models/search_model/who_read_this_model.dart';
import '../../models/search_model/who_reading_this_model.dart';
import '../../reusableWidgets/cached_network_image.dart';
import '../../reusableWidgets/previous_screen_appbar.dart';

class ShowClubScreen extends StatefulWidget {
  final List<int>? bookIds;
  final String? filterName;
  final String? bookName;
  final String? bookAuthor;
  const ShowClubScreen({
    super.key,
    this.bookIds,
    this.filterName,
    this.bookName,
    this.bookAuthor,
  });

  @override
  State<ShowClubScreen> createState() => _ShowClubScreenState();
}

class _ShowClubScreenState extends State<ShowClubScreen> {
  String userProfilePicture = '';
  String image = ApiConstants.imageBaseUrl;
  TextEditingController reviewController = TextEditingController();
  String bookTime = "";
  List<InterestedList>? interestedList;
  List<WhatClubsIntoThisList>? whatClubsIntoThisList;

  List<WhoReadingThisList>? whoReadingThisList;
  int limit = 10;
  int offSet = 0;
  bool isLoading = false;

  final ScrollController _whoReadScrollController = ScrollController();
  final ScrollController _whoReadingScrollController = ScrollController();
  final ScrollController _interestedScrollController = ScrollController();
  final ScrollController _whatClubsIntoThisScrollController =
      ScrollController();

  bool whoReadLoading = false;
  bool whoReadingLoading = false;
  bool interestedLoading = false;
  bool whatClubsIntoThisLoading = false;

  /// COUNT VARIABLES
  int whoReadCount = 0;
  int whoReadingCount = 0;
  int interestedCount = 0;
  int whatClubsIntoThisCount = 0;

  /// LIMIT VARIABLES
  int whoReadLimit = 10;
  int whoReadingLimit = 10;
  int interestedLimit = 10;
  int whatClubsIntoThisLimit = 10;

  List<Data> searchList = [
    Data(
      title: 'Moby Dick',
      subtitle: 'Herman Melville',
    ),
    Data(
      title: 'Of Time and the River',
      subtitle: 'Thomas Wolfe',
    ),
  ];
  int? loggedInUserId;
  SearchDataController? searchDataController;
  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    searchDataController =
        Provider.of<SearchDataController>(context, listen: false);
    print("List Of Book Id: ${widget.bookIds?[0]}");
    // print('type: ${widget.filterName}');
    _interestedScrollController.addListener(_interestedOnScroll);
    _whatClubsIntoThisScrollController.addListener(_whatClubsIntoThisOnScroll);
    _whoReadScrollController.addListener(_whoReadOnScroll);
    _whoReadingScrollController.addListener(_whoReadingOnScroll);

    _initializeUserId();
    super.initState();
  }

  @override
  void dispose() {
    reviewController.dispose();
    _whoReadScrollController.dispose();
    _whoReadingScrollController.dispose();
    _interestedScrollController.dispose();
    _whatClubsIntoThisScrollController.dispose();
    super.dispose();
  }

  void _whoReadingOnScroll() {
    if (_whoReadingScrollController.position.pixels >=
            _whoReadingScrollController.position.maxScrollExtent &&
        !whoReadingLoading &&
        (searchDataController?.whoReadingThisList?.length ?? 0) <
            (searchDataController?.whoReadingCount ?? 0)) {
      _fetchWhoReadingThisList(true);
    }
  }

  void _whoReadOnScroll() {
    if (_whoReadScrollController.position.pixels >=
            _whoReadScrollController.position.maxScrollExtent &&
        !whoReadLoading &&
        (searchDataController?.whoReadThisList?.length ?? 0) <
            (searchDataController?.whoReadThisCount ?? 0)) {
      _fetchWhoReadThisList(true);
    }
  }

  void _interestedOnScroll() {
    if (_interestedScrollController.position.pixels >=
            _interestedScrollController.position.maxScrollExtent &&
        !interestedLoading &&
        (searchDataController?.interestedList?.length ?? 0) <
            (searchDataController?.interestedCount ?? 0)) {
      _fetchInterestedList(true);
    }
  }

  /// CLUB READING THIS BOOK SCROLL FUNCTION
  void _whatClubsIntoThisOnScroll() {
    if (_whatClubsIntoThisScrollController.position.pixels >=
            _whatClubsIntoThisScrollController.position.maxScrollExtent &&
        !whatClubsIntoThisLoading &&
        (searchDataController?.whatClubsIntoThisList?.length ?? 0) <
            (searchDataController?.clubCount ?? 0)) {
      _fetchWhatClubsIntoThisList(true);
    }
  }

  Future<void> _initializeUserId() async {
    loggedInUserId = _sessionManager.userId;
    setState(() {
      isLoading = true;
    });
    Future.wait([
      _fetchInterestedList(false),
      _fetchWhatClubsIntoThisList(false),
      _fetchWhoReadThisList(false),
      _fetchWhoReadingThisList(false),
    ]).then((_) {
      setState(() {
        isLoading = false;
      });
    });
  }

  Future<void> getBookDetailsById() async {}

  Future<void> _fetchInterestedList(bool isMore) async {
    if ((searchDataController?.interestedList?.length ?? 0) <=
            (searchDataController?.interestedCount ?? 0) ||
        !isMore) {
      interestedLoading = true;

      if (isMore) {
        interestedLimit += 10;
      }
      // Increment the limit by 10 for the next load
    }
    try {
      await Provider.of<SearchDataController>(context, listen: false)
          .getInterestedList(loggedInUserId ?? 0, widget.bookIds,
              FilterOption.interested.value, interestedLimit, offSet, context)
          .then((_) {
        // interestedCount = searchDataController?.interestedCount ?? 0;
        // interestedList = searchDataController?.interestedList;
      }).whenComplete(() {
        interestedLoading = false;
      });
    } catch (e) {
      log(e.toString());
    }
    log("Interested List Length : ${searchDataController?.interestedList?.length}");
    // return searchObj;
  }

  List<WhoReadThisList>? whoReadThisList;
  Future<void> _fetchWhoReadThisList(bool isMore) async {
    if ((searchDataController?.whoReadThisList?.length ?? 0) <=
            (searchDataController?.whoReadThisCount ?? 0) ||
        !isMore) {
      whoReadLoading = true;

      if (isMore) {
        whoReadLimit += 10;
      }
      // Increment the limit by 10 for the next load
    }

    try {
      await Provider.of<SearchDataController>(context, listen: false)
          .getWhoReadThisList(loggedInUserId ?? 0, widget.bookIds,
              FilterOption.whoReadThis.value, whoReadLimit, offSet, context)
          .then((_) {
        // whoReadCount = searchDataController?.whoReadThisCount ?? 0;
        // whoReadThisList = searchDataController?.whoReadThisList;
      }).whenComplete(() {
        whoReadLoading = false;
      });
      log("WhoReadThis List Length : ${searchDataController?.whoReadThisList?.length}");
    } catch (e) {
      log(e.toString());
    }

    // return searchObj;
  }

  Future<void> _fetchWhoReadingThisList(bool isMore) async {
    if ((searchDataController?.whoReadingThisList?.length ?? 0) <=
            (searchDataController?.whoReadingCount ?? 0) ||
        !isMore) {
      whoReadingLoading = true;

      if (isMore) {
        whoReadingLimit += 10;
      }
      // Increment the limit by 10 for the next load
    }
    // if (isMore) {
    //   limit += 10;
    // }
    try {
      await Provider.of<SearchDataController>(context, listen: false)
          .getWhoReadingThisList(
              loggedInUserId ?? 0,
              widget.bookIds,
              FilterOption.whoReadingThis.value,
              whoReadingLimit,
              offSet,
              context)
          .then((_) {
        // whoReadingCount = searchDataController?.whoReadingCount ?? 0;
        // whoReadingThisList = searchDataController?.whoReadingThisList;
        // searchDataController?.notifyListeners();
      }).whenComplete(() {
        whoReadingLoading = false;
      });
      log("WhoReadingThis List Length : ${searchDataController?.whoReadingThisList?.length}");
      // return searchObj;
    } catch (e) {
      log(e.toString());
    }
  }

  Future<void> _fetchWhatClubsIntoThisList(bool isMore) async {
    if ((searchDataController?.whatClubsIntoThisList?.length ?? 0) <=
            (searchDataController?.clubCount ?? 0) ||
        !isMore) {
      whatClubsIntoThisLoading = true;
      setState(() {});

      if (isMore) {
        whatClubsIntoThisLimit += 10;
      }
      // Increment the limit by 10 for the next load
    }
    // if (isMore) {
    //   limit += 10;
    // }
    try {
      await Provider.of<SearchDataController>(context, listen: false)
          .getWhatClubsIntoThisList(
              loggedInUserId ?? 0,
              widget.bookIds,
              FilterOption.whatClubIntoThis.value,
              whatClubsIntoThisLimit,
              offSet,
              context)
          .then((_) {
        // whatClubsIntoThisCount = searchDataController?.clubCount ?? 0;
        // whatClubsIntoThisList = searchDataController?.whatClubsIntoThisList;
        // searchDataController?.notifyListeners();
        // setState(() {});
      }).whenComplete(() {
        whatClubsIntoThisLoading = false;
      });
      log("WhatClubsIntoThis List Length : ${searchDataController?.whatClubsIntoThisList?.length}");
    } catch (e) {
      log(e.toString());
    }

    // return searchObj;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: PreviousScreenAppBar(
            bookName: widget.filterName,
            isSetProfile: true,
            searchBookName: widget.bookName,
            searchAuthorName: widget.bookAuthor,
            showSearchResults: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          containersColor: AppConstants.skeletonBackgroundColor,
          enabled: isLoading,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 25,
                ),

                /// PEOPLE READING THIS BOOK

                Padding(
                  padding: const EdgeInsets.only(left: 20.0),
                  child: Text(
                    (widget.filterName == "Who’s into this book?")
                        ? "People reading this book"
                        : "People reading this author",
                    overflow: TextOverflow.ellipsis,
                    style: lbRegular.copyWith(
                      fontSize: 18,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                searchDataController?.whoReadingThisList?.isNotEmpty ?? false
                    ? SizedBox(
                        height: 65,
                        child: Consumer<SearchDataController>(
                            builder: (context, searchDataController, child) {
                          return ListView.builder(
                            controller: _whoReadingScrollController,
                            padding: const EdgeInsets.only(left: 10, right: 20),
                            // shrinkWrap: true,
                            scrollDirection: Axis.horizontal,
                            itemCount: whoReadingLoading
                                ? searchDataController
                                        .whoReadingThisList?.length ??
                                    0 + 1
                                : searchDataController
                                        .whoReadingThisList?.length ??
                                    0,
                            itemBuilder: (context, index) {
                              if (index ==
                                      searchDataController
                                          .whoReadingThisList?.length &&
                                  whoReadingLoading) {
                                return const Padding(
                                  padding: EdgeInsets.only(left: 10.0),
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                                );
                              }
                              var whoReadinglist = searchDataController
                                  .whoReadingThisList?[index];
                              final userImage = image +
                                  (whoReadinglist?.userProfilePicture ?? '');

                              return NetworkAwareTap(
                                onTap: () {
                                  context.pushNamed(
                                    'club-member-profile',
                                    extra: {
                                      'userId': whoReadinglist?.userId,
                                      'userName': whoReadinglist?.userName
                                    },
                                  );
                                },
                                child: Skeleton.replace(
                                  replacement: peopleReadSkeleton(
                                      false, whoReadinglist, userImage),
                                  child: peopleReadSkeleton(
                                      true, whoReadinglist, userImage),
                                ),
                              );
                            },
                          );
                        }),
                      )
                    : Skeleton.replace(
                        replacement: messageSkeleton(
                            "No one is currently reading this book"),
                        child: NoDataWidget(
                          message:
                              (widget.filterName == "Who’s into this book?")
                                  ? "No one is currently reading this book"
                                  : "No one is currently reading this author",
                        ),
                      ),

                const SizedBox(
                  height: 25,
                ),

                /// CLUBS READING THIS BOOK (WHAT CLUBS INTO THIS LIST)

                Padding(
                  padding: const EdgeInsets.only(left: 20.0),
                  child: Text(
                    widget.filterName == "Who’s into this book?"
                        ? "Clubs reading this book"
                        : "Clubs reading this author",
                    overflow: TextOverflow.ellipsis,
                    style: lbRegular.copyWith(
                      fontSize: 18,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                searchDataController?.whatClubsIntoThisList?.isNotEmpty ?? false
                    ? SizedBox(
                        height: 70,
                        child: Consumer<SearchDataController>(
                          builder: (context, searchDataController, child) {
                            return ListView.builder(
                              controller: _whatClubsIntoThisScrollController,
                              padding:
                                  const EdgeInsets.only(left: 10, right: 20),
                              // shrinkWrap: true,
                              scrollDirection: Axis.horizontal,
                              itemCount: whatClubsIntoThisLoading
                                  ? searchDataController
                                          .whatClubsIntoThisList?.length ??
                                      0 + 1
                                  : searchDataController
                                          .whatClubsIntoThisList?.length ??
                                      0,
                              itemBuilder: (context, index) {
                                if (index ==
                                        (searchDataController
                                                .whatClubsIntoThisList
                                                ?.length ??
                                            0) &&
                                    whatClubsIntoThisLoading) {
                                  return const Padding(
                                    padding: EdgeInsets.only(left: 10.0),
                                    child: Center(
                                      child: CircularProgressIndicator(
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                  );
                                }
                                var whatClubReadList = searchDataController
                                    .whatClubsIntoThisList?[index];
                                var clubType = '';
                                if (whatClubReadList?.bookClubType ==
                                    "STANDING") {
                                  clubType = "Standing book club";
                                } else {
                                  clubType = whatClubReadList?.clubCount ?? '';
                                }

                                return NetworkAwareTap(
                                  onTap: () {
                                    context.pushNamed(
                                      'club-details',
                                      // queryParameters: {
                                      //   'bookClubId': clubList[index].clubId,
                                      // },
                                      extra: {
                                        'bookClubId':
                                            whatClubReadList?.bookClubId,
                                        'bookClubName':
                                            whatClubReadList?.bookClubName,
                                        'impromptuCount':
                                            whatClubReadList?.clubCount,
                                      },
                                    );
                                  },
                                  child: Skeleton.replace(
                                    replacement: clubRadingSkeleton(
                                      false,
                                      whatClubReadList,
                                      clubType,
                                    ),
                                    child: clubRadingSkeleton(
                                      true,
                                      whatClubReadList,
                                      clubType,
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      )
                    : Skeleton.replace(
                        replacement:
                            messageSkeleton("No clubs are reading this book"),
                        child: NoDataWidget(
                          message:
                              (widget.filterName == "Who’s into this book?")
                                  ? "No clubs are reading this book"
                                  : "No clubs are reading this author",
                        ),
                      ),

                const SizedBox(
                  height: 25,
                ),

                /// PEOPLE WANT TO READ THIS BOOK (INTERESTED LIST)

                Padding(
                  padding: const EdgeInsets.only(left: 20.0),
                  child: Text(
                    widget.filterName == "Who’s into this book?"
                        ? "People that want to read this book"
                        : "People that want to read this author",
                    overflow: TextOverflow.ellipsis,
                    style: lbRegular.copyWith(
                      fontSize: 18,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),

                searchDataController?.interestedList?.isNotEmpty ?? false
                    ? SizedBox(
                        height: 65,
                        child: Consumer<SearchDataController>(
                            builder: (context, searchDataController, child) {
                          return ListView.builder(
                            padding: const EdgeInsets.only(left: 10, right: 20),
                            // shrinkWrap: true,
                            scrollDirection: Axis.horizontal,
                            itemCount: interestedLoading
                                ? (searchDataController
                                        .interestedList?.length ??
                                    0 + 1)
                                : searchDataController.interestedList?.length ??
                                    0,
                            itemBuilder: (context, index) {
                              if (index ==
                                      (searchDataController
                                              .interestedList?.length ??
                                          0) &&
                                  interestedLoading) {
                                return const Padding(
                                  padding: EdgeInsets.only(left: 10.0),
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                                );
                              }
                              var interestList =
                                  searchDataController.interestedList?[index];
                              var userImage = image +
                                  (interestList?.userProfilePicture ?? '');
                              return NetworkAwareTap(
                                onTap: () {
                                  context.pushNamed(
                                    'club-member-profile',
                                    extra: {
                                      'userId': interestList?.userId,
                                      'userName': interestList?.userName
                                    },
                                  );
                                },
                                child: Skeleton.replace(
                                  replacement: peopleWantReadSkeleton(
                                      false, interestList, userImage),
                                  child: peopleWantReadSkeleton(
                                      true, interestList, userImage),
                                ),
                              );
                            },
                          );
                        }),
                      )
                    : Skeleton.replace(
                        replacement: messageSkeleton(
                            "No one has this book in to-be-read"),
                        child: NoDataWidget(
                          message:
                              (widget.filterName == "Who’s into this book?")
                                  ? "No one has this book in to-be-read"
                                  : "No one has this author in to-be-read",
                        ),
                      ),
                const SizedBox(
                  height: 25,
                ),

                /// PEOPLE HAVE READ THIS BOOK (WHOREADTHISLIST)
                Padding(
                  padding: const EdgeInsets.only(left: 20.0),
                  child: Text(
                    widget.filterName == "Who’s into this book?"
                        ? "People that have read this book"
                        : "People that have read this author",
                    style: lbRegular.copyWith(
                      fontSize: 18,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),

                searchDataController?.whoReadThisList?.isNotEmpty ?? false
                    ? SizedBox(
                        height: 154,
                        child: Consumer<SearchDataController>(
                            builder: (context, searchDataController, child) {
                          return ListView.builder(
                            padding: const EdgeInsets.only(left: 10, right: 20),
                            // shrinkWrap: true,
                            scrollDirection: Axis.horizontal,
                            itemCount: whoReadLoading
                                ? (searchDataController
                                        .whoReadThisList?.length ??
                                    0 + 1)
                                : searchDataController
                                        .whoReadThisList?.length ??
                                    0,
                            itemBuilder: (context, index) {
                              if (index ==
                                      (searchDataController
                                              .whoReadThisList?.length ??
                                          0) &&
                                  whoReadLoading) {
                                return const Padding(
                                  padding: EdgeInsets.only(left: 10.0),
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                                );
                              }
                              final list =
                                  searchDataController.whoReadThisList?[index];
                              String completedBookTime = "";
                              if (list?.readingCompleteDate != null) {
                                completedBookTime =
                                    DateFormat('MMM yyyy').format(
                                  DateTime.fromMillisecondsSinceEpoch(
                                      list?.readingCompleteDate ?? 0),
                                );
                              } else {
                                completedBookTime = "Unknown";
                              }
                              final userImage =
                                  image + (list?.userProfilePicture ?? '');

                              final review = list?.review ?? '';
                              final bookAuthor = list?.bookAuthors ?? '';
                              final bookName = list?.bookNames ?? '';

                              return NetworkAwareTap(
                                onTap: () {
                                  context.pushNamed(
                                    'club-member-profile',
                                    extra: {
                                      'userId': list?.userId,
                                      'userName': list?.userName
                                    },
                                  );
                                },
                                child: Skeleton.replace(
                                  replacement: peopleHaveReadSkeleton(
                                      list,
                                      completedBookTime,
                                      userImage,
                                      false,
                                      review,
                                      bookAuthor,
                                      bookName),
                                  child: peopleHaveReadSkeleton(
                                      list,
                                      completedBookTime,
                                      userImage,
                                      true,
                                      review,
                                      bookAuthor,
                                      bookName),
                                ),
                              );
                            },
                          );
                        }),
                      )
                    : Skeleton.replace(
                        replacement:
                            messageSkeleton("No one has read this book yet!"),
                        child: NoDataWidget(
                          message: widget.filterName == "Who’s into this book?"
                              ? "No one has read this book yet!"
                              : "No one has read this author yet!",
                        ),
                      ),
                const SizedBox(
                  height: 25,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget peopleReadSkeleton(
      bool isBorder, WhoReadingThisList? whoReadinglist, String userImage) {
    return Container(
      // padding: const EdgeInsets.all(14),
      margin: const EdgeInsets.only(left: 10),
      // height: 63,
      width: MediaQuery.of(context).size.width / 1.5,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? Colors.transparent
            : AppConstants.skeletonBackgroundColor,
        border: Border.all(
          color: isBorder ? AppConstants.primaryColor : Colors.transparent,
          width: isBorder ? 1.5 : 0,
        ),
      ),
      child: ListTile(
        title: Text(
          whoReadinglist?.userName ?? "",
          overflow: TextOverflow.ellipsis,
          style: lbBold.copyWith(
            fontSize: 16,
            color: AppConstants.primaryColor,
          ),
        ),
        trailing: ClipRRect(
          borderRadius: BorderRadius.circular(49),
          child: CustomCachedNetworkImage(
            imageUrl: userImage,
            width: 45,
            height: 45,
            errorImage: AppConstants.profileLogoImagePath,
          ),
        ),
      ),
    );
  }

  Widget clubRadingSkeleton(
    bool isBorder,
    WhatClubsIntoThisList? whatClubReadList,
    String clubType,
  ) {
    return Container(
      // padding: const EdgeInsets.all(14),
      margin: const EdgeInsets.only(left: 10),
      // height: 63,
      width: MediaQuery.of(context).size.width / 1.5,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? Colors.transparent
            : AppConstants.skeletonBackgroundColor,
        border: Border.all(
          color: isBorder ? AppConstants.primaryColor : Colors.transparent,
          width: isBorder ? 1.5 : 0,
        ),
      ),
      child: ListTile(
        title: Text(
          whatClubReadList?.bookClubName ?? '',
          overflow: TextOverflow.ellipsis,
          style: lbBold.copyWith(
            fontSize: 16,
            color: AppConstants.primaryColor,
          ),
        ),
        subtitle: Text(
          clubType,
          overflow: TextOverflow.ellipsis,
          style: lbItalic.copyWith(
            fontSize: 14,
            color: AppConstants.primaryColor,
          ),
        ),
        trailing: ClipRRect(
          borderRadius: BorderRadius.circular(49),
          child: Image.asset(
            (whatClubReadList?.totalVacancies ?? 0) > 0
                ? AppConstants.clubOpeningLogoImagePath
                : AppConstants.clubOpeningZero,
            height: 50,
            width: 50,
            fit: BoxFit.cover,
            filterQuality: FilterQuality.high,
          ),
        ),
      ),
    );
  }

  Widget peopleWantReadSkeleton(
      bool isBorder, InterestedList? interestList, String userImage) {
    return Container(
      // padding: const EdgeInsets.all(14),
      margin: const EdgeInsets.only(left: 10),
      // height: 63,
      width: MediaQuery.of(context).size.width / 1.5,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? Colors.transparent
            : AppConstants.skeletonBackgroundColor,
        border: Border.all(
          color: isBorder ? AppConstants.primaryColor : Colors.transparent,
          width: isBorder ? 1.5 : 0,
        ),
      ),
      child: ListTile(
        title: Text(
          interestList?.userName ?? "",
          overflow: TextOverflow.ellipsis,
          style: lbBold.copyWith(
            fontSize: 16,
            color: AppConstants.primaryColor,
          ),
        ),
        trailing: ClipRRect(
          borderRadius: BorderRadius.circular(49),
          child: CustomCachedNetworkImage(
            imageUrl: userImage,
            width: 45,
            height: 45,
            errorImage: AppConstants.profileLogoImagePath,
          ),
        ),
      ),
    );
  }

  Widget peopleHaveReadSkeleton(
    WhoReadThisList? list,
    String completedBookTime,
    String userImage,
    isBorder,
    String review,
    String bookAuthor,
    String bookName,
  ) {
    return Container(
      padding: const EdgeInsets.all(14),
      margin: const EdgeInsets.only(left: 10),
      // height: 63,
      width: MediaQuery.of(context).size.width / 1.5,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? Colors.transparent
            : AppConstants.skeletonBackgroundColor,
        border: Border.all(
          color: isBorder ? AppConstants.primaryColor : Colors.transparent,
          width: isBorder ? 1.5 : 0,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                /// USER NAME
                Text(
                  list?.userName ?? "",
                  overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(
                    fontSize: 16,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(
                  height: 8,
                ),
                Text(
                  list?.bookNames ?? "",
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                    color: AppConstants.primaryColor,
                  ),
                ),

                const SizedBox(
                  height: 8,
                ),

                /// RATING
                RatingBar(
                  ignoreGestures: true,
                  itemCount: 5,
                  itemSize: 14,
                  allowHalfRating: true,
                  initialRating: list?.ratings ?? 0,
                  minRating: 0,
                  ratingWidget: RatingWidget(
                    full: const Icon(
                      Icons.star,
                      color: AppConstants.textGreenColor,
                    ),
                    half: const Icon(
                      Icons.star_half,
                      color: AppConstants.textGreenColor,
                    ),
                    empty: const Icon(
                      Icons.star_border_outlined,
                      color: AppConstants.textGreenColor,
                    ),
                  ),
                  onRatingUpdate: (double value) {},
                ),

                const SizedBox(
                  height: 8,
                ),

                /// READ REVIEW TEXT
                NetworkAwareTap(
                  onTap: () {
                    readReviewFunction(bookName, bookAuthor, review);
                  },
                  child: Text(
                    "Read Review",
                    overflow: TextOverflow.ellipsis,
                    style: lbItalic.copyWith(
                      decoration: TextDecoration.underline,
                      fontSize: 12,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),

                const SizedBox(
                  height: 8,
                ),

                /// COMPLETED DATE
                Text(
                  "Completed: $completedBookTime",
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ],
            ),
          ),

          /// USER IMAGE
          Column(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(49),
                child: CustomCachedNetworkImage(
                  imageUrl: userImage,
                  width: 45,
                  height: 45,
                  errorImage: AppConstants.profileLogoImagePath,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget messageSkeleton(String message) {
    return Container(
      padding: const EdgeInsets.only(left: 20),
      margin: const EdgeInsets.symmetric(horizontal: 20),
      height: 50,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: AppConstants.skeletonBackgroundColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          message,
          textAlign: TextAlign.start,
        ),
      ),
    );
  }

  Future<void> readReviewFunction(
      String? bookName, String? bookAuthor, String? review) async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        reviewController.clear();
        reviewController.text = review ?? '';
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "Review:",
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          "$bookName, $bookAuthor",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: TextFormField(
                    controller: reviewController,
                    readOnly: true,
                    maxLines: 4,
                    // minLines: 1,
                    // maxLength: 2000,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.all(10),
                      fillColor: AppConstants.backgroundColor,
                      filled: true,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5),
                        borderSide: const BorderSide(
                          color: AppConstants.primaryColor,
                          width: 1.5,
                        ),
                      ),
                      counterStyle: lbRegular.copyWith(
                        fontSize: 14,
                      ),
                      hintText: "No review",
                      hintStyle: lbRegular.copyWith(
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () async {
                    if (context.mounted) {
                      context.pop();
                    }
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }
}
