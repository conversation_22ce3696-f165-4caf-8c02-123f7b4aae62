import 'dart:developer';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/controller/session_manager.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:eljunto/reusableWidgets/custom_text_widget.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:pinput/pinput.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'provider/setting_provider.dart';

class ManageEmailPage extends StatefulWidget {
  const ManageEmailPage({super.key});

  @override
  State createState() => _ManageEmailPageState();
}

class _ManageEmailPageState extends State<ManageEmailPage> {
  final _emailFormKey = GlobalKey<FormState>();
  final _otpFormKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _otpController = TextEditingController();
  int? removeEmailIndex = 0;
  final initialEmailController = TextEditingController();
  bool isLoading = false;
  late SettingProvider provider;
  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    provider = Provider.of<SettingProvider>(context, listen: false);
    provider.initialize(context);
    _initializeUserEmail();
    super.initState();
  }

  Future<void> _initializeUserEmail() async {
    initialEmailController.text = _sessionManager.userEmail ?? '';
  }

  @override
  void dispose() {
    _emailController.dispose();
    _otpController.dispose();
    initialEmailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: 'Change Email',
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(25.0),
          child: Column(children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'Email',
                  style: lbRegular.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            SizedBox(
              child: TextFormField(
                controller: initialEmailController,
                style: lbRegular.copyWith(
                  fontSize: 18,
                ),
                readOnly: true,
                decoration: InputDecoration(
                  contentPadding: const EdgeInsets.all(10),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5.0),
                    borderSide: const BorderSide(
                      color: AppConstants.primaryColor,
                      width: 2.5,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white.withValues(alpha: 0.8),
                ),
              ),
            ),
            const SizedBox(height: 25),
            CustomButton(
              text: 'Change Email',
              onPressed: showAddEmailPopup,
            ),
          ]),
        ),
      ),
    );
  }

  void showAddEmailPopup() {
    provider.errorMessage = '';
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            actionsPadding: const EdgeInsets.only(right: 10),
            insetPadding: const EdgeInsets.symmetric(horizontal: 20),
            contentPadding: EdgeInsets.zero,
            backgroundColor: AppConstants.backgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: const BorderSide(
                color: AppConstants.popUpBorderColor,
                width: 1.5,
              ),
            ),
            surfaceTintColor: Colors.white,
            actions: [
              Form(
                key: _emailFormKey,
                child: Consumer<SettingProvider>(
                    builder: (context, settingProvider, child) {
                  return Column(
                    children: [
                      NetworkAwareTap(
                        onTap: () {
                          resetForm();
                          context.pop();
                        },
                        child: Container(
                          alignment: Alignment.centerRight,
                          padding: const EdgeInsets.only(top: 10),
                          child: Image.asset(
                            AppConstants.closePopupImagePath,
                            height: 30,
                            width: 30,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Center(
                          child: Text(
                            "Change Email",
                            textAlign: TextAlign.center,
                            style: lbRegular.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 30.0, right: 20),
                        child: TextFormField(
                          controller: _emailController,
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                          keyboardType: TextInputType.emailAddress,
                          decoration: InputDecoration(
                            contentPadding: const EdgeInsets.all(10),
                            border: const OutlineInputBorder(),
                            filled: true,
                            fillColor: Colors.white.withValues(alpha: 0.8),
                            errorStyle: errorMsg,
                          ),
                          onChanged: (value) {
                            if (value.isNotEmpty) {
                              settingProvider.errorMessage = '';
                            }
                            setState(() {});
                          },
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 30.0, right: 20),
                        child: Row(
                          children: [
                            settingProvider.errorMessage.isNotEmpty
                                ? Text(
                                    settingProvider.errorMessage,
                                    style: lbBold.copyWith(
                                      fontSize: 14,
                                      color: AppConstants.redColor,
                                    ),
                                  )
                                : const SizedBox.shrink(),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 30.0, right: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            NetworkAwareTap(
                              onTap: () {
                                settingProvider
                                    .sendOTP(_emailController.text.trim(),
                                        _emailFormKey, context)
                                    .then((value) {
                                  final token =
                                      settingProvider.otpVerificationToken;
                                  if (value) {
                                    if (context.mounted) {
                                      context.pop();
                                      showOTPDialogue(token);
                                    }
                                  } else {
                                    log("Value : $value");
                                  }
                                });
                              },
                              child: Container(
                                height: 45,
                                width: MediaQuery.of(context).size.width / 3,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(49),
                                  color: AppConstants.textGreenColor,
                                ),
                                child: Center(
                                  child: Text(
                                    "Submit",
                                    textAlign: TextAlign.center,
                                    style: lbRegular.copyWith(
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            NetworkAwareTap(
                              onTap: () {
                                resetForm();
                                context.pop();
                              },
                              child: Container(
                                height: 45,
                                width: MediaQuery.of(context).size.width / 3,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(49),
                                  color: AppConstants.backgroundColor,
                                  border: Border.all(
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    "Cancel",
                                    textAlign: TextAlign.center,
                                    style: lbRegular.copyWith(
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                    ],
                  );
                }),
              )
            ],
          );
        });
      },
    );
  }

  void showOTPDialogue(String token) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        final defaultPinTheme = PinTheme(
          height: 45,
          width: 48.03,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            color: Colors.white,
            border: Border.all(
              color: Colors.black38,
              width: 1,
            ),
          ),
        );

        final focusedPinTheme = PinTheme(
          height: 45,
          width: 48.03,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            color: Colors.white,
            border: Border.all(
              color: AppConstants.primaryColor,
              width: 1,
            ),
          ),
        );

        final submittedPinTheme = PinTheme(
          height: 45,
          width: 48.03,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            color: Colors.white,
            border: Border.all(
              color: Colors.black38,
              width: 1.5,
            ),
          ),
        );
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            actionsPadding: const EdgeInsets.only(right: 10),
            insetPadding: const EdgeInsets.all(20),
            contentPadding: EdgeInsets.zero,
            backgroundColor: AppConstants.backgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: const BorderSide(
                color: AppConstants.popUpBorderColor,
                width: 1.5,
              ),
            ),
            surfaceTintColor: Colors.white,
            actions: [
              Form(
                key: _otpFormKey,
                child: Consumer<SettingProvider>(
                    builder: (context, settingProvider, child) {
                  return Column(
                    children: [
                      NetworkAwareTap(
                        onTap: () {
                          resetForm();
                          context.pop();
                        },
                        child: Container(
                          alignment: Alignment.centerRight,
                          padding: const EdgeInsets.only(top: 10),
                          child: Image.asset(
                            AppConstants.closePopupImagePath,
                            height: 30,
                            width: 30,
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 30.0, right: 20),
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: Center(
                            child: Text(
                              "Confirm Email",
                              textAlign: TextAlign.center,
                              style: lbRegular.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 30.0, right: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Text(
                                "Enter code sent to: ${_emailController.text}",
                                textAlign: TextAlign.center,
                                style: lbRegular.copyWith(
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 30.0, right: 20),
                        child: Pinput(
                          autofocus: true,
                          length: 6,
                          keyboardType: TextInputType.number,
                          showCursor: true,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          controller: _otpController,
                          defaultPinTheme: defaultPinTheme,
                          focusedPinTheme: focusedPinTheme,
                          submittedPinTheme: submittedPinTheme,
                          enabled: settingProvider.isFieldEnabled,
                          onChanged: (value) {
                            if (value.isNotEmpty) {
                              settingProvider.errorMessage = '';
                            }
                            setState(() {});
                          },
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 30.0, right: 20),
                        child: Row(
                          children: [
                            settingProvider.errorMessage.isNotEmpty
                                ? Text(
                                    settingProvider.errorMessage,
                                    style: lbBold.copyWith(
                                      fontSize: 14,
                                      color: AppConstants.redColor,
                                    ),
                                  )
                                : const SizedBox.shrink(),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 30.0, right: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            NetworkAwareTap(
                              onTap: () {
                                settingProvider
                                    .verifyOtpApiFun(
                                  _emailController.text.trim(),
                                  _otpController.text,
                                  token,
                                  _otpFormKey,
                                  context,
                                )
                                    .then((value) {
                                  if (value) {
                                    if (context.mounted) {
                                      context.pop();
                                      submitOTP(token);
                                      resetForm();
                                    }
                                  }
                                });
                              },
                              child: Container(
                                height: 45,
                                width: MediaQuery.of(context).size.width / 3,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(49),
                                  color: AppConstants.textGreenColor,
                                ),
                                child: Center(
                                  child: Text(
                                    "Submit",
                                    textAlign: TextAlign.center,
                                    style: lbRegular.copyWith(
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            NetworkAwareTap(
                              onTap: () {
                                context.pop();
                                resetForm();
                              },
                              child: Container(
                                height: 45,
                                width: MediaQuery.of(context).size.width / 3,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(49),
                                  color: AppConstants.backgroundColor,
                                  border: Border.all(
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    "Cancel",
                                    textAlign: TextAlign.center,
                                    style: lbRegular.copyWith(
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                    ],
                  );
                }),
              )
            ],
          );
        });
      },
    );
  }

  Future<void> submitOTP(String currentToken) async {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Builder(builder: (context) {
              return Consumer<SettingProvider>(
                  builder: (context, settingProvider, child) {
                return Column(
                  children: [
                    NetworkAwareTap(
                      onTap: saveLocally,
                      child: Container(
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(top: 10),
                        child: Image.asset(
                          AppConstants.closePopupImagePath,
                          height: 30,
                          width: 30,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Text(
                              settingProvider.errorMessage,
                              textAlign: TextAlign.center,
                              style: lbRegular.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        NetworkAwareTap(
                          onTap: saveLocally,
                          child: Container(
                            height: 45,
                            width: MediaQuery.of(context).size.width / 3.5,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(49),
                              color: AppConstants.textGreenColor,
                            ),
                            child: Center(
                              child: Text(
                                "Ok",
                                textAlign: TextAlign.center,
                                style: lbBold.copyWith(fontSize: 18),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              });
            })
          ],
        );
      },
    );
  }

  Future<void> saveLocally() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    await pref.clear();
    log('Token expire');
    if (mounted) {
      context.goNamed('login');
    }
  }

  void resetForm() {
    provider.clearErrorMessage();
    _emailController.clear();
    _otpController.clear();
    setState(() {});
  }
}
