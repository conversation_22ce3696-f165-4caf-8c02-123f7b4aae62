import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:webview_flutter/webview_flutter.dart';

class PrivacyPolicyPage extends StatefulWidget {
  const PrivacyPolicyPage({super.key});

  @override
  State createState() => _PrivacyPolicyPageState();
}

class _PrivacyPolicyPageState extends State<PrivacyPolicyPage> {
  late WebViewController webViewController;
  @override
  void initState() {
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(AppConstants.privacyPolicyUrl);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        automaticallyImplyLeading: false,
        backgroundColor: AppConstants.textGreenColor,
        leading: null,
      ),
      body: Stack(
        clipBehavior: Clip.none,
        children: [
          WebViewWidget(controller: webViewController),
          Positioned(
            top: 20,
            child: Padding(
              padding: const EdgeInsets.only(left: 20.0, top: 10),
              child: NetworkAwareTap(
                onTap: () {
                  context.pop();
                },
                child: Image.asset(
                  width: 45,
                  height: 45,
                  "assets/icons/Back.png",
                  fit: BoxFit.contain,
                  filterQuality: FilterQuality.high,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
