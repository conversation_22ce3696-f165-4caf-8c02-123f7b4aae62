import 'dart:developer';

import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/controller/session_manager.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../controller/login_controller.dart';
import '../../../controller/profile_controller.dart';
import '../../../controller/user_controller.dart';
import '../../../models/profile_model/edit_profile/update_user_profile_model.dart';

class SettingProvider extends ChangeNotifier {
  late LoginController loginController;
  late ProfileController profileController;

  String _errorMessage = '';
  bool _isFieldEnabled = true;
  String _otpVerificationToken = '';
  bool _deleteLoading = false;

  bool get isFieldEnabled => _isFieldEnabled;

  String get errorMessage => _errorMessage;

  String get otpVerificationToken => _otpVerificationToken;

  bool get deleteLoading => _deleteLoading;
  final _sessionManager = locator<SessionManager>();

  set errorMessage(String message) {
    if (_errorMessage != message) {
      _errorMessage = message;
      notifyListeners();
    }
  }

  void clearErrorMessage() {
    _errorMessage = '';
    notifyListeners();
  }

  void initialize(BuildContext context) {
    loginController = Provider.of<LoginController>(context, listen: false);
    profileController = Provider.of<ProfileController>(context, listen: false);
    _errorMessage = '';
    _isFieldEnabled = true;
    _otpVerificationToken = '';
  }

  Future<bool> sendOTP(String userEmail, GlobalKey<FormState> emailFormKey,
      BuildContext context) async {
    bool validation = emailFormKey.currentState!.validate();
    if (validation) {
      if (userEmail.isEmpty) {
        _errorMessage = '*Enter your email';
        notifyListeners();
        return false;
      } else if (!EmailValidator.validate(userEmail)) {
        _errorMessage = '*Enter valid email';
        notifyListeners();
        return false;
      }
    }
    bool isMailChecked = false;
    try {
      _errorMessage = '';
      notifyListeners();
      await loginController
          .verifyEmail(userEmail.toLowerCase(), context)
          .then((responseMap) {
        log("Otp Response : $responseMap");
        if (responseMap["statusCode"] == 200) {
          final token = responseMap['data']['token'];
          _otpVerificationToken = token;
          isMailChecked = true;
          notifyListeners();
        } else {
          _errorMessage = responseMap['message'];
          isMailChecked = false;
          notifyListeners();
        }
      });
    } catch (e) {
      _errorMessage = "$e";
      isMailChecked = false;
      notifyListeners();
    }
    return isMailChecked;
  }

  Future<bool> verifyOtpApiFun(
      String userEmail,
      String otp,
      String currentToken,
      GlobalKey<FormState> otpFormKey,
      BuildContext context) async {
    String email = userEmail.trim().toLowerCase();

    bool validation = otpFormKey.currentState!.validate();
    if (validation && otp.isEmpty) {
      _errorMessage = '*Enter code';
      notifyListeners();
      return false;
    }

    bool showDoneImg = false;

    _isFieldEnabled = false;
    notifyListeners();

    await loginController
        .verifyOTP(email, currentToken, otp, false, context)
        .then((response) async {
      log("Verify otp response : $response");

      if (response["statusCode"] == 200) {
        // bool success = false;

        var userData = await getUserProfileUpdateModel(email);
        if (context.mounted) {
          await profileController
              .updateProfileFunction(userData, context)
              .then((value) {
            if (value) {
              _errorMessage = profileController.apiResponse;
              // "Email has been confirmed and added.";
              showDoneImg = true;
            } else {
              _errorMessage = profileController.apiResponse;
              // GenericMessages.somethingWrong;
              showDoneImg = false;
            }
          });
        }
      } else {
        _errorMessage = response['message'];
        showDoneImg = false;
      }
    });
    _isFieldEnabled = true;
    notifyListeners();
    return showDoneImg;
  }

  Future<bool> confirmDeleteAccount(int userId, BuildContext context) async {
    _deleteLoading = true;
    notifyListeners();
    bool value = await Provider.of<UserController>(context, listen: false)
        .deleteAccount(userId, context);
    _errorMessage =
        Provider.of<UserController>(context, listen: false).apiResponseMessage;
    _deleteLoading = false;
    notifyListeners();
    return value;
  }

  Future<UserProfileUpdateModel> getUserProfileUpdateModel(String email) async {
    int? userId = _sessionManager.userId;
    String? userName = _sessionManager.userName;
    String? userLocation = _sessionManager.userLocation;
    String? userBio = _sessionManager.userBio;
    var userData = UserProfileUpdateModel(
        userId: userId,
        userName: userName ?? '',
        userEmailId: email,
        userLocation: userLocation ?? '',
        userBio: userBio ?? '');
    return userData;
  }
}
