// tool/build.dart
import 'dart:io';

// --- Configuration ---
// Add your build types and flavor names here.
const List<String> validBuildTypes = ['apk', 'bundle'];
const List<String> validFlavors = ['dev', 'prod'];

/// A helper function to run shell commands and stream their output.
Future<void> runCommand(
  String command,
  List<String> arguments, {
  String? workingDirectory,
}) async {
  print(
    '⚙️ Running command: "$command ${arguments.join(' ')}"'
    '${workingDirectory != null ? ' in $workingDirectory' : ''}',
  );

  final process = await Process.start(
    command,
    arguments,
    workingDirectory: workingDirectory,
    runInShell: true, // Helps find commands like 'pod' in the system's PATH
  );

  // Stream output from the command
  await stdout.addStream(process.stdout);
  await stderr.addStream(process.stderr);

  final exitCode = await process.exitCode;
  if (exitCode != 0) {
    throw 'Command failed with exit code $exitCode';
  }
}

/// A helper function to ask the user a question and get a valid response.
String askQuestion(String question, List<String> options) {
  while (true) {
    print('\n$question');
    for (int i = 0; i < options.length; i++) {
      print('${i + 1}. ${options[i]}');
    }
    stdout.write('Enter your choice (1-${options.length}): ');

    final input = stdin.readLineSync();
    if (input == null) continue;

    try {
      final choice = int.parse(input);
      if (choice > 0 && choice <= options.length) {
        return options[choice - 1];
      }
    } catch (e) {
      // Input was not a number, fall through to the error message
    }

    print('❌ Invalid selection. Please try again.');
  }
}

Future<void> main() async {
  try {
    // --- Interactive Prompts ---
    final buildType = askQuestion('📦 Select build type:', validBuildTypes);
    final flavor = askQuestion('🌿 Select flavor:', validFlavors);
    final entryPoint = 'lib/main_$flavor.dart';

    print('\n🚀 Starting build process...');
    print('   - Type:   $buildType');
    print('   - Flavor: $flavor');
    print('   - Target: $entryPoint');
    print('----------------------------------------------------');

    // --- Core Flutter Commands ---
    await runCommand('flutter', ['clean']);
    await runCommand('flutter', ['pub', 'get']);

    // --- Platform-Specific Steps (macOS) ---
    if (Platform.isMacOS) {
      print('🍎 Running on macOS, updating CocoaPods...');
      // Using 'pod install --repo-update' is safer for CI/CD and scripts
      // than 'pod update', as it respects the Podfile.lock but updates the repo.
      await runCommand(
        'pod',
        ['install', '--repo-update'],
        workingDirectory: 'ios',
      );
      print('✅ CocoaPods updated successfully.');
      print('----------------------------------------------------');
    }

    // --- Final Build Command ---
    final buildArgs = [
      'build',
      buildType,
      '--flavor',
      flavor,
      '--target',
      entryPoint,
      '--release',
    ];

    // Add split-per-abi only for APK builds
    if (buildType == 'apk') {
      buildArgs.insert(2, '--split-per-abi');
    }

    await runCommand('flutter', buildArgs);

    print('\n----------------------------------------------------');
    print('🎉 Build process finished successfully!');

    // Provide final output location
    if (buildType == 'apk') {
      print('✅ Find your split APKs in: build/app/outputs/flutter-apk/');
    } else {
      print(
          '✅ Find your App Bundle in: build/app/outputs/bundle/${flavor}Release/');
    }
  } catch (e) {
    print('\n❌ BUILD FAILED: $e');
    exit(1); // Exit with a non-zero code to indicate failure
  }
}
